using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using System.Collections;
using System.Collections.Concurrent;

namespace Common.Helper;

/// <summary>
/// 配置监控服务
/// 提供配置文件的热更新支持和静态访问能力
/// </summary>
public sealed class ConfigurationMonitor
{
    private static readonly ConcurrentDictionary<string, object> _configCache = new();
    private readonly IConfiguration _configuration;
    private static ConfigurationMonitor? _instance;

    /// <summary>
    /// 获取ConfigurationMonitor实例
    /// </summary>
    public static ConfigurationMonitor Instance => _instance ?? throw new InvalidOperationException("ConfigurationMonitor未初始化");

    /// <summary>
    /// 初始化ConfigurationMonitor
    /// </summary>
    /// <param name="configuration">IConfiguration实例</param>
    public ConfigurationMonitor(IConfiguration configuration)
    {
        _configuration = configuration;
        _instance = this;

        // 注册配置变更事件
        ChangeToken.OnChange(
            () => _configuration.GetReloadToken(),
            OnConfigurationChanged);


    }

    /// <summary>
    /// 配置变更事件
    /// </summary>
    private void OnConfigurationChanged()
    {
        try
        {
            // 清除缓存
            _configCache.Clear();

            // 获取 BasisConfigDto 中定义的所有静态属性类型
            var configTypes = typeof(BasisConfigDto)
                .GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static)
                .Select(p => p.PropertyType)
                .ToList();

            // 清空所有配置类的静态属性
            foreach (var configType in configTypes)
            {
                ClearStaticConfig(configType);
            }

            // 加载新配置
            var config = _configuration.GetSection("BasisConfig").Get<BasisConfigDto>();
            if (config != null)
            {
                // 手动设置各个配置项
                WxSetting.AppId = _configuration.GetSection("BasisConfig:Wx:AppId").Value ?? "";
                WxSetting.AppSecret = _configuration.GetSection("BasisConfig:Wx:AppSecret").Value ?? "";

                JWTSetting.Sign = _configuration.GetSection("BasisConfig:Jwt:Sign").Value ?? "";
                if (int.TryParse(_configuration.GetSection("BasisConfig:Jwt:Exp").Value, out int exp))
                {
                    JWTSetting.Exp = exp;
                }
                JWTSetting.Issuer = _configuration.GetSection("BasisConfig:Jwt:Issuer").Value ?? "";
                JWTSetting.Audience = _configuration.GetSection("BasisConfig:Jwt:Audience").Value ?? "";

                var redisInstances = _configuration.GetSection("BasisConfig:Redis:Instances").Get<List<RedisInstance>>();
                if (redisInstances != null)
                {
                    RedisSetting.Instances.Clear();
                    RedisSetting.Instances.AddRange(redisInstances);
                }

                DataBaseConnectionStrings.MysqlConnectionString = _configuration.GetSection("BasisConfig:DataBaseConnectionStrings:MysqlConnectionString").Value ?? "";

                LogServiceSetting.Path = _configuration.GetSection("BasisConfig:LogService:Path").Value ?? "";

                CardpoenApiSetting.OpenApiKey = _configuration.GetSection("BasisConfig:CardpoenApi:OpenApiKey").Value ?? "";
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"配置更新失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 清空静态配置类的所有静态属性
    /// </summary>
    private static void ClearStaticConfig(Type configType)
    {
        var properties = configType.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
        foreach (var property in properties)
        {
            if (!property.CanWrite) continue;

            var propertyType = property.PropertyType;
            if (typeof(IList).IsAssignableFrom(propertyType))
            {
                // 对于集合类型，创建新的空集合
                var newList = Activator.CreateInstance(propertyType);
                property.SetValue(null, newList);
            }
            else if (propertyType == typeof(string))
            {
                // 字符串设置为空
                property.SetValue(null, string.Empty);
            }
            else if (propertyType.IsValueType)
            {
                // 值类型设置为默认值
                property.SetValue(null, Activator.CreateInstance(propertyType));
            }
            else
            {
                // 引用类型创建新实例
                property.SetValue(null, Activator.CreateInstance(propertyType));
            }
        }
    }




}