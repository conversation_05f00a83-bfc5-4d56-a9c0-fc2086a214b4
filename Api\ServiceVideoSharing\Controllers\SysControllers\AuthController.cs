using BLL.SysService;
using Entity.Dto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.SysControllers
{
    /// <summary>
    /// 认证控制器 - 处理管理员和员工认证相关的请求
    /// </summary>
    /// <remarks>
    /// 构造函数 - 依赖注入用户服务和日志服务
    /// </remarks>
    /// <param name="userService">用户服务实例</param>
    /// <param name="logService">日志服务实例</param>
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController(SysUserService userService, SysLogService logService) : BaseController
    {
        /// <summary>
        /// 用户服务接口
        /// </summary>
        private readonly SysUserService _userService = userService;


        /// <summary>
        /// 日志服务接口
        /// </summary>
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 用户登录接口
        /// </summary>
        /// <param name="loginRequest">登录请求DTO，包含用户名和密码</param>
        /// <returns>
        /// 返回登录响应结果:
        /// - 成功: 返回200状态码和用户Token信息
        /// - 失败: 返回500状态码和错误信息
        /// </returns>
        [HttpPost("login", Name = "Auth_Login")]
        [AllowAnonymous]
        public async Task<Result<SysLoginResponseDto>> Login([FromBody] SysLoginRequestDto loginRequest)
        {
            // 执行登录操作
            var loginResult = await _userService.LoginAsync(loginRequest);

            // 记录登录成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "认证管理",
                Operation = "用户登录",
                BusinessObject = "SysUser",
                ObjectId = loginResult.UserInfo.UserId,
                DetailedInfo = $"用户 {loginResult.UserInfo.Username} 登录成功",
                UserId = loginResult.UserInfo.UserId,
                Username = loginResult.UserInfo.Username,
                Ip = IP
            });

            // 登录成功，返回成功响应
            return Success(loginResult, "登录成功");

        }

        /// <summary>
        /// 用户登出接口
        /// </summary>
        /// <returns>登出结果</returns>
        [HttpPost("logout", Name = "Auth_Logout")]
        public Result Logout() => Success("登出成功");




        /// <summary>
        /// 获取当前登录用户信息接口
        /// </summary>
        /// <returns>
        /// 返回用户信息结果:
        /// - 成功: 返回200状态码和用户详细信息
        /// - 未授权: 返回401状态码
        /// - 失败: 返回500状态码和错误信息
        /// </returns>
        [HttpGet("userinfo", Name = "Auth_GetUserInfo")]
        [Permission]
        // JWT认证特性，确保接口受保护
        public async Task<Result<SysUserInfoDto>> GetUserInfo()
        => Success(await GetCurrentUserInfoAsync(_userService), "获取成功");


    }
}