using BLL.Common;
using Common.Autofac;
using Common.JWT;
using DAL.SysDAL;
using DAL.VideoDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;

namespace BLL.VideoService
{
    /// <summary>
    /// 仪表板数据汇总服务
    /// 整合各种统计数据，提供综合数据展示功能
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class DashboardService(
        UserBatchRecordService userBatchRecordService,
        OrderStatisticsService orderStatisticsService,
        TagStatisticsService tagStatisticsService,
        UserDAL userDAL,
        SysUserDAL sysUserDAL) : BasePermissionService(userDAL, sysUserDAL)
    {
        private readonly UserBatchRecordService _userBatchRecordService = userBatchRecordService;
        private readonly OrderStatisticsService _orderStatisticsService = orderStatisticsService;
        private readonly TagStatisticsService _tagStatisticsService = tagStatisticsService;

        /// <summary>
        /// 获取综合仪表板数据（带权限过滤）
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>综合仪表板数据</returns>
        public async Task<DashboardDto> GetDashboardDataAsync(DateTime? startDate = null, DateTime? endDate = null, UserInfo? currentUserInfo = null)
        {
            var start = startDate ?? DateTime.Today;
            var end = endDate ?? DateTime.Today.AddDays(1).AddSeconds(-1);

            // 验证权限
            if (currentUserInfo != null)
            {
                ValidateUserTypePermission(currentUserInfo, [1, 2, 3], "权限不足，无法查看仪表板数据");
            }

            // 串行获取各种统计数据（避免 DbContext 并发访问问题）
            var summary = await GetDashboardSummaryAsync(start, end, currentUserInfo);
            var tagStatistics = await _tagStatisticsService.GetTagStatisticsAsync(currentUserInfo);

            // 使用当前用户信息获取权限范围内的记录
            var userInfoForService = currentUserInfo != null
                ? new CurrentUserInfoDto { UserId = currentUserInfo.UserId, UserName = currentUserInfo.UserName, UserType = currentUserInfo.UserType }
                : new CurrentUserInfoDto { UserId = "1", UserName = "System", UserType = 1 };
            var allRecords = await _userBatchRecordService.GetRecordsByDateRangeAsync(start, end, userInfoForService);

            var courseStatistics = new CourseStatisticsDto
            {
                ViewerCount = allRecords.Select(r => r.UserId).Distinct().Count(),
                CompleteViewerCount = allRecords.Where(r => r.IsCompleted).Select(r => r.UserId).Distinct().Count(),
                TotalViews = allRecords.Count,
                TotalCompleteViews = allRecords.Count(r => r.IsCompleted),
                CompleteRate = allRecords.Count > 0 ? (decimal)allRecords.Count(r => r.IsCompleted) / allRecords.Count * 100 : 0,
                AverageViewDuration = 0 // 暂时设为0，因为UserBatchRecord没有观看时长字段
            };

            var answerStatistics = new AnswerStatisticsDto
            {
                AnswerUserCount = allRecords.Where(r => r.TotalQuestions > 0).Select(r => r.UserId).Distinct().Count(),
                CorrectUserCount = allRecords.Where(r => r.CorrectAnswers > 0).Select(r => r.UserId).Distinct().Count(),
                TotalAnswerCount = allRecords.Sum(r => r.TotalQuestions),
                CorrectAnswerCount = allRecords.Sum(r => r.CorrectAnswers),
                CorrectRate = allRecords.Sum(r => r.TotalQuestions) > 0 ? (decimal)allRecords.Sum(r => r.CorrectAnswers) / allRecords.Sum(r => r.TotalQuestions) * 100 : 0,
                AverageAnswerTime = 0 // 暂时设为0，因为没有答题时间字段
            };

            var rewardStatistics = new RewardStatisticsDto
            {
                // 简化实现，将所有红包归类为答题红包
                AnswerRewardCount = allRecords.Count(r => r.RewardAmount > 0),
                AnswerRewardAmount = allRecords.Sum(r => r.RewardAmount),
                ViewRewardCount = 0,
                ViewRewardAmount = 0,
                ShareRewardCount = 0,
                ShareRewardAmount = 0,
                TotalRewardCount = allRecords.Count(r => r.RewardAmount > 0),
                TotalRewardAmount = allRecords.Sum(r => r.RewardAmount),
                DistributedCount = allRecords.Count(r => r.RewardAmount > 0),
                DistributedAmount = allRecords.Sum(r => r.RewardAmount)
            };
            var orderStatistics = await _orderStatisticsService.GetOrderStatisticsAsync(start, end);

            return new DashboardDto
            {
                Summary = summary,
                TagStatistics = tagStatistics,
                CourseStatistics = courseStatistics,
                AnswerStatistics = answerStatistics,
                RewardStatistics = rewardStatistics,
                OrderStatistics = orderStatistics,
                StartDate = start,
                EndDate = end,
                UpdateTime = DateTime.Now
            };
        }

        /// <summary>
        /// 获取仪表板汇总数据（带权限过滤）
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>汇总数据</returns>
        public async Task<DashboardSummaryDto> GetDashboardSummaryAsync(DateTime startDate, DateTime endDate, UserInfo? currentUserInfo = null)
        {
            // 验证权限
            if (currentUserInfo != null)
            {
                ValidateUserTypePermission(currentUserInfo, [1, 2, 3], "权限不足，无法查看汇总数据");
            }

            // 使用当前用户信息获取权限范围内的记录
            var userInfoForService = currentUserInfo != null
                ? new CurrentUserInfoDto { UserId = currentUserInfo.UserId, UserName = currentUserInfo.UserName, UserType = currentUserInfo.UserType }
                : new CurrentUserInfoDto { UserId = "1", UserName = "System", UserType = 1 };

            // 获取所有历史记录来统计总用户数（基于观看记录）
            var allHistoryRecords = await _userBatchRecordService.GetRecordsByDateRangeAsync(DateTime.MinValue, DateTime.MaxValue, userInfoForService);

            // 获取指定时间范围的记录
            var periodRecords = await _userBatchRecordService.GetRecordsByDateRangeAsync(startDate, endDate, userInfoForService);

            // 获取今日观看记录
            var todayViewRecords = await _userBatchRecordService.GetRecordsByDateRangeAsync(DateTime.Today, DateTime.Today.AddDays(1), userInfoForService);

            // 统计今日新注册用户数（基于用户注册时间）
            int todayNewUsers;
            if (currentUserInfo != null)
            {
                // 根据用户权限获取可访问的用户ID列表
                var accessibleUserIds = await GetAccessibleUserIdsAsync(currentUserInfo);
                if (accessibleUserIds == null)
                {
                    // 超级管理员，可以查看所有用户
                    todayNewUsers = await _userDAL.GetUserCountByDateRangeAsync(DateTime.Today, DateTime.Today.AddDays(1));
                }
                else
                {
                    // 管理员或员工，只能查看权限范围内的用户
                    var todayUsers = await _userDAL.GetPagedListWithScopeAsync(new UserDAL.Queryable
                    {
                        StartTime = DateTime.Today,
                        EndTime = DateTime.Today.AddDays(1),
                        PageIndex = 1,
                        PageSize = int.MaxValue
                    }, accessibleUserIds);
                    todayNewUsers = todayUsers.TotalCount;
                }
            }
            else
            {
                // 未提供用户信息时，返回所有今日新注册用户数（向后兼容）
                todayNewUsers = await _userDAL.GetUserCountByDateRangeAsync(DateTime.Today, DateTime.Today.AddDays(1));
            }

            var userStats = new
            {
                TotalUsers = allHistoryRecords.Select(r => r.UserId).Distinct().Count(), // 所有历史用户数（基于观看记录）
                NewUsers = periodRecords.Select(r => r.UserId).Distinct().Count(), // 指定时间段的用户数（基于观看记录）
                TodayNewUsers = todayNewUsers, // 今日新注册用户数（基于用户注册时间）
                TodayViewers = todayViewRecords.Select(r => r.UserId).Distinct().Count() // 今日观看用户数（基于观看记录）
            };

            return new DashboardSummaryDto
            {
                TotalMembers = userStats.TotalUsers,
                TodayNewMembers = userStats.TodayNewUsers,
                TodayViewers = userStats.TodayViewers
            };
        }

        /// <summary>
        /// 获取课程统计数据（带权限过滤）
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>课程统计</returns>
        public async Task<CourseStatisticsDto> GetCourseStatisticsAsync(DateTime startDate, DateTime endDate, UserInfo? currentUserInfo = null)
        {
            // 验证权限
            if (currentUserInfo != null)
            {
                ValidateUserTypePermission(currentUserInfo, [1, 2, 3], "权限不足，无法查看课程统计");
            }

            // 使用当前用户信息获取权限范围内的记录
            var userInfoForService = currentUserInfo != null
                ? new CurrentUserInfoDto { UserId = currentUserInfo.UserId, UserName = currentUserInfo.UserName, UserType = currentUserInfo.UserType }
                : new CurrentUserInfoDto { UserId = "1", UserName = "System", UserType = 1 };

            var records = await _userBatchRecordService.GetRecordsByDateRangeAsync(startDate, endDate, userInfoForService);
            return new CourseStatisticsDto
            {
                ViewerCount = records.Select(r => r.UserId).Distinct().Count(),
                CompleteViewerCount = records.Where(r => r.IsCompleted).Select(r => r.UserId).Distinct().Count(),
                TotalViews = records.Count,
                TotalCompleteViews = records.Count(r => r.IsCompleted),
                CompleteRate = records.Count > 0 ? (decimal)records.Count(r => r.IsCompleted) / records.Count * 100 : 0,
                AverageViewDuration = 0
            };
        }

        /// <summary>
        /// 获取答题统计数据（带权限过滤）
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>答题统计</returns>
        public async Task<AnswerStatisticsDto> GetAnswerStatisticsAsync(DateTime startDate, DateTime endDate, UserInfo? currentUserInfo = null)
        {
            // 验证权限
            if (currentUserInfo != null)
            {
                ValidateUserTypePermission(currentUserInfo, [1, 2, 3], "权限不足，无法查看答题统计");
            }

            // 使用当前用户信息获取权限范围内的记录
            var userInfoForService = currentUserInfo != null
                ? new CurrentUserInfoDto { UserId = currentUserInfo.UserId, UserName = currentUserInfo.UserName, UserType = currentUserInfo.UserType }
                : new CurrentUserInfoDto { UserId = "1", UserName = "System", UserType = 1 };

            var records = await _userBatchRecordService.GetRecordsByDateRangeAsync(startDate, endDate, userInfoForService);
            return new AnswerStatisticsDto
            {
                AnswerUserCount = records.Where(r => r.TotalQuestions > 0).Select(r => r.UserId).Distinct().Count(),
                CorrectUserCount = records.Where(r => r.CorrectAnswers > 0).Select(r => r.UserId).Distinct().Count(),
                TotalAnswerCount = records.Sum(r => r.TotalQuestions),
                CorrectAnswerCount = records.Sum(r => r.CorrectAnswers),
                CorrectRate = records.Sum(r => r.TotalQuestions) > 0 ? (decimal)records.Sum(r => r.CorrectAnswers) / records.Sum(r => r.TotalQuestions) * 100 : 0,
                AverageAnswerTime = 0
            };
        }

        /// <summary>
        /// 获取红包统计数据（带权限过滤）
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>红包统计</returns>
        public async Task<RewardStatisticsDto> GetRewardStatisticsAsync(DateTime startDate, DateTime endDate, UserInfo? currentUserInfo = null)
        {
            // 验证权限
            if (currentUserInfo != null)
            {
                ValidateUserTypePermission(currentUserInfo, [1, 2, 3], "权限不足，无法查看红包统计");
            }

            // 使用当前用户信息获取权限范围内的记录
            var userInfoForService = currentUserInfo != null
                ? new CurrentUserInfoDto { UserId = currentUserInfo.UserId, UserName = currentUserInfo.UserName, UserType = currentUserInfo.UserType }
                : new CurrentUserInfoDto { UserId = "1", UserName = "System", UserType = 1 };

            var records = await _userBatchRecordService.GetRecordsByDateRangeAsync(startDate, endDate, userInfoForService);
            return new RewardStatisticsDto
            {
                AnswerRewardCount = records.Count(r => r.RewardAmount > 0),
                AnswerRewardAmount = records.Sum(r => r.RewardAmount),
                ViewRewardCount = 0,
                ViewRewardAmount = 0,
                ShareRewardCount = 0,
                ShareRewardAmount = 0,
                TotalRewardCount = records.Count(r => r.RewardAmount > 0),
                TotalRewardAmount = records.Sum(r => r.RewardAmount),
                DistributedCount = records.Count(r => r.RewardAmount > 0),
                DistributedAmount = records.Sum(r => r.RewardAmount)
            };
        }

        /// <summary>
        /// 获取标签统计数据（带权限过滤）
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>标签统计</returns>
        public async Task<List<TagStatisticsDto>> GetTagStatisticsAsync(UserInfo? currentUserInfo = null)
        {
            return await _tagStatisticsService.GetTagStatisticsAsync(currentUserInfo);
        }

        /// <summary>
        /// 获取订单统计数据（带权限过滤）
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>订单统计</returns>
        public async Task<OrderStatisticsDto> GetOrderStatisticsAsync(DateTime startDate, DateTime endDate, UserInfo? currentUserInfo = null)
        {
            // 验证权限
            if (currentUserInfo != null)
            {
                ValidateUserTypePermission(currentUserInfo, [1, 2, 3], "权限不足，无法查看订单统计");
            }

            return await _orderStatisticsService.GetOrderStatisticsAsync(startDate, endDate);
        }

        /// <summary>
        /// 获取今日数据快照（带权限过滤）
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>今日数据快照</returns>
        public async Task<DashboardDto> GetTodaySnapshotAsync(UserInfo? currentUserInfo = null)
        {
            var today = DateTime.Today;
            var todayEnd = today.AddDays(1).AddSeconds(-1);
            return await GetDashboardDataAsync(today, todayEnd, currentUserInfo);
        }

        /// <summary>
        /// 获取本月数据快照（带权限过滤）
        /// </summary>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>本月数据快照</returns>
        public async Task<DashboardDto> GetThisMonthSnapshotAsync(UserInfo? currentUserInfo = null)
        {
            var thisMonthStart = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            var thisMonthEnd = thisMonthStart.AddMonths(1).AddSeconds(-1);
            return await GetDashboardDataAsync(thisMonthStart, thisMonthEnd, currentUserInfo);
        }

        /// <summary>
        /// 获取数据对比（今日vs昨日）
        /// </summary>
        /// <returns>数据对比</returns>
        public async Task<(DashboardDto Today, DashboardDto Yesterday)> GetTodayVsYesterdayAsync()
        {
            var today = DateTime.Today;
            var todayEnd = today.AddDays(1).AddSeconds(-1);
            var yesterday = today.AddDays(-1);
            var yesterdayEnd = today.AddSeconds(-1);

            // 串行执行避免 DbContext 并发访问问题
            var todayData = await GetDashboardDataAsync(today, todayEnd);
            var yesterdayData = await GetDashboardDataAsync(yesterday, yesterdayEnd);

            return (todayData, yesterdayData);
        }

        /// <summary>
        /// 获取关键指标摘要（带权限过滤）
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>关键指标摘要</returns>
        public async Task<KeyMetricsSummaryDto> GetKeyMetricsSummaryAsync(DateTime? startDate = null, DateTime? endDate = null, UserInfo? currentUserInfo = null)
        {
            var dashboard = await GetDashboardDataAsync(startDate, endDate, currentUserInfo);

            return new KeyMetricsSummaryDto
            {
                TotalMembers = dashboard.Summary.TotalMembers,
                TodayNewMembers = dashboard.Summary.TodayNewMembers,
                TodayViewers = dashboard.Summary.TodayViewers
            };
        }
    }

    /// <summary>
    /// 关键指标摘要DTO
    /// </summary>
    public class KeyMetricsSummaryDto
    {
        public int TotalMembers { get; set; }
        public int TodayNewMembers { get; set; }
        public int TodayViewers { get; set; }
    }
}
