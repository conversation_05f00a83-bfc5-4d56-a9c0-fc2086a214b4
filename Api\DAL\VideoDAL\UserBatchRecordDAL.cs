using Common.Autofac;
using DAL.Databases;
using Entity.Dto;
using Entity.Entitys.VideoEntity;
using Entity.Extensions;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.VideoDAL
{
    /// <summary>
    /// 用户批次记录数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserBatchRecordDAL(MyContext context) : BaseQueryDLL<UserBatchRecord, UserBatchRecordDAL.Queryable>(context)
    {

        /// <summary>
        /// 获取数据库上下文
        /// </summary>
        /// <returns>数据库上下文</returns>
        public MyContext GetDbContext() => _dbContext;

        /// <summary>
        /// 用户批次记录查询条件模型类
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? UserId { get; set; }

            /// <summary>
            /// 批次ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? BatchId { get; set; }

            /// <summary>
            /// 是否完播
            /// </summary>
            [Query(QueryOperator.等于)]
            public bool? IsCompleted { get; set; }

            /// <summary>
            /// 红包状态
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? RewardStatus { get; set; }

            /// <summary>
            /// 员工ID（用于权限过滤）
            /// </summary>
            [Query(QueryOperator.等于, columnName: "User.EmployeeId")]
            public string? EmployeeId { get; set; }

            /// <summary>
            /// 用户ID列表（用于权限过滤）
            /// </summary>
            [Query(QueryOperator.包含于)]
            public List<string>? UserIds { get; set; }

            /// <summary>
            /// 开始时间范围 - 起始
            /// </summary>
            [Query(QueryOperator.日期范围, relatedProperty: nameof(StartTimeTo))]
            public DateTime? StartTimeFrom { get; set; }

            /// <summary>
            /// 开始时间范围 - 结束
            /// </summary>
            public DateTime? StartTimeTo { get; set; }

            /// <summary>
            /// 排序字段
            /// </summary>
            [Query(QueryOperator.排序, orderDirection: OrderDirection.降序, orderPriority: 1)]
            public DateTime? CreateTime { get; set; }
        }

        #region 基础CRUD操作

        /// <summary>
        /// 获取用户在指定批次的记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>用户批次记录</returns>
        public async Task<UserBatchRecord?> GetUserBatchRecordAsync(string userId, int batchId)
        {
            return await _dbContext.Set<UserBatchRecord>()
                .FirstOrDefaultAsync(ubr => ubr.UserId == userId && ubr.BatchId == batchId);
        }

        /// <summary>
        /// 检查用户是否已有批次记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否存在记录</returns>
        public async Task<bool> ExistsAsync(string userId, int batchId)
        {
            return await _dbContext.Set<UserBatchRecord>()
                .AnyAsync(ubr => ubr.UserId == userId && ubr.BatchId == batchId);
        }

        /// <summary>
        /// 创建或获取用户批次记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <param name="promotionLink">推广链接</param>
        /// <returns>用户批次记录</returns>
        public async Task<UserBatchRecord> CreateOrGetAsync(string userId, int batchId, CurrentUserInfoDto currentUserInfo, string? promotionLink = null)
        {
            var existing = await GetUserBatchRecordAsync(userId, batchId);
            if (existing != null)
            {
                return existing;
            }

            var record = new UserBatchRecord
            {
                UserId = userId,
                BatchId = batchId,
                PromotionLink = promotionLink,
                StartTime = DateTime.Now
            };

            // 使用扩展方法设置审计字段
            record.InitializeForAdd(currentUserInfo);

            _dbContext.Set<UserBatchRecord>().Add(record);
            await _dbContext.SaveChangesAsync();
            return record;
        }

        #endregion

        #region 观看相关操作

        /// <summary>
        /// 更新观看进度
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <param name="viewDuration">观看时长</param>
        /// <param name="watchProgress">观看进度</param>
        /// <param name="isCompleted">是否完播</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateWatchProgressAsync(string userId, int batchId, int viewDuration, decimal watchProgress, bool isCompleted, CurrentUserInfoDto currentUserInfo)
        {
            var record = await GetUserBatchRecordAsync(userId, batchId);
            if (record == null) return false;

            // 更新观看数据（取最大值，防止倒退）
            record.ViewDuration = Math.Max(record.ViewDuration, viewDuration);
            record.WatchProgress = Math.Max(record.WatchProgress, watchProgress);
            record.LastWatchTime = DateTime.Now;

            if (isCompleted && !record.IsCompleted)
            {
                record.IsCompleted = true;
            }

            // 使用扩展方法设置审计字段
            record.InitializeForUpdate(currentUserInfo);

            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 检查用户是否已观看
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否已观看</returns>
        public async Task<bool> HasWatchedAsync(string userId, int batchId)
        {
            return await _dbContext.Set<UserBatchRecord>()
                .AnyAsync(ubr => ubr.UserId == userId && ubr.BatchId == batchId && ubr.ViewDuration > 0);
        }

        /// <summary>
        /// 检查用户是否完播
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否完播</returns>
        public async Task<bool> HasCompletedAsync(string userId, int batchId)
        {
            return await _dbContext.Set<UserBatchRecord>()
                .AnyAsync(ubr => ubr.UserId == userId && ubr.BatchId == batchId && ubr.IsCompleted);
        }

        #endregion

        #region 答题相关操作

        /// <summary>
        /// 更新答题结果
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <param name="totalQuestions">总题目数</param>
        /// <param name="correctAnswers">正确答案数</param>
        /// <param name="answerDetails">答题详情JSON</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateAnswerResultAsync(string userId, int batchId, int totalQuestions, int correctAnswers, string answerDetails, CurrentUserInfoDto currentUserInfo)
        {
            var record = await GetUserBatchRecordAsync(userId, batchId);
            if (record == null) return false;

            record.TotalQuestions = totalQuestions;
            record.CorrectAnswers = correctAnswers;
            record.AnswerDetails = answerDetails;
            record.AnswerTime = DateTime.Now;

            // 使用扩展方法设置审计字段
            record.InitializeForUpdate(currentUserInfo);

            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 检查用户是否已答题
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否已答题</returns>
        public async Task<bool> HasAnsweredAsync(string userId, int batchId)
        {
            return await _dbContext.Set<UserBatchRecord>()
                .AnyAsync(ubr => ubr.UserId == userId && ubr.BatchId == batchId && ubr.TotalQuestions > 0);
        }

        #endregion

        #region 红包相关操作

        /// <summary>
        /// 更新红包信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <param name="rewardAmount">红包金额</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <param name="transactionId">微信支付交易号</param>
        /// <param name="outTradeNo">微信支付订单号</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateRewardAsync(string userId, int batchId, decimal rewardAmount, CurrentUserInfoDto currentUserInfo, string? transactionId = null, string? outTradeNo = null)
        {
            var record = await GetUserBatchRecordAsync(userId, batchId);
            if (record == null) return false;

            record.RewardAmount = rewardAmount;
            record.RewardStatus = 1; // 发放成功
            record.RewardTime = DateTime.Now;
            record.TransactionId = transactionId;
            record.OutTradeNo = outTradeNo;

            // 使用扩展方法设置审计字段
            record.InitializeForUpdate(currentUserInfo);

            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 更新红包状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <param name="status">红包状态</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <param name="failReason">失败原因</param>
        /// <param name="transactionId">微信支付交易号</param>
        /// <param name="outTradeNo">微信支付订单号</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateRewardStatusAsync(string userId, int batchId, byte status, CurrentUserInfoDto currentUserInfo, string? failReason = null, string? transactionId = null, string? outTradeNo = null)
        {
            var record = await GetUserBatchRecordAsync(userId, batchId);
            if (record == null) return false;

            record.RewardStatus = status;
            record.RewardFailReason = failReason;
            record.TransactionId = transactionId;
            record.OutTradeNo = outTradeNo;

            if (status == 1) // 发放成功
            {
                record.RewardTime = DateTime.Now;
            }
            else if (status == 2) // 发放失败
            {
                record.RewardRetryCount++;
            }

            // 使用扩展方法设置审计字段
            record.InitializeForUpdate(currentUserInfo);

            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 检查用户是否已获得红包
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <returns>是否已获得红包</returns>
        public async Task<bool> HasRewardAsync(string userId, int batchId)
        {
            return await _dbContext.Set<UserBatchRecord>()
                .AnyAsync(ubr => ubr.UserId == userId && ubr.BatchId == batchId && ubr.RewardAmount > 0 && ubr.RewardStatus == 1);
        }

        #endregion

        #region 统计查询方法

        /// <summary>
        /// 获取批次统计数据
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="accessibleUserIds">可访问的用户ID列表（权限控制）</param>
        /// <returns>批次统计数据</returns>
        public async Task<BatchStatisticsResult> GetBatchStatisticsAsync(int batchId, List<string>? accessibleUserIds = null)
        {
            var query = _dbContext.Set<UserBatchRecord>().Where(ubr => ubr.BatchId == batchId);

            // 权限过滤
            if (accessibleUserIds != null)
            {
                query = query.Where(ubr => accessibleUserIds.Contains(ubr.UserId));
            }

            var records = await query.ToListAsync();
            var recordCount = records.Count; // 实际参与该批次的用户数（有记录的用户）
            var viewerCount = records.Count(r => r.ViewDuration > 0); // 实际观看的用户数

            // 获取总的可访问用户数（用于计算未参与用户数）
            int totalAccessibleUsers;
            if (accessibleUserIds == null)
            {
                // 超管：获取所有用户数量
                totalAccessibleUsers = await _dbContext.Set<User>().CountAsync();
            }
            else
            {
                // 管理员/员工：使用传入的可访问用户ID列表
                totalAccessibleUsers = accessibleUserIds.Count;
            }

            return new BatchStatisticsResult
            {
                TotalParticipants = totalAccessibleUsers, // 修正：总用户数应该是所有可访问用户数
                ViewerCount = viewerCount, // 实际观看的用户数
                UnwatchedUserCount = totalAccessibleUsers - viewerCount, // 修正：未观看用户数 = 总用户数 - 观看用户数
                CompletedViewerCount = records.Count(r => r.IsCompleted),
                AnswerCount = records.Count(r => r.TotalQuestions > 0),
                RewardCount = records.Count(r => r.RewardAmount > 0),
                SuccessRewardCount = records.Count(r => r.RewardAmount > 0 && r.RewardStatus == 1),
                TotalRewardAmount = records.Where(r => r.RewardStatus == 1).Sum(r => r.RewardAmount),
                AverageViewDuration = records.Where(r => r.ViewDuration > 0).Any() ?
                    (int)records.Where(r => r.ViewDuration > 0).Average(r => r.ViewDuration) : 0,
                AverageWatchProgress = records.Where(r => r.WatchProgress > 0).Any() ?
                    records.Where(r => r.WatchProgress > 0).Average(r => r.WatchProgress) : 0,
                AverageCorrectRate = records.Where(r => r.TotalQuestions > 0).Any() ?
                    records.Where(r => r.TotalQuestions > 0).Average(r => r.CorrectRate) : 0
            };
        }

        /// <summary>
        /// 获取用户的批次记录列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户批次记录列表</returns>
        public async Task<List<UserBatchRecord>> GetUserRecordsAsync(string userId)
        {
            return await _dbContext.Set<UserBatchRecord>()
                .Where(ubr => ubr.UserId == userId)
                .OrderByDescending(ubr => ubr.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取批次的所有记录（支持权限过滤）
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="accessibleUserIds">可访问的用户ID列表</param>
        /// <returns>批次记录列表</returns>
        public async Task<List<UserBatchRecord>> GetBatchRecordsAsync(int batchId, List<string>? accessibleUserIds = null)
        {
            var query = _dbContext.Set<UserBatchRecord>().Where(ubr => ubr.BatchId == batchId);

            if (accessibleUserIds != null)
            {
                query = query.Where(ubr => accessibleUserIds.Contains(ubr.UserId));
            }

            return await query.OrderByDescending(ubr => ubr.CreateTime).ToListAsync();
        }

        /// <summary>
        /// 获取批次的所有用户记录（包括没有观看记录的用户）
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="accessibleUserIds">可访问的用户ID列表</param>
        /// <returns>所有用户的记录列表（没有记录的用户返回null记录）</returns>
        public async Task<List<UserBatchRecord?>> GetBatchRecordsWithAllUsersAsync(int batchId, List<string>? accessibleUserIds = null)
        {
            // 获取所有可访问的用户ID
            List<string> allUserIds;
            if (accessibleUserIds == null)
            {
                // 超管：获取所有用户ID
                allUserIds = await _dbContext.Set<User>()
                    .Select(u => u.Id)
                    .ToListAsync();
            }
            else
            {
                // 管理员/员工：使用传入的可访问用户ID列表
                allUserIds = accessibleUserIds;
            }

            // 获取有记录的用户
            var existingRecords = await _dbContext.Set<UserBatchRecord>()
                .Where(ubr => ubr.BatchId == batchId && allUserIds.Contains(ubr.UserId))
                .ToListAsync();

            // 创建结果列表，包含所有用户
            var result = new List<UserBatchRecord?>();

            foreach (var userId in allUserIds)
            {
                var record = existingRecords.FirstOrDefault(r => r.UserId == userId);
                result.Add(record); // 如果没有记录，添加null
            }

            return result;
        }

        #endregion
    }

    /// <summary>
    /// 批次统计结果
    /// </summary>
    public class BatchStatisticsResult
    {
        public int TotalParticipants { get; set; }
        public int ViewerCount { get; set; }
        public int UnwatchedUserCount { get; set; }
        public int CompletedViewerCount { get; set; }
        public int AnswerCount { get; set; }
        public int RewardCount { get; set; }
        public int SuccessRewardCount { get; set; }
        public decimal TotalRewardAmount { get; set; }
        public int AverageViewDuration { get; set; }
        public decimal AverageWatchProgress { get; set; }
        public decimal AverageCorrectRate { get; set; }
    }
}
