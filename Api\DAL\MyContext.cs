﻿using Entity.Entitys.SysEntity;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;

namespace DAL
{
    /// <summary>
    /// 数据库上下文类,继承自DbContext
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="options">数据库上下文配置选项</param>
    public class MyContext(DbContextOptions<MyContext> options) : DbContext(options)
    {
        #region DbSet属性

        #region 系统相关表

        /// <summary>
        /// 系统管理员和员工用户表
        /// </summary>
        public DbSet<SysUser> SysUsers { get; set; }


        /// <summary>
        /// 系统日志表
        /// </summary>
        public DbSet<SysLog> SysLogs { get; set; }

        #endregion



        #region 视频相关表

        /// <summary>
        /// 用户表
        /// </summary>
        public DbSet<User> Users { get; set; }

        /// <summary>
        /// 视频表
        /// </summary>
        public DbSet<Video> Videos { get; set; }

        /// <summary>
        /// 批次表
        /// </summary>
        public DbSet<Batch> Batches { get; set; }

        /// <summary>
        /// 用户批次记录表（统一观看、答题、红包记录）
        /// </summary>
        public DbSet<UserBatchRecord> UserBatchRecords { get; set; }

        /// <summary>
        /// 用户审核表
        /// </summary>
        public DbSet<UserAudit> UserAudits { get; set; }

        /// <summary>
        /// 系统配置表
        /// </summary>
        public DbSet<SystemConfig> SystemConfigs { get; set; }

        /// <summary>
        /// 用户转移表
        /// </summary>
        public DbSet<UserTransfer> UserTransfers { get; set; }





        /// <summary>
        /// 微信访问令牌表
        /// </summary>
        public DbSet<WechatAccessToken> WechatAccessTokens { get; set; }

        /// <summary>
        /// 微信支付表
        /// </summary>
        public DbSet<WechatPayment> WechatPayments { get; set; }

        #endregion
        #endregion

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 为实时统计优化业务表索引
            ConfigureStatisticsOptimizedIndexes(modelBuilder);
        }

        /// <summary>
        /// 配置用于统计查询优化的索引
        /// </summary>
        private static void ConfigureStatisticsOptimizedIndexes(ModelBuilder modelBuilder)
        {
            // UserBatchRecord 表索引优化
            modelBuilder.Entity<UserBatchRecord>(entity =>
            {
                // 用户ID和批次ID复合索引，用于快速查询用户的批次记录
                entity.HasIndex(e => new { e.UserId, e.BatchId })
                      .HasDatabaseName("IX_UserBatchRecords_UserId_BatchId")
                      .IsUnique(); // 确保一个用户在一个批次中只有一条记录

                // 批次ID索引，用于批次统计查询
                entity.HasIndex(e => e.BatchId)
                      .HasDatabaseName("IX_UserBatchRecords_BatchId");

                // 用户ID索引，用于用户相关查询
                entity.HasIndex(e => e.UserId)
                      .HasDatabaseName("IX_UserBatchRecords_UserId");

                // 创建时间索引，用于时间范围查询
                entity.HasIndex(e => e.CreateTime)
                      .HasDatabaseName("IX_UserBatchRecords_CreateTime");

                // 完播状态索引，用于统计查询
                entity.HasIndex(e => e.IsCompleted)
                      .HasDatabaseName("IX_UserBatchRecords_IsCompleted");

                // 答题状态索引，用于统计查询（基于TotalQuestions字段）
                entity.HasIndex(e => e.TotalQuestions)
                      .HasDatabaseName("IX_UserBatchRecords_TotalQuestions");

                // 红包状态索引，用于红包相关查询
                entity.HasIndex(e => e.RewardStatus)
                      .HasDatabaseName("IX_UserBatchRecords_RewardStatus");
            });

            // 用户表索引优化
            modelBuilder.Entity<User>(entity =>
            {
                // 员工ID索引，用于权限查询
                entity.HasIndex(e => e.EmployeeId)
                      .HasDatabaseName("IX_Users_EmployeeId");
            });
        }
    }
}
