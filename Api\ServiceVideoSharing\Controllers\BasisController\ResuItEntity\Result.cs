﻿namespace ServiceVideoSharing.Controllers.BasisController.ResuItEntity
{

    /// <summary>
    /// 接口返回规范
    /// </summary>
    public class Result
    {
        public Result()
        {
            Code = 200;
            Success = true;
        }
        /// <summary>
        /// 响应码
        /// </summary>
        public int Code { get; set; }
        /// <summary>
        /// 返回消息
        /// </summary>
        public string? Msg { get; set; }
        private bool _success;
        /// <summary>
        /// 返回结果
        /// </summary>
        public bool Success
        {
            get => _success;
            set
            {
                _success = value;
                // 当success为false时自动设置code为500
                if (!value) if (Code == 200) Code = 500;
            }
        }
    }

    public class Result<T> : Result
    {
        /// <summary>
        /// 接口返回数据
        /// </summary>
        public T? Data { get; set; }
    }
}
