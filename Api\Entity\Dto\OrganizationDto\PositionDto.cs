using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.OrganizationDto
{
    /// <summary>
    /// 职位DTO
    /// </summary>
    public class PositionDto
    {
        public long Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public int Level { get; set; }
        public int Type { get; set; }
        public int Status { get; set; }
        public int Sort { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// 创建职位DTO
    /// </summary>
    public class CreatePositionDto
    {
        [Required]
        [MaxLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;

        public int Level { get; set; }

        public int Type { get; set; }

        public int Sort { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }
    }

    /// <summary>
    /// 更新职位DTO
    /// </summary>
    public class UpdatePositionDto
    {
        public string Id { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string Code { get; set; } = string.Empty;

        public int Level { get; set; }

        public int Type { get; set; }

        public int Status { get; set; }

        public int Sort { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }
    }
}