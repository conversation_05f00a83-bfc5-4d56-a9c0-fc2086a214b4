using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.VideoDAL
{
    /// <summary>
    /// 视频数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class VideoDAL(MyContext context) : BaseQueryDLL<Video, VideoDAL.Queryable>(context)
    {

        /// <summary>
        /// 视频查询条件模型类
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 视频标题(模糊查询)
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Title { get; set; }

            /// <summary>
            /// 创建人ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? CreatedBy { get; set; }

            /// <summary>
            /// 创建人ID列表（用于权限过滤）
            /// </summary>
            [Query(QueryOperator.包含于, columnName: "CreatedBy")]
            public List<string>? CreatedByList { get; set; }

            /// <summary>
            /// 状态:0下架,1上架
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? Status { get; set; }

            /// <summary>
            /// 开始时间
            /// </summary>
            [Query(QueryOperator.日期范围, relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 结束时间
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 排序字段
            /// </summary>
            [Query(QueryOperator.排序, orderDirection: OrderDirection.降序, orderPriority: 1)]
            public DateTime? CreateTime { get; set; }
        }

        /// <summary>
        /// 根据标题获取视频
        /// </summary>
        /// <param name="title">视频标题</param>
        /// <returns>视频实体</returns>
        public async Task<Video?> GetByTitleAsync(string title)
        {
            return await _dbContext.Set<Video>()
                .FirstOrDefaultAsync(v => v.Title == title);
        }

        /// <summary>
        /// 检查视频标题是否存在
        /// </summary>
        /// <param name="title">视频标题</param>
        /// <param name="excludeId">排除的视频ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsTitleAsync(string title, int? excludeId = null)
        {
            var query = _dbContext.Set<Video>().Where(v => v.Title == title);
            if (excludeId.HasValue)
            {
                query = query.Where(v => v.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        /// <summary>
        /// 获取用户创建的视频列表
        /// </summary>
        /// <param name="createdBy">创建人ID</param>
        /// <returns>视频列表</returns>
        public async Task<List<Video>> GetByCreatorAsync(string createdBy)
        {
            return await _dbContext.Set<Video>()
                .Where(v => v.CreatedBy == createdBy)
                .OrderByDescending(v => v.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取上架的视频列表
        /// </summary>
        /// <returns>上架视频列表</returns>
        public async Task<List<Video>> GetOnlineVideosAsync()
        {
            return await _dbContext.Set<Video>()
                .Where(v => v.Status == 1)
                .OrderByDescending(v => v.CreateTime)
                .ToListAsync();
        }

        /// <summary>
        /// 更新视频状态
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <param name="status">新状态</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateStatusAsync(int videoId, byte status)
        {
            var video = await _dbContext.Set<Video>().FindAsync(videoId);
            if (video == null) return false;

            video.Status = status;
            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取视频统计信息
        /// </summary>
        /// <param name="videoId">视频ID</param>
        /// <returns>统计信息</returns>
        public async Task<VideoStatistics> GetVideoStatisticsAsync(int videoId)
        {
            // 通过视频ID获取相关批次
            var batchIds = await _dbContext.Set<Batch>()
                .Where(b => b.VideoId == videoId)
                .Select(b => b.Id)
                .ToListAsync();

            // 使用新的 UserBatchRecord 进行统计
            var viewCount = await _dbContext.Set<UserBatchRecord>()
                .CountAsync(ubr => batchIds.Contains(ubr.BatchId) && ubr.ViewDuration > 0);

            var completeViewCount = await _dbContext.Set<UserBatchRecord>()
                .CountAsync(ubr => batchIds.Contains(ubr.BatchId) && ubr.IsCompleted);

            var rewardCount = await _dbContext.Set<UserBatchRecord>()
                .CountAsync(ubr => batchIds.Contains(ubr.BatchId) && ubr.RewardAmount > 0);

            var rewardAmount = await _dbContext.Set<UserBatchRecord>()
                .Where(ubr => batchIds.Contains(ubr.BatchId))
                .SumAsync(ubr => (decimal?)ubr.RewardAmount) ?? 0;

            return new VideoStatistics
            {
                ViewCount = viewCount,
                CompleteViewCount = completeViewCount,
                CompleteRate = viewCount > 0 ? (decimal)completeViewCount / viewCount * 100 : 0,
                RewardCount = rewardCount,
                RewardAmount = rewardAmount
            };
        }

        /// <summary>
        /// 根据ID获取视频
        /// </summary>
        /// <param name="id">视频ID</param>
        /// <returns>视频实体</returns>
        public async Task<Video?> GetByIdAsync(int id)
        {
            return await _dbContext.Set<Video>().FindAsync(id);
        }

        /// <summary>
        /// 获取分页视频列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<Video>> GetPagedListAsync(Queryable queryable)
        {
            return await GetPageDataAsync(queryable, q => q.OrderByDescending(x => x.CreateTime));
        }



        /// <summary>
        /// 视频统计信息类
        /// </summary>
        public class VideoStatistics
        {
            public int ViewCount { get; set; }
            public int CompleteViewCount { get; set; }
            public decimal CompleteRate { get; set; }
            public int RewardCount { get; set; }
            public decimal RewardAmount { get; set; }
        }
    }
}
