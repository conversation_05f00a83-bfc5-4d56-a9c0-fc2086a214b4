using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.VideoEntity
{
    /// <summary>
    /// 视频内容表
    /// </summary>
    [Table("videos")]
    public class Video : BaseEntity_ID
    {
        /// <summary>
        /// 视频标题
        /// </summary>
        [Required]
        [MaxLength(255)]
        [Comment("视频标题")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 视频描述
        /// </summary>
        [Comment("视频描述")]
        public string? Description { get; set; }

        /// <summary>
        /// 封面URL
        /// </summary>
        [MaxLength(255)]
        [Comment("封面URL")]
        public string? CoverUrl { get; set; }

        /// <summary>
        /// 视频URL
        /// </summary>
        [Required]
        [MaxLength(255)]
        [Comment("视频URL")]
        public string VideoUrl { get; set; } = string.Empty;

        /// <summary>
        /// 视频时长(秒)
        /// </summary>
        [Comment("视频时长(秒)")]
        public int Duration { get; set; } = 0;



        /// <summary>
        /// 红包金额
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        [Comment("红包金额")]
        public decimal RewardAmount { get; set; } = 0.00m;

        /// <summary>
        /// 问题JSON格式 - 格式: [{"questionText":"问题内容","orderNum":0,"options":[{"optionText":"选项1","isCorrect":true,"orderNum":0},{"optionText":"选项2","isCorrect":false,"orderNum":1}]}]
        /// </summary>
        [Comment("问题JSON格式 - 格式: [{'questionText':'问题内容','orderNum':0,'options':[{'optionText':'选项1','isCorrect':true,'orderNum':0},{'optionText':'选项2','isCorrect':false,'orderNum':1}]}]")]
        public string? Questions { get; set; }

        /// <summary>
        /// 观看次数
        /// </summary>
        [Comment("观看次数")]
        public int ViewCount { get; set; } = 0;

        /// <summary>
        /// 状态:0下架,1上架,2失败,3压缩中
        /// </summary>
        [Comment("状态:0下架,1上架,2失败,3压缩中")]
        public byte Status { get; set; } = 1;

        /// <summary>
        /// 文件ID，用于关联压缩进度
        /// </summary>
        [MaxLength(50)]
        [Comment("文件ID，用于关联压缩进度")]
        public string? FileId { get; set; }
    }
}
