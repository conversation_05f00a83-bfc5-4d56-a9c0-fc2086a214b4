@echo off
echo 正在启动已编译的应用程序（无调试）...
cd /d %~dp0

if exist "AdminProjectTemplate\bin\Debug\net8.0\AdminProjectTemplate.dll" (
    echo 找到调试版本，正在启动...
    cd AdminProjectTemplate\bin\Debug\net8.0
    start "" dotnet AdminProjectTemplate.dll --urls=http://localhost:7048
    timeout /t 2 >nul
    start http://localhost:7048
) else if exist "publish\AdminProjectTemplate.dll" (
    echo 找到发布版本，正在启动...
    cd publish
    start "" dotnet AdminProjectTemplate.dll --urls=http://localhost:7048
    timeout /t 2 >nul
    start http://localhost:7048
) else (
    echo 未找到编译好的应用程序。
    echo 请先运行 build-app.bat 打包应用或使用 start-app.bat 运行开发版本。
    pause
    exit /b 1
)

echo 应用程序已在 http://localhost:7048 启动
echo 可以按 Ctrl+C 关闭此窗口，应用将继续在后台运行。
pause