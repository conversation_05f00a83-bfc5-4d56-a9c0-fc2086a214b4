using System.ComponentModel.DataAnnotations;

namespace Entity.Dto
{
    /// <summary>
    /// 系统用户登录请求DTO
    /// </summary>
    public class SysLoginRequestDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// </summary>
        [Required(ErrorMessage = "密码不能为空")]
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 系统用户登录响应DTO
    /// </summary>
    public class SysLoginResponseDto
    {
        /// <summary>
        /// 访问令牌
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// 用户信息
        /// </summary>
        public SysUserInfoDto UserInfo { get; set; } = new SysUserInfoDto();
    }

    /// <summary>
    /// 系统用户信息DTO
    /// </summary>
    public class SysUserInfoDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 昵称
        /// </summary>
        public string NickName { get; set; } = string.Empty;

        /// <summary>
        /// 用户类型
        /// 1:超级管理员 2:管理员 3:员工
        /// </summary>
        public byte UserType { get; set; }
    }
}
