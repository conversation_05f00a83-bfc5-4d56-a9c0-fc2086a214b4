using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.VideoDAL
{
    /// <summary>
    /// 批次数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class BatchDAL(MyContext context) : BaseQueryDLL<Batch, BatchDAL.Queryable>(context)
    {

        /// <summary>
        /// 批次查询条件模型类
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 批次名称(模糊查询)
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Name { get; set; }

            /// <summary>
            /// 创建人ID
            /// </summary>
            [Query(QueryOperator.等于, columnName: "CreatedBy")]
            public string? CreatedBy { get; set; }

            /// <summary>
            /// 状态:0下线,1上线
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? Status { get; set; }

            /// <summary>
            /// 开始时间
            /// </summary>
            [Query(QueryOperator.日期范围, relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 结束时间
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 排序字段
            /// </summary>
            [Query(QueryOperator.排序, orderDirection: OrderDirection.降序, orderPriority: 1)]
            public DateTime? CreateTime { get; set; }

            /// <summary>
            /// 创建人ID列表（用于权限过滤）
            /// </summary>
            [Query(QueryOperator.包含于, columnName: "CreatedBy")]
            public List<string>? CreatedByList { get; set; }
        }

        /// <summary>
        /// 根据批次名称获取批次
        /// </summary>
        /// <param name="name">批次名称</param>
        /// <returns>批次实体</returns>
        public async Task<Batch?> GetByNameAsync(string name)
        {
            return await _dbContext.Set<Batch>()
                .FirstOrDefaultAsync(b => b.Name == name);
        }

        /// <summary>
        /// 检查批次名称是否存在
        /// </summary>
        /// <param name="name">批次名称</param>
        /// <param name="excludeId">排除的批次ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsNameAsync(string name, int? excludeId = null)
        {
            var query = _dbContext.Set<Batch>().Where(b => b.Name == name);
            if (excludeId.HasValue)
            {
                query = query.Where(b => b.Id != excludeId.Value);
            }
            return await query.AnyAsync();
        }

        /// <summary>
        /// 根据ID获取批次
        /// </summary>
        /// <param name="id">批次ID</param>
        /// <returns>批次实体</returns>
        public async Task<Batch?> GetByIdAsync(int id)
        {
            return await _dbContext.Set<Batch>().FindAsync(id);
        }

        /// <summary>
        /// 获取分页批次列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<Batch>> GetPagedListAsync(Queryable queryable)
        {
            return await GetPageDataAsync(queryable, q => q.OrderByDescending(x => x.CreateTime));
        }
    }
}
