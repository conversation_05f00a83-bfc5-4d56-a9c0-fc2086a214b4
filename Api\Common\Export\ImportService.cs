using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Reflection;
using System.Text;

namespace Common.Export
{
    /// <summary>
    /// 导入服务实现
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="logger">日志记录器</param>
    public class ImportService(ILogger<ImportService> logger) : IImportService
    {
        private readonly ILogger<ImportService> _logger = logger;

        /// <summary>
        /// 从CSV文件导入数据
        /// </summary>
        /// <typeparam name="T">目标数据类型</typeparam>
        /// <param name="file">上传的CSV文件</param>
        /// <param name="columnMappings">列映射配置（CSV列名 -> 属性名）</param>
        /// <returns>导入的数据列表</returns>
        public async Task<List<T>> ImportFromCsvAsync<T>(IFormFile file, Dictionary<string, string> columnMappings) where T : new()
        {
            try
            {
                _logger.LogInformation("开始从CSV文件导入数据");

                if (file == null || file.Length == 0)
                {
                    throw new ArgumentException("文件为空");
                }

                var result = new List<T>();
                using var stream = file.OpenReadStream();
                using var reader = new StreamReader(stream, Encoding.GetEncoding("gb2312"));

                // 读取表头
                var headerLine = await reader.ReadLineAsync();
                if (string.IsNullOrEmpty(headerLine))
                {
                    throw new InvalidDataException("CSV文件格式无效：缺少表头");
                }

                // 解析表头
                var headers = ParseCsvLine(headerLine);
                var columnIndexMap = new Dictionary<string, int>();

                // 创建列名到索引的映射
                for (int i = 0; i < headers.Count; i++)
                {
                    columnIndexMap[headers[i]] = i;
                }

                // 验证所有必需的列是否存在
                foreach (var column in columnMappings.Keys)
                {
                    if (!columnIndexMap.ContainsKey(column))
                    {
                        throw new InvalidDataException($"CSV文件缺少必需的列：{column}");
                    }
                }

                // 读取数据行
                string? line;
                int lineNumber = 1;
                while ((line = await reader.ReadLineAsync()) != null)
                {
                    lineNumber++;
                    if (string.IsNullOrWhiteSpace(line))
                    {
                        continue;
                    }

                    try
                    {
                        var values = ParseCsvLine(line);
                        var item = new T();

                        // 设置属性值
                        foreach (var mapping in columnMappings)
                        {
                            var csvColumnName = mapping.Key;
                            var propertyName = mapping.Value;

                            if (columnIndexMap.TryGetValue(csvColumnName, out int columnIndex) && columnIndex < values.Count)
                            {
                                var value = values[columnIndex];
                                SetPropertyValue(item, propertyName, value);
                            }
                        }

                        result.Add(item);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "解析CSV行 {LineNumber} 失败", lineNumber);
                        // 继续处理下一行
                    }
                }

                _logger.LogInformation("CSV文件导入完成，共导入 {Count} 条记录", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从CSV文件导入数据失败");
                throw new ApplicationException("从CSV文件导入数据失败", ex);
            }
        }

        /// <summary>
        /// 验证CSV文件格式
        /// </summary>
        /// <param name="file">上传的CSV文件</param>
        /// <param name="requiredColumns">必需的列名</param>
        /// <returns>验证结果</returns>
        public async Task<(bool IsValid, string ErrorMessage)> ValidateCsvFileAsync(IFormFile file, List<string> requiredColumns)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return (false, "文件为空");
                }

                using var stream = file.OpenReadStream();
                using var reader = new StreamReader(stream, Encoding.GetEncoding("gb2312"));

                // 读取表头
                var headerLine = await reader.ReadLineAsync();
                if (string.IsNullOrEmpty(headerLine))
                {
                    return (false, "CSV文件格式无效：缺少表头");
                }

                // 解析表头
                var headers = ParseCsvLine(headerLine);
                var missingColumns = requiredColumns.Where(col => !headers.Contains(col)).ToList();

                if (missingColumns.Count != 0)
                {

                    return (false, $"CSV文件缺少必需的列：{string.Join(", ", missingColumns)}");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证CSV文件失败");
                return (false, $"验证CSV文件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        /// <param name="line">CSV行</param>
        /// <returns>字段值列表</returns>
        private static List<string> ParseCsvLine(string line)
        {
            var result = new List<string>();
            if (string.IsNullOrEmpty(line))
            {
                return result;
            }

            bool inQuotes = false;
            var currentField = new StringBuilder();

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                    {
                        // 处理双引号转义
                        currentField.Append('"');
                        i++;
                    }
                    else
                    {
                        // 切换引号状态
                        inQuotes = !inQuotes;
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    // 字段结束
                    result.Add(currentField.ToString());
                    currentField.Clear();
                }
                else
                {
                    // 普通字符
                    currentField.Append(c);
                }
            }

            // 添加最后一个字段
            result.Add(currentField.ToString());

            return result;
        }

        /// <summary>
        /// 设置属性值
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="obj">目标对象</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="value">属性值</param>
        private void SetPropertyValue<T>(T obj, string propertyName, string value)
        {
            try
            {
                if (obj == null)
                {
                    _logger.LogWarning("目标对象为null");
                    return;
                }
                // 支持嵌套属性，如 "User.Name"
                var propertyNames = propertyName.Split('.');
                object currentObj = obj;

                for (int i = 0; i < propertyNames.Length; i++)
                {
                    var propName = propertyNames[i];
                    PropertyInfo? property = currentObj.GetType().GetProperty(propName);

                    if (property == null)
                    {
                        _logger.LogWarning("属性不存在: {PropertyName}", propName);
                        return;
                    }

                    if (i == propertyNames.Length - 1)
                    {
                        // 最后一个属性，设置值
                        if (string.IsNullOrEmpty(value))
                        {
                            // 如果值为空，则设置为默认值或null
                            property.SetValue(currentObj, null);
                        }
                        else
                        {
                            // 转换值类型并设置
                            var convertedValue = ConvertValue(value, property.PropertyType);
                            property.SetValue(currentObj, convertedValue);
                        }
                    }
                    else
                    {
                        // 中间属性，获取或创建对象
                        object? nestedObj = property.GetValue(currentObj);
                        if (nestedObj == null)
                        {
                            // 如果嵌套对象为null，则创建新实例
                            nestedObj = Activator.CreateInstance(property.PropertyType);
                            property.SetValue(currentObj, nestedObj);
                        }
                        currentObj = nestedObj!;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "设置属性值失败: {PropertyName}, 值: {Value}", propertyName, value);
            }
        }

        /// <summary>
        /// 转换值类型
        /// </summary>
        /// <param name="value">字符串值</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>转换后的值</returns>
        private static object? ConvertValue(string value, Type targetType)
        {
            if (string.IsNullOrEmpty(value))
            {
                return null;
            }

            // 处理可空类型
            Type underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

            if (underlyingType == typeof(string))
            {
                return value;
            }
            else if (underlyingType == typeof(int))
            {
                return int.Parse(value);
            }
            else if (underlyingType == typeof(long))
            {
                return long.Parse(value);
            }
            else if (underlyingType == typeof(double))
            {
                return double.Parse(value);
            }
            else if (underlyingType == typeof(decimal))
            {
                return decimal.Parse(value);
            }
            else if (underlyingType == typeof(bool))
            {
                return bool.Parse(value);
            }
            else if (underlyingType == typeof(DateTime))
            {
                return DateTime.Parse(value);
            }
            else if (underlyingType == typeof(Guid))
            {
                return Guid.Parse(value);
            }
            else if (underlyingType.IsEnum)
            {
                return Enum.Parse(underlyingType, value);
            }
            else
            {
                throw new NotSupportedException($"不支持的类型转换: {targetType.Name}");
            }
        }
    }
}