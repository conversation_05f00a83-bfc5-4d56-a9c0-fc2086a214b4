namespace Entity.Dto.CardDto;

public class ChannelCardValueConfigDto
{

}


public class CreateChannelCardValueConfigDto
{
    /// <summary>   
    /// 渠道
    /// </summary>
    public string Channel { get; set; } = string.Empty;

    /// <summary>
    /// 卡片类型
    /// </summary>
    public string CardType { get; set; } = string.Empty;

    /// <summary>
    /// 游戏币
    /// </summary>
    public long GameCurrency { get; set; }

    /// <summary>
    /// 实际面值
    /// </summary>
    public decimal ActualValue { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}


public class UpdateChannelCardValueConfigDto
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 实际面值
    /// </summary>
    public decimal ActualValue { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}