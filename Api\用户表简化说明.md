# 用户表简化说明

## 概述
根据您的要求，已对用户信息表进行简化，只保留核心字段：用户信息、绑定的员工信息、登录时间和注册时间。

## 修改内容

### 1. User实体简化 (Api/Entity/Entitys/VideoEntity/User.cs)

**保留的字段：**
- `Id` - 用户ID（来自BaseEntity_ID）
- `OpenId` - 微信OpenID
- `UnionId` - 微信UnionID  
- `Nickname` - 微信昵称
- `Avatar` - 头像URL
- `EmployeeId` - 绑定的员工ID（关联SysUser表）
- `LastLogin` - 最后登录时间
- `CreateTime` - 注册时间（来自BaseEntity）
- `UpdateTime` - 更新时间（来自BaseEntity）

**删除的字段：**
- `RoleId` - 角色ID（角色管理功能）
- `ParentId` - 上级ID
- `SuperParentId` - 上上级ID（冗余字段）
- `Username` - 账号（管理员登录字段）
- `Password` - 密码（管理员登录字段）
- `Phone` - 手机号
- `RealName` - 真实姓名
- `Email` - 邮箱
- `AuditRemark` - 审核备注
- `Status` - 状态（审核相关）
- `AuditStatus` - 审核状态
- `AuditTime` - 审核时间

### 2. DTO类简化 (Api/Entity/Dto/VideoDto/UserDto.cs)

**UserQueryDto：**
- 保留：`Nickname`、`EmployeeId`、时间范围查询
- 删除：`Username`、`Phone`、`RealName`、`RoleId`、`ParentId`、`Status`、`AuditStatus`

**UserCreateDto：**
- 保留：微信相关字段（`OpenId`、`UnionId`、`Nickname`、`Avatar`）、`EmployeeId`
- 删除：角色、账号密码、个人信息、审核相关字段

**UserUpdateDto：**
- 保留：`Id`、`Nickname`、`Avatar`、`EmployeeId`
- 删除：角色、个人信息等字段

**UserResponseDto：**
- 保留：基础信息、微信信息、员工绑定信息、时间信息
- 新增：`EmployeeName`（员工姓名，用于显示）
- 删除：角色、审核、状态等复杂字段

**删除的DTO类：**
- `UserAuditDto` - 用户审核DTO
- `RegisterLinkDto` - 注册链接生成DTO
- `RegisterLinkResponseDto` - 注册链接响应DTO
- `AdminLoginDto` - 管理员登录DTO
- `UserStatusDto` - 用户状态DTO

**保留的DTO类：**
- `UserTransferDto` - 用户转移DTO（简化为只转移到员工）

### 3. 数据访问层简化 (Api/DAL/VideoDAL/UserDAL.cs)

**Queryable查询条件：**
- 保留：`Nickname`、`EmployeeId`、时间范围、排序
- 删除：`Username`、`Phone`、`RealName`、`RoleId`、`ParentId`、`Status`、`AuditStatus`

**删除的方法：**
- `GetByUsernameAsync` - 根据用户名获取用户
- `ExistsUsernameAsync` - 检查用户名是否存在
- `GetChildrenAsync` - 获取下级用户列表
- `GetPendingAuditUsersAsync` - 获取待审核用户列表
- `UpdateAuditStatusAsync` - 更新审核状态
- `BatchTransferUsersAsync` - 批量转移用户角色
- `UpdateUserRoleAsync` - 更新用户角色

**新增/修改的方法：**
- `GetByEmployeeIdAsync` - 根据员工ID获取绑定的用户列表
- `BatchTransferAsync` - 批量转移用户到新员工（简化版）
- `UpdateUserEmployeeAsync` - 更新用户绑定的员工

### 4. 业务服务层简化 (Api/BLL/VideoService/UserService.cs)

**删除的方法：**
- `AuditUserAsync` - 审核用户
- `ChangePasswordAsync` - 修改密码
- `ResetPasswordAsync` - 重置密码
- `ToggleUserStatusAsync` - 切换用户状态
- `LoginAsync` - 管理员登录（AdminLoginDto版本）
- `GetPendingAuditUsersAsync` - 获取待审核用户列表
- `VerifyPassword` - 验证密码（私有方法）
- `EncryptPassword` - 加密密码（私有方法）

**保留/修改的方法：**
- `CreateUserAsync` - 创建用户（简化，只保留微信信息和员工绑定）
- `UpdateUserAsync` - 更新用户（简化，只保留基础信息）
- `WechatLoginAsync` - 微信登录（保留）
- `GetUserAsync` - 获取用户详情（简化返回字段）
- `GetUserPagedListAsync` - 分页查询（简化查询条件和返回字段）
- `BatchTransferUsersAsync` - 批量转移用户（简化为只转移员工绑定）
- `BindWechatAsync` - 绑定微信（保留）
- `UnbindWechatAsync` - 解绑微信（保留）
- `GetUserStatisticsAsync` - 获取用户统计（简化返回字段）

### 5. 控制器简化 (Api/ServiceVideoSharing/Controllers/VideoControllers/UserController.cs)

**删除的接口：**
- `Login` - 管理员登录
- `ChangePassword` - 修改密码
- `ResetPassword` - 重置密码
- `ToggleUserStatus` - 启用/禁用用户
- `AuditUser` - 审核用户
- `GetPendingAuditUsers` - 获取待审核用户列表
- `GetUserStatistics` - 获取用户统计信息

**保留的接口：**
- `Register` - 用户注册
- `WechatLogin` - 微信登录
- `GetProfile` - 获取当前用户信息
- `UpdateProfile` - 更新用户资料
- `GetUser` - 获取用户详情
- `GetUserPagedList` - 分页查询用户列表
- `BindWechat` - 绑定微信
- `UnbindWechat` - 解绑微信
- `TransferUsers` - 转移用户到新员工

## 业务逻辑变化

1. **用户类型简化**：不再区分角色，所有用户都是普通微信用户
2. **员工绑定**：用户通过`EmployeeId`字段绑定到SysUser表中的员工
3. **审核流程移除**：不再需要用户审核，微信登录即可使用
4. **密码功能移除**：用户只能通过微信登录，不支持账号密码登录
5. **状态管理简化**：移除复杂的状态管理，用户要么存在要么不存在
6. **转移功能简化**：用户转移只是更改绑定的员工ID

## 数据库影响

由于删除了多个字段，建议：
1. 在应用新代码前备份数据库
2. 创建数据库迁移脚本删除不需要的列
3. 更新相关的数据库索引和约束

## 编译状态

- ✅ Entity项目编译成功
- ✅ User实体相关代码修改完成
- ⚠️ 其他项目存在与AnswerRecord相关的编译错误（与本次修改无关）

## 总结

用户表已成功简化，只保留了核心的用户信息、员工绑定信息、登录时间和注册时间。删除了所有复杂的角色管理、审核流程、密码管理等功能，使系统更加简洁和专注于核心业务需求。
