# 答题记录JSON格式说明

## 概述

用户答题记录采用JSON格式存储，记录用户对一个批次中所有题目的答题情况。一个批次只有一组题目，用户必须完成所有题目才能提交。

## 题目格式

基于现有的VideoQuestionDto结构，题目格式如下：

```json
{
  "questionText": "问题内容",
  "orderNum": 0,
  "options": [
    {
      "optionText": "选项1内容",
      "isCorrect": true,
      "orderNum": 0
    },
    {
      "optionText": "选项2内容", 
      "isCorrect": false,
      "orderNum": 1
    }
  ]
}
```

## 答题记录JSON格式

### 存储在AnswerRecord.AnswersJson字段中的格式：

```json
[
  {
    "questionOrderNum": 0,
    "questionText": "第一题：以下哪个是正确的？",
    "selectedOptionOrderNum": 0,
    "selectedOptionText": "选项A",
    "isCorrect": true
  },
  {
    "questionOrderNum": 1,
    "questionText": "第二题：以下哪个是错误的？",
    "selectedOptionOrderNum": 1,
    "selectedOptionText": "选项B",
    "isCorrect": false
  }
]
```

### 字段说明：

- `questionOrderNum`: 问题序号（对应题目的orderNum）
- `questionText`: 问题内容（冗余存储）
- `selectedOptionOrderNum`: 用户选择的选项序号
- `selectedOptionText`: 用户选择的选项内容（冗余存储）
- `isCorrect`: 该题是否答对

## 数据库表结构

### AnswerRecord表字段：

- `id`: 记录ID
- `user_id`: 用户ID
- `batch_id`: 批次ID
- `answers_json`: 答题信息JSON格式
- `total_questions`: 总题目数
- `correct_answers`: 正确题目数
- `answer_time`: 答题时间
- `created_at`: 创建时间

## API接口

### 提交答题记录

**POST** `/api/video/AnswerRecord/submit`

请求体：
```json
{
  "batchId": 1,
  "answers": [
    {
      "questionOrderNum": 0,
      "questionText": "第一题：以下哪个是正确的？",
      "selectedOptionOrderNum": 0,
      "selectedOptionText": "选项A",
      "isCorrect": true
    },
    {
      "questionOrderNum": 1,
      "questionText": "第二题：以下哪个是错误的？",
      "selectedOptionOrderNum": 1,
      "selectedOptionText": "选项B",
      "isCorrect": false
    }
  ]
}
```

响应：
```json
{
  "success": true,
  "message": "答题记录提交成功",
  "data": 123
}
```

### 获取答题记录详情

**GET** `/api/video/AnswerRecord/{recordId}`

响应：
```json
{
  "success": true,
  "data": {
    "id": 123,
    "userId": 1,
    "batchId": 1,
    "videoId": 1,
    "answers": [
      {
        "questionOrderNum": 0,
        "questionText": "第一题：以下哪个是正确的？",
        "selectedOptionOrderNum": 0,
        "selectedOptionText": "选项A",
        "isCorrect": true
      }
    ],
    "totalQuestions": 2,
    "correctAnswers": 1,
    "accuracyRate": 50.00,
    "answerTime": "2024-01-01T10:00:00",
    "createTime": "2024-01-01T10:00:00"
  }
}
```

## 业务规则

1. **一个批次只能提交一次**：用户对同一个批次只能提交一次答题记录
2. **必须完成所有题目**：提交时必须包含批次中所有题目的答案
3. **时间限制**：只能在批次的有效时间内提交答题
4. **自动计算统计**：系统自动计算总题数、正确数和正确率

## 注意事项

1. JSON序列化使用CamelCase命名策略
2. 为了数据完整性，问题和选项内容会冗余存储
3. 旧版本的单题提交接口已标记为废弃，建议使用新的批量提交接口
4. 答题记录一旦提交不可修改（除非管理员操作）
