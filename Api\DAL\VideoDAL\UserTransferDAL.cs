using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.VideoDAL
{
    /// <summary>
    /// 用户转移数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserTransferDAL(MyContext context) : BaseQueryDLL<UserTransfer, UserTransferDAL.Queryable>(context)
    {

        /// <summary>
        /// 用户转移查询条件模型类
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? UserId { get; set; }

            /// <summary>
            /// 原员工ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? FromEmployeeId { get; set; }

            /// <summary>
            /// 新员工ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? ToEmployeeId { get; set; }

            /// <summary>
            /// 操作人ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? OperatorId { get; set; }

            /// <summary>
            /// 操作人姓名
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? OperatorName { get; set; }

            /// <summary>
            /// 转移原因
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? Reason { get; set; }

            /// <summary>
            /// 开始时间
            /// </summary>
            [Query(QueryOperator.日期范围, relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 结束时间
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 排序字段
            /// </summary>
            [Query(QueryOperator.排序, orderDirection: OrderDirection.降序, orderPriority: 1)]
            public DateTime? TransferTime { get; set; }
        }

        /// <summary>
        /// 获取用户的转移历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>转移历史列表</returns>
        public async Task<List<UserTransfer>> GetUserTransferHistoryAsync(string userId)
        {
            return await _dbContext.Set<UserTransfer>()
                .Where(ut => ut.UserId == userId)
                .OrderByDescending(ut => ut.TransferTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取用户最新的转移记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>最新转移记录</returns>
        public async Task<UserTransfer?> GetLatestUserTransferAsync(string userId)
        {
            return await _dbContext.Set<UserTransfer>()
                .Where(ut => ut.UserId == userId)
                .OrderByDescending(ut => ut.TransferTime)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取操作人的转移记录
        /// </summary>
        /// <param name="operatorId">操作人ID</param>
        /// <returns>转移记录列表</returns>
        public async Task<List<UserTransfer>> GetOperatorTransferRecordsAsync(string operatorId)
        {
            return await _dbContext.Set<UserTransfer>()
                .Where(ut => ut.OperatorId == operatorId)
                .OrderByDescending(ut => ut.TransferTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取从某个员工转出的用户记录
        /// </summary>
        /// <param name="fromEmployeeId">原员工ID</param>
        /// <returns>转移记录列表</returns>
        public async Task<List<UserTransfer>> GetTransferFromEmployeeAsync(string fromEmployeeId)
        {
            return await _dbContext.Set<UserTransfer>()
                .Where(ut => ut.FromEmployeeId == fromEmployeeId)
                .OrderByDescending(ut => ut.TransferTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取转入某个员工的用户记录
        /// </summary>
        /// <param name="toEmployeeId">新员工ID</param>
        /// <returns>转移记录列表</returns>
        public async Task<List<UserTransfer>> GetTransferToEmployeeAsync(string toEmployeeId)
        {
            return await _dbContext.Set<UserTransfer>()
                .Where(ut => ut.ToEmployeeId == toEmployeeId)
                .OrderByDescending(ut => ut.TransferTime)
                .ToListAsync();
        }

        /// <summary>
        /// 获取转移统计信息
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>转移统计</returns>
        public async Task<TransferStatistics> GetTransferStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _dbContext.Set<UserTransfer>().AsQueryable();

            if (startDate.HasValue)
            {
                query = query.Where(ut => ut.TransferTime >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(ut => ut.TransferTime <= endDate.Value);
            }

            var totalCount = await query.CountAsync();
            var uniqueUsers = await query.Select(ut => ut.UserId).Distinct().CountAsync();
            var uniqueOperators = await query.Where(ut => !string.IsNullOrEmpty(ut.OperatorId))
                .Select(ut => ut.OperatorId).Distinct().CountAsync();

            return new TransferStatistics
            {
                TotalTransfers = totalCount,
                UniqueUsers = uniqueUsers,
                UniqueOperators = uniqueOperators
            };
        }

        /// <summary>
        /// 获取日期范围内的转移统计
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>转移统计列表</returns>
        public async Task<List<DailyTransferStatistics>> GetDailyTransferStatisticsAsync(DateTime startDate, DateTime endDate)
        {
            return await _dbContext.Set<UserTransfer>()
                .Where(ut => ut.TransferTime >= startDate && ut.TransferTime <= endDate)
                .GroupBy(ut => ut.TransferTime.Date)
                .Select(g => new DailyTransferStatistics
                {
                    Date = g.Key,
                    TransferCount = g.Count(),
                    UniqueUsers = g.Select(ut => ut.UserId).Distinct().Count(),
                    UniqueOperators = g.Select(ut => ut.OperatorId).Distinct().Count()
                })
                .OrderBy(s => s.Date)
                .ToListAsync();
        }

        /// <summary>
        /// 转移统计信息类
        /// </summary>
        public class TransferStatistics
        {
            public int TotalTransfers { get; set; }
            public int UniqueUsers { get; set; }
            public int UniqueOperators { get; set; }
        }

        /// <summary>
        /// 根据ID获取用户转移记录
        /// </summary>
        /// <param name="id">转移记录ID</param>
        /// <returns>转移记录</returns>
        public async Task<UserTransfer?> GetByIdAsync(int id)
        {
            return await _dbContext.Set<UserTransfer>().FindAsync(id);
        }

        /// <summary>
        /// 获取分页用户转移记录列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<UserTransfer>> GetPagedListAsync(Queryable queryable)
        {
            return await GetPageDataAsync(queryable, q => q.OrderByDescending(x => x.TransferTime));
        }

        /// <summary>
        /// 批量转移用户
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="toEmployeeId">新员工ID</param>
        /// <param name="operatorId">操作人ID</param>
        /// <param name="reason">转移原因</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BatchTransferUsersAsync(List<string> userIds, string toEmployeeId, string operatorId, string? reason = null)
        {
            var transfers = userIds.Select(userId => new UserTransfer
            {
                UserId = userId,
                ToEmployeeId = toEmployeeId,
                OperatorId = operatorId,
                Reason = reason,
                TransferTime = DateTime.Now
            }).ToList();

            await _dbContext.Set<UserTransfer>().AddRangeAsync(transfers);
            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 根据用户ID获取转移记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>转移记录列表</returns>
        public async Task<List<UserTransfer>> GetByUserIdAsync(string userId)
        {
            return await _dbContext.Set<UserTransfer>()
                .Where(ut => ut.UserId == userId)
                .OrderByDescending(ut => ut.TransferTime)
                .ToListAsync();
        }

        /// <summary>
        /// 根据员工ID获取转移记录
        /// </summary>
        /// <param name="employeeId">员工ID</param>
        /// <returns>转移记录列表</returns>
        public async Task<List<UserTransfer>> GetByEmployeeIdAsync(string employeeId)
        {
            return await _dbContext.Set<UserTransfer>()
                .Where(ut => ut.FromEmployeeId == employeeId || ut.ToEmployeeId == employeeId)
                .OrderByDescending(ut => ut.TransferTime)
                .ToListAsync();
        }

        /// <summary>
        /// 根据操作人ID获取转移记录
        /// </summary>
        /// <param name="operatorId">操作人ID</param>
        /// <returns>转移记录列表</returns>
        public async Task<List<UserTransfer>> GetByOperatorIdAsync(string operatorId)
        {
            return await _dbContext.Set<UserTransfer>()
                .Where(ut => ut.OperatorId == operatorId)
                .OrderByDescending(ut => ut.TransferTime)
                .ToListAsync();
        }

        /// <summary>
        /// 日转移统计类
        /// </summary>
        public class DailyTransferStatistics
        {
            public DateTime Date { get; set; }
            public int TransferCount { get; set; }
            public int UniqueUsers { get; set; }
            public int UniqueOperators { get; set; }
        }
    }
}
