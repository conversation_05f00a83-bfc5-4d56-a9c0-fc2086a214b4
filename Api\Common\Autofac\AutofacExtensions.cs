using Autofac;
using System.Reflection;

namespace Common.Autofac
{
    public static class AutofacExtensions
    {
        public static void RegisterByAttribute(this ContainerBuilder builder, params Assembly[] assemblies)
        {
            var types = assemblies
                .SelectMany(a => a.GetTypes())
                .Where(type =>
                    type.IsClass &&
                    !type.IsAbstract &&
                    !type.IsGenericType &&
                    type.GetConstructors().Length > 0 &&
                    type.GetCustomAttribute<DependencyAttribute>() != null);

            foreach (var type in types)
            {
                try
                {
                    var attribute = type.GetCustomAttribute<DependencyAttribute>();
                    var interfaces = type.GetInterfaces();

                    if (attribute == null)
                    {
                        // 如果没有特性，使用默认的 Scoped 生命周期
                        // 获取注册的服务接口
                        var serviceTypes = interfaces.Length > 0
                            ? interfaces
                            : [type];

                        foreach (var serviceType in serviceTypes)
                        {
                            builder.RegisterType(type)
                                  .As(serviceType)
                                  .InstancePerLifetimeScope();
                        }
                        continue;
                    }

                    // 有 [Dependency] 特性的类，既注册为接口类型，也注册为自身类型
                    var registrationBuilder = builder.RegisterType(type);

                    // 注册为自身类型
                    switch (attribute.DependencyType)
                    {
                        case DependencyType.Singleton:
                            registrationBuilder.AsSelf().SingleInstance();
                            break;
                        case DependencyType.Scoped:
                            registrationBuilder.AsSelf().InstancePerLifetimeScope();
                            break;
                        case DependencyType.Transient:
                            registrationBuilder.AsSelf().InstancePerDependency();
                            break;
                    }

                    // 如果有接口，也注册为接口类型
                    if (interfaces.Length > 0)
                    {
                        foreach (var interfaceType in interfaces)
                        {
                            var interfaceRegistrationBuilder = builder.RegisterType(type).As(interfaceType);

                            switch (attribute.DependencyType)
                            {
                                case DependencyType.Singleton:
                                    interfaceRegistrationBuilder.SingleInstance();
                                    break;
                                case DependencyType.Scoped:
                                    interfaceRegistrationBuilder.InstancePerLifetimeScope();
                                    break;
                                case DependencyType.Transient:
                                    interfaceRegistrationBuilder.InstancePerDependency();
                                    break;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 记录错误但继续注册其他类型
                    System.Diagnostics.Debug.WriteLine($"Error registering type {type.FullName}: {ex.Message}");
                }
            }
        }

        public static void RegisterByConvention(this ContainerBuilder builder, params Assembly[] assemblies)
        {
            try
            {
                // 注册所有以Service结尾的类
                builder.RegisterAssemblyTypes(assemblies)
                    .Where(t => t.Name.EndsWith("Service") && !t.IsAbstract && t.GetConstructors().Length > 0)
                    // .AsImplementedInterfaces() // 注册为接口类型
                    .AsSelf() // 注册为自身类型
                    .InstancePerLifetimeScope();

                // 注册所有以Repository结尾的类
                builder.RegisterAssemblyTypes(assemblies)
                    .Where(t => t.Name.EndsWith("Repository") && !t.IsAbstract && t.GetConstructors().Length > 0)
                    .AsImplementedInterfaces()
                    .InstancePerLifetimeScope();

                // 注册所有以Manager结尾的类
                builder.RegisterAssemblyTypes(assemblies)
                    .Where(t => t.Name.EndsWith("Manager") && !t.IsAbstract && t.GetConstructors().Length > 0)
                    .AsImplementedInterfaces()
                    .InstancePerLifetimeScope();

                // 注册所有以Provider结尾的类
                builder.RegisterAssemblyTypes(assemblies)
                    .Where(t => t.Name.EndsWith("Provider") && !t.IsAbstract && t.GetConstructors().Length > 0)
                    .AsImplementedInterfaces()
                    .InstancePerLifetimeScope();

                // 注册所有以Helper结尾的类
                builder.RegisterAssemblyTypes(assemblies)
                    .Where(t => t.Name.EndsWith("Helper") && !t.IsAbstract && t.GetConstructors().Length > 0)
                    .AsSelf() // 注册为自身类型
                    .InstancePerLifetimeScope();

                // 添加：注册所有以DLL结尾的类
                builder.RegisterAssemblyTypes(assemblies)
                    .Where(t => t.Name.EndsWith("DLL") && !t.IsAbstract && t.GetConstructors().Length > 0)
                    // .AsImplementedInterfaces() // 注册为接口类型
                    .AsSelf() // 注册为自身类型
                    .InstancePerLifetimeScope();

                // 添加：注册所有以DAL结尾的类
                builder.RegisterAssemblyTypes(assemblies)
                    .Where(t => t.Name.EndsWith("DAL") && !t.IsAbstract && t.GetConstructors().Length > 0)
                    .AsSelf() // 注册为自身类型
                    .InstancePerLifetimeScope();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in convention-based registration: {ex.Message}");
                throw;
            }
        }

        public static void RegisterCommonServices(this ContainerBuilder builder)
        {
            // 注册常用服务
            builder.RegisterAssemblyTypes(Assembly.GetExecutingAssembly())
                .Where(t => t.GetConstructors().Length > 0)
                .AsImplementedInterfaces()
                .InstancePerLifetimeScope();
        }
    }
}