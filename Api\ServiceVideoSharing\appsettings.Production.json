{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "BasisConfig": {"DataBaseConnectionStrings": {"MysqlConnectionString": "Server=rm-bp1umss2l4q21v269.mysql.rds.aliyuncs.com;Port=33066;User=datamgrsystemuser;Password=************$*;Database=datamgrsystem"}, "Redis": {"Instances": [{"InstanceName": "main", "Connection": "r-bp1yikl0vm7a26orpg.tairpena.rds.aliyuncs.com:6379,password=r-bp1yikl0vm7a26orpg:!@#Meiyoumima$", "DefaultDb": 1}, {"InstanceName": "game", "Connection": "r-bp1yikl0vm7a26orpg.tairpena.rds.aliyuncs.com:6379,password=r-bp1yikl0vm7a26orpg:!@#Meiyoumima$", "DefaultDb": 0}]}, "JWT": {"sign": "aVeryLongRandomStringThatIsAtLeast32Characters", "exp": 60000, "Issuer": "AdminProjectTemplate", "Audience": "AdminProjectTemplate"}, "RabbitMQ": {"HostName": "127.0.0.1", "Port": 5672, "UserName": "guest", "Password": "guest", "VirtualHost": "my_vhost", "MaxRetryCount": 3, "ConnectionPoolSize": 5}, "LogService": {"Path": "https://localhost:5001/CreateLogInfo"}}}