-- 系统配置初始化脚本
-- 用于初始化微信登录推广链接和自动审核功能相关的系统配置

-- 插入自动审核配置
INSERT INTO system_configs (config_key, config_value, description, group_name, config_type, is_enabled, create_time) 
VALUES 
('AUTO_AUDIT_ENABLED', 'false', '是否开启用户自动审核：true=自动通过，false=需要手动审核', 'AUDIT', 'boolean', 1, NOW())
ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    group_name = VALUES(group_name),
    config_type = VALUES(config_type),
    is_enabled = VALUES(is_enabled);

-- 插入微信相关配置
INSERT INTO system_configs (config_key, config_value, description, group_name, config_type, is_enabled, create_time) 
VALUES 
('WECHAT_APP_ID', '', '微信小程序/公众号AppID', 'WECHAT', 'string', 1, NOW()),
('WECHAT_APP_SECRET', '', '微信小程序/公众号AppSecret', 'WECHAT', 'string', 1, NOW()),
('WECHAT_MCH_ID', '', '微信商户号ID', 'WECHAT', 'string', 1, NOW()),
('WECHAT_MCH_KEY', '', '微信商户号密钥', 'WECHAT', 'string', 1, NOW())
ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    group_name = VALUES(group_name),
    config_type = VALUES(config_type),
    is_enabled = VALUES(is_enabled);

-- 插入推广链接相关配置
INSERT INTO system_configs (config_key, config_value, description, group_name, config_type, is_enabled, create_time) 
VALUES 
('H5_BASE_URL', 'https://h5.example.com', 'H5页面基础URL，用于生成推广链接', 'PROMOTION', 'string', 1, NOW()),
('QR_CODE_ENABLED', 'true', '是否启用二维码生成功能', 'PROMOTION', 'boolean', 1, NOW())
ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    group_name = VALUES(group_name),
    config_type = VALUES(config_type),
    is_enabled = VALUES(is_enabled);

-- 插入视频相关配置
INSERT INTO system_configs (config_key, config_value, description, group_name, config_type, is_enabled, create_time) 
VALUES 
('VIDEO_COMPLETE_THRESHOLD', '80', '视频完播阈值（百分比），达到此阈值视为完播', 'VIDEO', 'number', 1, NOW()),
('REWARD_MIN_CORRECT_RATE', '50', '获得奖励的最低答题正确率（百分比）', 'VIDEO', 'number', 1, NOW())
ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    group_name = VALUES(group_name),
    config_type = VALUES(config_type),
    is_enabled = VALUES(is_enabled);

-- 插入审核相关配置
INSERT INTO system_configs (config_key, config_value, description, group_name, config_type, is_enabled, create_time) 
VALUES 
('AUDIT_TIMEOUT_DAYS', '7', '用户审核超时天数，超过此天数自动拒绝', 'AUDIT', 'number', 1, NOW()),
('AUDIT_NOTIFICATION_ENABLED', 'true', '是否启用审核通知功能', 'AUDIT', 'boolean', 1, NOW())
ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    group_name = VALUES(group_name),
    config_type = VALUES(config_type),
    is_enabled = VALUES(is_enabled);

-- 查询插入结果
SELECT 
    config_key,
    config_value,
    description,
    group_name,
    config_type,
    is_enabled,
    create_time
FROM system_configs 
WHERE group_name IN ('AUDIT', 'WECHAT', 'PROMOTION', 'VIDEO')
ORDER BY group_name, config_key;
