﻿using log4net;
using log4net.Config;

namespace Common.Log4Net
{
    public class LoggerHelper<T>
    {
        private static readonly ILog logger;
        static LoggerHelper()
        {
            try
            {
                // 获取应用程序的基础路径
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
                var configFile = Path.Combine(baseDirectory, "Config", "log4net.config");

                if (!File.Exists(configFile))
                {
                    throw new FileNotFoundException($"Log4net配置文件未找到，请确保配置文件存在于路径: {configFile}");
                }

                var repository = LogManager.CreateRepository(typeof(T).FullName);
                XmlConfigurator.Configure(repository, new FileInfo(configFile));
                logger = LogManager.GetLogger(repository.Name, typeof(T));
            }
            catch (Exception ex)
            {
                throw new Exception($"初始化日志系统时发生错误: {ex.Message}", ex);
            }
        }


        /// <summary>
        /// 普通日志
        /// </summary>
        /// <param name="message"></param>
        /// <param name="exception"></param>
        public static async Task InfoAsync(string message, Exception? exception = null) => await Task.Run(() => Info(message, exception));

        /// <summary>
        /// 普通日志
        /// </summary>
        /// <param name="message"></param>
        /// <param name="exception"></param>
        public static void Info(string message, Exception? exception = null)
        {
            if (exception == null)
                logger.Info(message);
            else
                logger.Info(message, exception);
        }


        /// <summary>
        /// 告警日志
        /// </summary>
        /// <param name="message"></param>
        /// <param name="exception"></param>
        public static async Task WarnAsync(string message, Exception? exception = null) => await Task.Run(() => Warn(message, exception));

        /// <summary>
        /// 告警日志
        /// </summary>
        /// <param name="message"></param>
        /// <param name="exception"></param>
        public static void Warn(string message, Exception? exception = null)
        {
            if (exception == null)
                logger.Warn(message);
            else
                logger.Warn(message, exception);
        }

        /// <summary>
        /// 错误日志
        /// </summary>
        /// <param name="message"></param>
        /// <param name="exception"></param>
        public static async Task ErrorAsync(string message, Exception? exception = null) => await Task.Run(() => Error(message, exception));

        /// <summary>
        /// 错误日志
        /// </summary>
        /// <param name="message"></param>
        /// <param name="exception"></param>
        public static void Error(string message, Exception? exception = null)
        {
            if (exception == null)
                logger.Error(message);
            else
                logger.Error(message, exception);
        }
    }
}
