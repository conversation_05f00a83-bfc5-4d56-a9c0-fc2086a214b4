using BLL.VideoService;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 微信访问令牌控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class WechatAccessTokenController(WechatAccessTokenService wechatAccessTokenService) : BaseController
    {
        private readonly WechatAccessTokenService _wechatAccessTokenService = wechatAccessTokenService;

        /// <summary>
        /// 创建微信访问令牌记录
        /// </summary>
        /// <param name="createDto">创建微信访问令牌DTO</param>
        /// <returns>令牌ID</returns>
        [HttpPost(Name = "WechatAccessToken_Create")]
        public async Task<Result<int>> CreateAccessToken([FromBody] WechatAccessTokenCreateDto createDto)
        {
            var tokenId = await _wechatAccessTokenService.CreateAccessTokenAsync(createDto, GetCurrentUserInfo());
            return Success(tokenId, "微信访问令牌创建成功");
        }

        /// <summary>
        /// 更新微信访问令牌记录
        /// </summary>
        /// <param name="updateDto">更新微信访问令牌DTO</param>
        /// <returns>是否成功</returns>
        [HttpPost("update", Name = "WechatAccessToken_Update")]
        public async Task<Result<bool>> UpdateAccessToken([FromBody] WechatAccessTokenUpdateDto updateDto)
        {
            var result = await _wechatAccessTokenService.UpdateAccessTokenAsync(updateDto, GetCurrentUserInfo());
            return Success(result, "微信访问令牌更新成功");
        }

        /// <summary>
        /// 停用/激活微信访问令牌
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <param name="isActive">是否激活</param>
        /// <returns>是否成功</returns>
        [HttpPost("{tokenId}/status")]
        public async Task<Result<bool>> ToggleTokenStatus(int tokenId, [FromBody] bool isActive)
        {
            var result = await _wechatAccessTokenService.ToggleTokenStatusAsync(tokenId, (byte)(isActive ? 1 : 0), GetCurrentUserInfo());
            return Success(result, isActive ? "令牌激活成功" : "令牌停用成功");
        }

        /// <summary>
        /// 获取微信访问令牌详情
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <returns>令牌详情</returns>
        [HttpGet("{tokenId}")]
        public async Task<Result<WechatAccessTokenResponseDto?>> GetAccessToken(int tokenId)
        {
            var token = await _wechatAccessTokenService.GetAccessTokenAsync(tokenId);
            return Success(token);
        }

        /// <summary>
        /// 分页查询微信访问令牌列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>令牌列表</returns>
        [HttpGet]
        public async Task<Result<PagedResult<WechatAccessTokenResponseDto>>> GetAccessTokenPagedList([FromQuery] WechatAccessTokenQueryDto queryDto)
        {
            var result = await _wechatAccessTokenService.GetAccessTokenPagedListAsync(queryDto);
            return Success(result);
        }

        /// <summary>
        /// 获取有效的访问令牌
        /// </summary>
        /// <param name="appId">微信应用ID</param>
        /// <param name="tokenType">令牌类型</param>
        /// <returns>有效的访问令牌字符串</returns>
        [HttpGet("valid")]
        public async Task<Result<string?>> GetValidAccessToken([FromQuery] string appId, [FromQuery] string tokenType = "access_token")
        {
            var token = await _wechatAccessTokenService.GetValidAccessTokenAsync(appId, tokenType);
            return Success(token, "获取有效访问令牌成功");
        }

        /// <summary>
        /// 获取应用的所有令牌
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="isActive">是否激活（可选）</param>
        /// <returns>令牌列表</returns>
        [HttpGet("app/{appId}")]
        public async Task<Result<List<WechatAccessTokenResponseDto>>> GetAppTokens(string appId, [FromQuery] bool? isActive = null)
        => Success(await _wechatAccessTokenService.GetAppTokensAsync(appId, isActive.HasValue ? (byte?)(isActive.Value ? 1 : 0) : null));

        /// <summary>
        /// 刷新访问令牌
        /// </summary>
        /// <param name="refreshDto">刷新令牌请求</param>
        /// <returns>是否成功</returns>
        [HttpPost("refresh")]
        public async Task<Result<bool>> RefreshAccessToken([FromBody] TokenRefreshRequestDto refreshDto)
        {
            if (string.IsNullOrEmpty(refreshDto.AppId) || string.IsNullOrEmpty(refreshDto.TokenType) || string.IsNullOrEmpty(refreshDto.NewAccessToken))
            {
                return Fail<bool>("应用ID、令牌类型和新访问令牌不能为空");
            }

            var result = await _wechatAccessTokenService.RefreshAccessTokenAsync(
                refreshDto.AppId,
                refreshDto.TokenType,
                refreshDto.NewAccessToken,
                refreshDto.ExpiresIn,
                GetCurrentUserInfo());
            return Success(result, "访问令牌刷新成功");
        }

        /// <summary>
        /// 批量刷新访问令牌
        /// </summary>
        /// <param name="refreshRequests">刷新请求列表</param>
        /// <returns>刷新结果</returns>
        [HttpPost("batch-refresh")]
        public async Task<Result<BatchRefreshResultDto>> BatchRefreshAccessTokens([FromBody] List<TokenRefreshRequestDto> refreshRequests)
        {
            var result = await _wechatAccessTokenService.BatchRefreshTokensAsync(refreshRequests, GetCurrentUserInfo());
            return Success(result, "批量刷新完成");
        }

        /// <summary>
        /// 获取令牌统计信息
        /// </summary>
        /// <returns>令牌统计</returns>
        [HttpGet("statistics")]
        public async Task<Result<WechatTokenStatisticsDto>> GetTokenStatistics()
        {
            var statistics = await _wechatAccessTokenService.GetTokenStatisticsAsync();
            return Success(statistics);
        }

        /// <summary>
        /// 获取即将过期的令牌列表
        /// </summary>
        /// <param name="bufferMinutes">缓冲时间（分钟）</param>
        /// <returns>即将过期的令牌列表</returns>
        [HttpGet("expiring")]
        public async Task<Result<List<WechatAccessTokenResponseDto>>> GetExpiringTokens([FromQuery] int bufferMinutes = 10)
        {
            var tokens = await _wechatAccessTokenService.GetExpiringTokensAsync(bufferMinutes);
            return Success(tokens);
        }

        /// <summary>
        /// 检查令牌是否即将过期
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="tokenType">令牌类型</param>
        /// <param name="bufferMinutes">缓冲时间（分钟）</param>
        /// <returns>是否即将过期</returns>
        [HttpGet("check-expiring")]
        public async Task<Result<bool>> IsTokenExpiring([FromQuery] string appId, [FromQuery] string tokenType = "access_token", [FromQuery] int bufferMinutes = 10)
        {
            var isExpiring = await _wechatAccessTokenService.IsTokenExpiringAsync(appId, tokenType, bufferMinutes);
            return Success(isExpiring);
        }

        /// <summary>
        /// 清理过期令牌
        /// </summary>
        /// <returns>清理的令牌数</returns>
        [HttpDelete("cleanup-expired")]
        public async Task<Result<int>> CleanupExpiredTokens()
        {
            var deletedCount = await _wechatAccessTokenService.CleanupExpiredTokensAsync(GetCurrentUserInfo());
            return Success(deletedCount, $"清理了 {deletedCount} 个过期令牌");
        }
    }
}
