using BLL.SysService;
using Common.Autofac;
using Common.Exceptions;
using DAL.VideoDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Microsoft.Extensions.Logging;

namespace BLL.VideoService
{
    /// <summary>
    /// 系统配置业务服务类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class SystemConfigService(SystemConfigDAL systemConfigDAL, SysLogService logService)
    {
        private readonly SystemConfigDAL _systemConfigDAL = systemConfigDAL;
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 创建系统配置
        /// </summary>
        /// <param name="createDto">创建配置DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>配置ID</returns>
        public async Task<int> CreateSystemConfigAsync(SystemConfigCreateDto createDto, CurrentUserInfoDto currentUserInfo)
        {
            // 检查配置键是否已存在
            if (await _systemConfigDAL.ExistsConfigKeyAsync(createDto.ConfigKey))
                throw new BusinessException($"配置键 {createDto.ConfigKey} 已存在");

            // 创建系统配置实体
            var systemConfig = new SystemConfig
            {
                ConfigKey = createDto.ConfigKey,
                ConfigValue = createDto.ConfigValue,
                ConfigType = createDto.ConfigType,
                Description = createDto.Description,
                IsEnabled = createDto.IsEnabled,
                CreateTime = DateTime.Now
            };

            // 添加配置
            await _systemConfigDAL.AddAsync(systemConfig);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "系统配置",
                Operation = "创建配置",
                BusinessObject = "SystemConfig",
                ObjectId = systemConfig.Id.ToString(),
                DetailedInfo = $"创建系统配置：{createDto.ConfigKey}",
                AfterData = systemConfig,
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return systemConfig.Id;
        }

        /// <summary>
        /// 更新系统配置
        /// </summary>
        /// <param name="updateDto">更新配置DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateSystemConfigAsync(SystemConfigUpdateDto updateDto, CurrentUserInfoDto currentUserInfo)
        {
            // 获取原配置信息
            var systemConfig = await _systemConfigDAL.GetByIdAsync(updateDto.Id)
                ?? throw new BusinessException("系统配置不存在");

            var beforeData = new { systemConfig.ConfigValue, systemConfig.ConfigType, systemConfig.Description, systemConfig.IsEnabled };

            // 检查配置键是否已存在（排除自己）
            if (await _systemConfigDAL.ExistsConfigKeyAsync(updateDto.ConfigKey, updateDto.Id))
                throw new BusinessException($"配置键 {updateDto.ConfigKey} 已存在");

            // 更新配置信息
            systemConfig.ConfigKey = updateDto.ConfigKey;
            systemConfig.ConfigValue = updateDto.ConfigValue;
            systemConfig.ConfigType = updateDto.ConfigType;
            systemConfig.Description = updateDto.Description;
            systemConfig.IsEnabled = updateDto.IsEnabled;

            var result = await _systemConfigDAL.UpdateAsync(systemConfig);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "系统配置",
                Operation = "更新配置",
                BusinessObject = "SystemConfig",
                ObjectId = systemConfig.Id.ToString(),
                DetailedInfo = $"更新系统配置：{updateDto.ConfigKey}",
                BeforeData = beforeData,
                AfterData = new { systemConfig.ConfigValue, systemConfig.ConfigType, systemConfig.Description, systemConfig.IsEnabled },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 删除系统配置
        /// </summary>
        /// <param name="configId">配置ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteSystemConfigAsync(int configId, CurrentUserInfoDto currentUserInfo)
        {
            // 获取配置信息
            var systemConfig = await _systemConfigDAL.GetByIdAsync(configId)
                ?? throw new BusinessException("系统配置不存在");

            var result = await _systemConfigDAL.DeleteAsync(systemConfig);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "系统配置",
                Operation = "删除配置",
                BusinessObject = "SystemConfig",
                ObjectId = configId.ToString(),
                DetailedInfo = $"删除系统配置：{systemConfig.ConfigKey}",
                BeforeData = systemConfig,
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 根据配置键获取配置值
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <returns>配置值</returns>
        public async Task<string?> GetConfigValueAsync(string configKey)
        {
            return await _systemConfigDAL.GetConfigValueAsync(configKey);
        }

        /// <summary>
        /// 根据配置键获取配置值（泛型）
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="configKey">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public async Task<T> GetConfigValueAsync<T>(string configKey, T defaultValue = default!)
        {
            var configValue = await _systemConfigDAL.GetConfigValueAsync(configKey);
            if (string.IsNullOrEmpty(configValue))
                return defaultValue;

            try
            {
                return (T)Convert.ChangeType(configValue, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <param name="configValue">配置值</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetConfigValueAsync(string configKey, string configValue, CurrentUserInfoDto currentUserInfo)
        {
            var config = await _systemConfigDAL.GetByConfigKeyAsync(configKey) ?? throw new BusinessException($"配置键 {configKey} 不存在");
            var beforeValue = config.ConfigValue;
            var result = await _systemConfigDAL.UpdateConfigValueAsync(configKey, configValue);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "系统配置",
                Operation = "设置配置值",
                BusinessObject = "SystemConfig",
                ObjectId = config.Id.ToString(),
                DetailedInfo = $"设置配置 {configKey} 的值",
                BeforeData = new { ConfigValue = beforeValue },
                AfterData = new { ConfigValue = configValue },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 启用/禁用配置
        /// </summary>
        /// <param name="configId">配置ID</param>
        /// <param name="isEnabled">是否启用</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ToggleConfigStatusAsync(int configId, byte isEnabled, CurrentUserInfoDto currentUserInfo)
        {
            var config = await _systemConfigDAL.GetByIdAsync(configId)
                ?? throw new BusinessException("系统配置不存在");

            var beforeStatus = config.IsEnabled;
            var result = await _systemConfigDAL.UpdateConfigStatusAsync(configId, isEnabled == 1);

            // 记录业务日志
            var statusText = isEnabled == 1 ? "启用" : "禁用";
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "系统配置",
                Operation = $"{statusText}配置",
                BusinessObject = "SystemConfig",
                ObjectId = configId.ToString(),
                DetailedInfo = $"{statusText}系统配置：{config.ConfigKey}",
                BeforeData = new { IsEnabled = beforeStatus },
                AfterData = new { IsEnabled = isEnabled },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 获取系统配置详情
        /// </summary>
        /// <param name="configId">配置ID</param>
        /// <returns>配置响应DTO</returns>
        public async Task<SystemConfigResponseDto?> GetSystemConfigAsync(int configId)
        {
            var systemConfig = await _systemConfigDAL.GetByIdAsync(configId);
            if (systemConfig == null) return null;

            return new SystemConfigResponseDto
            {
                Id = systemConfig.Id,
                ConfigKey = systemConfig.ConfigKey,
                ConfigValue = systemConfig.ConfigValue,
                ConfigType = systemConfig.ConfigType,
                Description = systemConfig.Description,
                IsEnabled = systemConfig.IsEnabled,
                CreateTime = systemConfig.CreateTime
            };
        }

        /// <summary>
        /// 分页查询系统配置列表
        /// </summary>
        /// <param name="queryDto">查询条件DTO</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<SystemConfigResponseDto>> GetSystemConfigPagedListAsync(SystemConfigQueryDto queryDto)
        {
            var queryable = new SystemConfigDAL.Queryable
            {
                ConfigKey = queryDto.ConfigKey,
                ConfigType = queryDto.ConfigType,
                IsEnabled = queryDto.IsEnabled.HasValue ? queryDto.IsEnabled == 1 : null,
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            var result = await _systemConfigDAL.GetPagedListAsync(queryable);

            var responseList = (result.Items ?? []).Select(config => new SystemConfigResponseDto
            {
                Id = config.Id,
                ConfigKey = config.ConfigKey,
                ConfigValue = config.ConfigValue,
                ConfigType = config.ConfigType,
                Description = config.Description,
                IsEnabled = config.IsEnabled,
                CreateTime = config.CreateTime
            }).ToList();

            return new PagedResult<SystemConfigResponseDto>
            {
                Items = responseList,
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }

        /// <summary>
        /// 获取所有启用的配置
        /// </summary>
        /// <returns>配置字典</returns>
        public async Task<Dictionary<string, string>> GetAllEnabledConfigsAsync()
        {
            var configs = await _systemConfigDAL.GetAllEnabledConfigsAsync();
            return configs.ToDictionary(c => c.ConfigKey, c => c.ConfigValue ?? string.Empty);
        }

        /// <summary>
        /// 根据配置类型获取配置列表
        /// </summary>
        /// <param name="configType">配置类型</param>
        /// <param name="isEnabled">是否启用（可选）</param>
        /// <returns>配置列表</returns>
        public async Task<List<SystemConfigResponseDto>> GetConfigsByTypeAsync(string configType, byte? isEnabled = null)
        {
            var configs = await _systemConfigDAL.GetByConfigTypeAsync(configType);

            // 如果指定了启用状态，进行过滤
            if (isEnabled.HasValue)
            {
                configs = [.. configs.Where(c => c.IsEnabled == isEnabled.Value)];
            }

            return [.. configs.Select(config => new SystemConfigResponseDto
            {
                Id = config.Id,
                ConfigKey = config.ConfigKey,
                ConfigValue = config.ConfigValue,
                ConfigType = config.ConfigType,
                Description = config.Description,
                IsEnabled = config.IsEnabled,
                CreateTime = config.CreateTime
            })];
        }

        /// <summary>
        /// 批量更新配置
        /// </summary>
        /// <param name="configUpdates">配置更新列表</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> BatchUpdateConfigsAsync(Dictionary<string, string> configUpdates, CurrentUserInfoDto currentUserInfo)
        {
            // 获取需要更新的配置
            var configKeys = configUpdates.Keys.ToList();
            var configs = await _systemConfigDAL.GetListAsync(new SystemConfigDAL.Queryable());
            var configsToUpdate = configs.Where(c => configKeys.Contains(c.ConfigKey)).ToList();

            // 更新配置值
            foreach (var config in configsToUpdate)
            {
                if (configUpdates.ContainsKey(config.ConfigKey))
                {
                    config.ConfigValue = configUpdates[config.ConfigKey];
                }
            }

            var result = await _systemConfigDAL.BatchUpdateConfigsAsync(configsToUpdate);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "系统配置",
                Operation = "批量更新配置",
                BusinessObject = "SystemConfig",
                ObjectId = string.Join(",", configUpdates.Keys),
                DetailedInfo = $"批量更新 {configUpdates.Count} 个配置",
                AfterData = configUpdates,
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 根据配置键获取配置
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <returns>配置响应DTO</returns>
        public async Task<SystemConfigResponseDto?> GetSystemConfigByKeyAsync(string configKey)
        {
            var systemConfig = await _systemConfigDAL.GetByConfigKeyAsync(configKey);
            if (systemConfig == null) return null;

            return new SystemConfigResponseDto
            {
                Id = systemConfig.Id,
                ConfigKey = systemConfig.ConfigKey,
                ConfigValue = systemConfig.ConfigValue,
                ConfigType = systemConfig.ConfigType,
                Description = systemConfig.Description,
                GroupName = systemConfig.GroupName,
                IsEnabled = systemConfig.IsEnabled,
                CreateTime = systemConfig.CreateTime
            };
        }

        /// <summary>
        /// 获取所有系统配置
        /// </summary>
        /// <returns>配置列表</returns>
        public async Task<List<SystemConfigResponseDto>> GetAllSystemConfigsAsync()
        {
            var configs = await _systemConfigDAL.GetListAsync(new SystemConfigDAL.Queryable());
            return [.. configs.Select(config => new SystemConfigResponseDto
            {
                Id = config.Id,
                ConfigKey = config.ConfigKey,
                ConfigValue = config.ConfigValue,
                ConfigType = config.ConfigType,
                Description = config.Description,
                GroupName = config.GroupName,
                IsEnabled = config.IsEnabled,
                CreateTime = config.CreateTime
            })];
        }

        /// <summary>
        /// 根据分组获取配置
        /// </summary>
        /// <param name="groupName">配置分组</param>
        /// <returns>配置列表</returns>
        public async Task<List<SystemConfigResponseDto>> GetConfigsByGroupAsync(string groupName)
        {
            var configs = await _systemConfigDAL.GetByGroupAsync(groupName);
            return [.. configs.Select(config => new SystemConfigResponseDto
            {
                Id = config.Id,
                ConfigKey = config.ConfigKey,
                ConfigValue = config.ConfigValue,
                ConfigType = config.ConfigType,
                Description = config.Description,
                GroupName = config.GroupName,
                IsEnabled = config.IsEnabled,
                CreateTime = config.CreateTime
            })];
        }

        /// <summary>
        /// 获取所有配置分组
        /// </summary>
        /// <returns>分组列表</returns>
        public async Task<List<string>> GetConfigGroupsAsync()
        {
            return await _systemConfigDAL.GetAllGroupsAsync();
        }

        /// <summary>
        /// 获取微信相关配置
        /// </summary>
        /// <returns>微信配置字典</returns>
        public async Task<Dictionary<string, string>> GetWechatConfigsAsync()
        {
            return await _systemConfigDAL.GetWechatConfigAsync();
        }

        /// <summary>
        /// 获取红包相关配置
        /// </summary>
        /// <returns>红包配置字典</returns>
        public async Task<Dictionary<string, string>> GetRewardConfigsAsync()
        {
            return await _systemConfigDAL.GetRewardConfigAsync();
        }

        /// <summary>
        /// 获取系统相关配置
        /// </summary>
        /// <returns>系统配置字典</returns>
        public async Task<Dictionary<string, string>> GetSystemConfigsAsync()
        {
            return await _systemConfigDAL.GetSystemConfigAsync();
        }

        /// <summary>
        /// 更新配置状态
        /// </summary>
        /// <param name="configId">配置ID</param>
        /// <param name="isEnabled">是否启用</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateConfigStatusAsync(int configId, byte isEnabled, CurrentUserInfoDto currentUserInfo)
        {
            // 获取原配置信息
            var config = await _systemConfigDAL.GetByIdAsync(configId) ?? throw new BusinessException("系统配置不存在");
            var beforeStatus = config.IsEnabled;
            var result = await _systemConfigDAL.UpdateConfigStatusAsync(configId, isEnabled == 1);

            // 记录业务日志
            var statusText = isEnabled == 1 ? "启用" : "禁用";
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "系统配置",
                Operation = $"{statusText}配置",
                BusinessObject = "SystemConfig",
                ObjectId = configId.ToString(),
                DetailedInfo = $"{statusText}系统配置：{config.ConfigKey}",
                BeforeData = new { IsEnabled = beforeStatus },
                AfterData = new { IsEnabled = isEnabled },
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return result;
        }

        /// <summary>
        /// 检查是否开启自动审核
        /// </summary>
        /// <returns>是否开启自动审核</returns>
        public async Task<bool> IsAutoAuditEnabledAsync()
        {
            return await GetConfigValueAsync<bool>("AUTO_AUDIT_ENABLED", false);
        }

        /// <summary>
        /// 设置自动审核开关
        /// </summary>
        /// <param name="enabled">是否开启</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SetAutoAuditEnabledAsync(bool enabled, CurrentUserInfoDto currentUserInfo)
        {
            return await SetConfigValueAsync("AUTO_AUDIT_ENABLED", enabled.ToString().ToLower(), currentUserInfo);
        }
    }
}
