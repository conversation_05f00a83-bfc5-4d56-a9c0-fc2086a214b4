using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.VideoDAL
{
    /// <summary>
    /// 微信访问令牌数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class WechatAccessTokenDAL(MyContext context) : BaseQueryDLL<WechatAccessToken, WechatAccessTokenDAL.Queryable>(context)
    {

        /// <summary>
        /// 微信访问令牌查询条件模型类
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 应用ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? AppId { get; set; }

            /// <summary>
            /// 令牌类型(模糊查询)
            /// </summary>
            [Query(QueryOperator.包含)]
            public string? TokenType { get; set; }

            /// <summary>
            /// 是否过期:0未过期,1已过期
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? IsExpired { get; set; }

            /// <summary>
            /// 是否激活
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? IsActive { get; set; }

            /// <summary>
            /// 开始时间
            /// </summary>
            [Query(QueryOperator.日期范围, relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 结束时间
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 排序字段
            /// </summary>
            [Query(QueryOperator.排序, orderDirection: OrderDirection.降序, orderPriority: 1)]
            public DateTime? CreateTime { get; set; }
        }

        /// <summary>
        /// 根据令牌类型获取最新的访问令牌
        /// </summary>
        /// <param name="tokenType">令牌类型</param>
        /// <returns>访问令牌</returns>
        public async Task<WechatAccessToken?> GetLatestTokenAsync(string tokenType)
        {
            return await _dbContext.Set<WechatAccessToken>()
                .Where(wat => wat.TokenType == tokenType)
                .OrderByDescending(wat => wat.CreateTime)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据令牌类型获取有效的访问令牌
        /// </summary>
        /// <param name="tokenType">令牌类型</param>
        /// <returns>有效的访问令牌</returns>
        public async Task<WechatAccessToken?> GetValidTokenAsync(string tokenType)
        {
            var now = DateTime.Now;
            return await _dbContext.Set<WechatAccessToken>()
                .Where(wat => wat.TokenType == tokenType && wat.ExpiresAt > now && wat.IsValid == 1)
                .OrderByDescending(wat => wat.CreateTime)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 检查令牌是否存在且有效
        /// </summary>
        /// <param name="tokenType">令牌类型</param>
        /// <returns>是否有效</returns>
        public async Task<bool> IsTokenValidAsync(string tokenType)
        {
            var token = await GetValidTokenAsync(tokenType);
            return token != null;
        }

        /// <summary>
        /// 标记令牌为过期
        /// </summary>
        /// <param name="tokenType">令牌类型</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ExpireTokenAsync(string tokenType)
        {
            var tokens = await _dbContext.Set<WechatAccessToken>()
                .Where(wat => wat.TokenType == tokenType && wat.IsValid == 1)
                .ToListAsync();

            foreach (var token in tokens)
            {
                token.IsValid = 0;
            }

            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 清理过期的令牌
        /// </summary>
        /// <param name="beforeDate">清理此日期之前的令牌</param>
        /// <returns>清理的记录数</returns>
        public async Task<int> CleanExpiredTokensAsync(DateTime? beforeDate = null)
        {
            var cutoffDate = beforeDate ?? DateTime.Now.AddDays(-30); // 默认清理30天前的过期令牌

            var expiredTokens = await _dbContext.Set<WechatAccessToken>()
                .Where(wat => wat.IsExpired == 1 && wat.CreateTime < cutoffDate)
                .ToListAsync();

            if (expiredTokens.Count > 0)
            {
                _dbContext.Set<WechatAccessToken>().RemoveRange(expiredTokens);
                await _dbContext.SaveChangesAsync();
            }

            return expiredTokens.Count;
        }

        /// <summary>
        /// 获取所有令牌类型
        /// </summary>
        /// <returns>令牌类型列表</returns>
        public async Task<List<string>> GetAllTokenTypesAsync()
        {
            return await _dbContext.Set<WechatAccessToken>()
                .Where(wat => wat.TokenType != null)
                .Select(wat => wat.TokenType!)
                .Distinct()
                .OrderBy(t => t)
                .ToListAsync();
        }

        /// <summary>
        /// 获取令牌统计信息
        /// </summary>
        /// <returns>令牌统计</returns>
        public async Task<TokenStatistics> GetTokenStatisticsAsync()
        {
            var now = DateTime.Now;

            var totalCount = await _dbContext.Set<WechatAccessToken>().CountAsync();
            var activeCount = await _dbContext.Set<WechatAccessToken>()
                .CountAsync(wat => wat.IsActive == 1);
            var validCount = await _dbContext.Set<WechatAccessToken>()
                .CountAsync(wat => wat.ExpiresAt > now && wat.IsValid == 1 && wat.IsActive == 1);
            var expiredCount = await _dbContext.Set<WechatAccessToken>()
                .CountAsync(wat => wat.IsExpired == 1);
            var expiringCount = await _dbContext.Set<WechatAccessToken>()
                .CountAsync(wat => wat.ExpiresAt <= now.AddMinutes(30) && wat.ExpiresAt > now && wat.IsActive == 1);
            var appCount = await _dbContext.Set<WechatAccessToken>()
                .Where(wat => !string.IsNullOrEmpty(wat.AppId))
                .Select(wat => wat.AppId)
                .Distinct()
                .CountAsync();

            // 获取Token类型统计
            var tokenTypeStats = await _dbContext.Set<WechatAccessToken>()
                .Where(wat => !string.IsNullOrEmpty(wat.TokenType))
                .GroupBy(wat => wat.TokenType)
                .ToDictionaryAsync(g => g.Key!, g => g.Count());

            return new TokenStatistics
            {
                TotalCount = totalCount,
                ActiveCount = activeCount,
                ValidCount = validCount,
                ExpiredCount = expiredCount,
                ExpiringCount = expiringCount,
                AppCount = appCount,
                TokenTypeStats = tokenTypeStats
            };
        }

        /// <summary>
        /// 根据令牌类型获取令牌统计
        /// </summary>
        /// <param name="tokenType">令牌类型</param>
        /// <returns>令牌类型统计</returns>
        public async Task<TokenTypeStatistics> GetTokenTypeStatisticsAsync(string tokenType)
        {
            var now = DateTime.Now;

            var totalCount = await _dbContext.Set<WechatAccessToken>()
                .CountAsync(wat => wat.TokenType == tokenType);
            var validCount = await _dbContext.Set<WechatAccessToken>()
                .CountAsync(wat => wat.TokenType == tokenType && wat.ExpiresAt > now && wat.IsValid == 1);
            var latestToken = await GetLatestTokenAsync(tokenType);

            return new TokenTypeStatistics
            {
                TokenType = tokenType,
                TotalCount = totalCount,
                ValidCount = validCount,
                LatestCreateTime = latestToken?.CreateTime,
                LatestExpiresTime = latestToken?.ExpiresAt
            };
        }

        /// <summary>
        /// 令牌统计信息类
        /// </summary>
        public class TokenStatistics
        {
            public int TotalCount { get; set; }
            public int ValidCount { get; set; }
            public int ActiveCount { get; set; }
            public int ExpiredCount { get; set; }
            public int ExpiringCount { get; set; }
            public int ExpiredByTimeCount { get; set; }
            public int AppCount { get; set; }
            public Dictionary<string, int> TokenTypeStats { get; set; } = [];
        }

        /// <summary>
        /// 根据ID获取微信访问令牌
        /// </summary>
        /// <param name="id">令牌ID</param>
        /// <returns>访问令牌</returns>
        public async Task<WechatAccessToken?> GetByIdAsync(int id)
        {
            return await _dbContext.Set<WechatAccessToken>().FindAsync(id);
        }

        /// <summary>
        /// 获取分页微信访问令牌列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<WechatAccessToken>> GetPagedListAsync(Queryable queryable)
        {
            return await GetPageDataAsync(queryable, q => q.OrderByDescending(x => x.CreateTime));
        }

        /// <summary>
        /// 刷新访问令牌
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="newAccessToken">新访问令牌</param>
        /// <param name="expiresIn">过期时间(秒)</param>
        /// <param name="refreshToken">刷新令牌</param>
        /// <returns>是否成功</returns>
        public async Task<bool> RefreshAccessTokenAsync(string appId, string newAccessToken, int expiresIn, string? refreshToken = null)
        {
            var token = await GetByAppIdAsync(appId);
            if (token == null) return false;

            var now = DateTime.Now;
            token.AccessToken = newAccessToken;
            token.ExpiresIn = expiresIn;
            token.ExpiresAt = now.AddSeconds(expiresIn);
            token.ExpiresTime = now.AddSeconds(expiresIn);
            token.RefreshToken = refreshToken;
            token.IsExpired = 0;
            token.IsActive = 1;
            token.IsValid = 1;

            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 更新令牌状态
        /// </summary>
        /// <param name="id">令牌ID</param>
        /// <param name="isActive">是否激活</param>
        /// <param name="isExpired">是否过期</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateTokenStatusAsync(int id, byte isActive, byte? isExpired = null)
        {
            var token = await GetByIdAsync(id);
            if (token == null) return false;

            token.IsActive = isActive;
            if (isExpired.HasValue)
                token.IsExpired = isExpired.Value;

            return await _dbContext.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 获取有效的访问令牌
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>有效的访问令牌</returns>
        public async Task<WechatAccessToken?> GetValidAccessTokenAsync(string? appId = null)
        {
            var now = DateTime.Now;
            var query = _dbContext.Set<WechatAccessToken>()
                .Where(t => t.IsExpired == 0 && t.IsActive == 1 && t.IsValid == 1 && t.ExpiresAt > now);

            if (!string.IsNullOrEmpty(appId))
            {
                query = query.Where(t => t.AppId == appId);
            }

            return await query
                .OrderByDescending(t => t.CreateTime)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 检查令牌是否即将过期
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <param name="minutesBeforeExpiry">过期前分钟数</param>
        /// <returns>是否即将过期</returns>
        public async Task<bool> IsTokenExpiringAsync(string appId, int minutesBeforeExpiry = 30)
        {
            var expiryTime = DateTime.Now.AddMinutes(minutesBeforeExpiry);
            return await _dbContext.Set<WechatAccessToken>()
                .AnyAsync(w => w.AppId == appId && w.IsActive == 1 && w.IsValid == 1 && w.ExpiresAt <= expiryTime && w.ExpiresAt > DateTime.Now);
        }

        /// <summary>
        /// 根据应用ID获取令牌
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>微信访问令牌</returns>
        public async Task<WechatAccessToken?> GetByAppIdAsync(string appId)
        {
            return await _dbContext.Set<WechatAccessToken>()
                .Where(w => w.AppId == appId && w.IsActive == 1)
                .OrderByDescending(w => w.CreateTime)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取即将过期的令牌
        /// </summary>
        /// <param name="minutesBeforeExpiry">过期前分钟数</param>
        /// <returns>即将过期的令牌列表</returns>
        public async Task<List<WechatAccessToken>> GetExpiringTokensAsync(int minutesBeforeExpiry = 30)
        {
            var now = DateTime.Now;
            var expiryTime = now.AddMinutes(minutesBeforeExpiry);
            return await _dbContext.Set<WechatAccessToken>()
                .Where(w => w.IsActive == 1 && w.IsValid == 1 && w.ExpiresAt <= expiryTime && w.ExpiresAt > now)
                .ToListAsync();
        }

        /// <summary>
        /// 清理过期令牌
        /// </summary>
        /// <param name="daysToKeep">保留天数</param>
        /// <returns>清理的令牌数量</returns>
        public async Task<int> CleanupExpiredTokensAsync(int daysToKeep = 30)
        {
            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
            var expiredTokens = await _dbContext.Set<WechatAccessToken>()
                .Where(w => w.ExpiresAt < cutoffDate || (w.IsExpired == 1 && w.CreateTime < cutoffDate))
                .ToListAsync();

            if (expiredTokens.Count > 0)
            {
                _dbContext.Set<WechatAccessToken>().RemoveRange(expiredTokens);
                await _dbContext.SaveChangesAsync();
            }

            return expiredTokens.Count;
        }

        /// <summary>
        /// 令牌类型统计类
        /// </summary>
        public class TokenTypeStatistics
        {
            public string TokenType { get; set; } = string.Empty;
            public int TotalCount { get; set; }
            public int ValidCount { get; set; }
            public DateTime? LatestCreateTime { get; set; }
            public DateTime? LatestExpiresTime { get; set; }
        }
    }
}
