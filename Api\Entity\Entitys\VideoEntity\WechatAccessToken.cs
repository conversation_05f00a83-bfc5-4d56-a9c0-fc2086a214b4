using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.VideoEntity
{
    /// <summary>
    /// 微信AccessToken记录表
    /// </summary>
    [Table("wechat_access_tokens")]
    public class WechatAccessToken : BaseEntity_ID
    {
        /// <summary>
        /// 微信AppID
        /// </summary>
        [Required]
        [MaxLength(100)]
        [Comment("微信AppID")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// AccessToken
        /// </summary>
        [Required]
        [MaxLength(512)]
        [Comment("AccessToken")]
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// 过期时间
        /// </summary>
        [Comment("过期时间")]
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// 过期时间（秒）
        /// </summary>
        [Comment("过期时间（秒）")]
        public int? ExpiresIn { get; set; }

        /// <summary>
        /// 过期时间戳
        /// </summary>
        [Comment("过期时间戳")]
        public DateTime? ExpiresTime { get; set; }

        /// <summary>
        /// 刷新Token
        /// </summary>
        [MaxLength(512)]
        [Comment("刷新Token")]
        public string? RefreshToken { get; set; }

        /// <summary>
        /// 授权范围
        /// </summary>
        [MaxLength(200)]
        [Comment("授权范围")]
        public string? Scope { get; set; }

        /// <summary>
        /// Token类型
        /// </summary>
        [MaxLength(50)]
        [Comment("Token类型")]
        public string? TokenType { get; set; }

        /// <summary>
        /// 是否有效:0无效,1有效
        /// </summary>
        [Comment("是否有效:0无效,1有效")]
        public byte IsValid { get; set; } = 1;

        /// <summary>
        /// 是否激活:0未激活,1已激活
        /// </summary>
        [Comment("是否激活:0未激活,1已激活")]
        public byte IsActive { get; set; } = 1;

        /// <summary>
        /// 是否过期:0未过期,1已过期
        /// </summary>
        [Comment("是否过期:0未过期,1已过期")]
        public byte IsExpired { get; set; } = 0;

        /// <summary>
        /// 获取时间
        /// </summary>
        [Comment("获取时间")]
        public DateTime ObtainTime { get; set; } = DateTime.Now;
    }
}
