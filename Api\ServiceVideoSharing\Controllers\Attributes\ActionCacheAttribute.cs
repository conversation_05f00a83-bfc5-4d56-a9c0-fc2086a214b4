﻿using Common.Redis;

namespace ServiceVideoSharing.Controllers.Attributes
{
    public enum Whether
    {
        是 = 1,
        否 = 0
    }

    /// <summary>
    /// 缓存类型枚举
    /// </summary>
    public enum CacheType
    {
        内存 = 0,
        Redis = 1
    }

    /// <summary>
    /// 缓存过期类型
    /// </summary>
    public enum CacheExpireType
    {
        固定时间 = 0,    // 从缓存创建后固定时间过期
        绝对时间 = 1,    // 在指定时间点过期（如每天0点）
        滑动过期 = 2     // 最后访问后多久过期
    }

    /// <summary>
    /// 缓存范围枚举
    /// </summary>
    public enum CacheScope
    {
        全局 = 0,     // 所有用户共享
        用户 = 1,     // 特定用户专用
        角色 = 2,     // 按角色缓存
        部门 = 3      // 按部门缓存
    }

    /// <summary>
    /// 缓存Action [配置特性]
    /// </summary>
    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
    public class ActionCacheAttribute : Attribute
    {
        /// <summary>
        /// 是否在缓存Key内添加用户信息
        /// </summary>
        public Whether IsToken { get; }

        /// <summary>
        /// 缓存时间(秒)
        /// </summary>
        public int CacheSeconds { get; }

        /// <summary>
        /// 缓存类型
        /// </summary>
        public CacheType CacheType { get; }

        /// <summary>
        /// Redis实例名
        /// </summary>
        public string? RedisInstance { get; }

        /// <summary>
        /// 缓存前缀
        /// </summary>
        public string CachePrefix { get; }

        /// <summary>
        /// 缓存范围
        /// </summary>
        public CacheScope CacheScope { get; }

        /// <summary>
        /// 过期类型
        /// </summary>
        public CacheExpireType ExpireType { get; }

        /// <summary>
        /// 时间范围(绝对时间模式下使用)
        /// </summary>
        public RedisHelper.TimeScope TimeScope { get; }

        /// <summary>
        /// 时间范围计数(绝对时间模式下使用)
        /// </summary>
        public int TimeScopeCount { get; }

        /// <summary>
        /// 是否使用Hash结构存储
        /// </summary>
        public bool UseHashStructure { get; }

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public ActionCacheAttribute()
        {
            CacheSeconds = 10;
            CacheType = CacheType.内存;
            CachePrefix = "API:";
            CacheScope = CacheScope.全局;
            IsToken = Whether.否;
            ExpireType = CacheExpireType.固定时间;
            TimeScope = RedisHelper.TimeScope.分钟;
            TimeScopeCount = 1;
            UseHashStructure = true;
        }

        /// <summary>
        /// 构造函数 - 基本参数
        /// </summary>
        /// <param name="cacheTime">缓存时间[秒]</param>
        /// <param name="isToken">是否需要将Token加入缓存Key</param>
        public ActionCacheAttribute(int cacheTime, Whether isToken = Whether.否)
        {
            CacheSeconds = cacheTime;
            IsToken = isToken;
            CacheType = CacheType.内存;
            CachePrefix = "API:";
            CacheScope = CacheScope.全局;
            ExpireType = CacheExpireType.固定时间;
            TimeScope = RedisHelper.TimeScope.分钟;
            TimeScopeCount = 1;
            UseHashStructure = true;
        }

        /// <summary>
        /// 构造函数 - 完整参数
        /// </summary>
        /// <param name="cacheTime">缓存时间[秒]</param>
        /// <param name="cacheType">缓存类型(内存/Redis)</param>
        /// <param name="cacheScope">缓存范围(全局/用户/角色/部门)</param>
        /// <param name="redisInstance">Redis实例名称</param>
        /// <param name="isToken">是否需要将Token加入缓存Key</param>
        /// <param name="expireType">过期类型</param>
        /// <param name="useHashStructure">是否使用Hash结构</param>
        public ActionCacheAttribute(
            int cacheTime,
            CacheType cacheType = CacheType.内存,
            CacheScope cacheScope = CacheScope.全局,
            string? redisInstance = null,
            Whether isToken = Whether.否,
            CacheExpireType expireType = CacheExpireType.固定时间,
            bool useHashStructure = true)
        {
            CacheSeconds = cacheTime;
            CacheType = cacheType;
            CacheScope = cacheScope;
            RedisInstance = redisInstance;
            IsToken = isToken;
            ExpireType = expireType;
            TimeScope = RedisHelper.TimeScope.分钟;
            TimeScopeCount = 1;
            CachePrefix = "API:";
            UseHashStructure = useHashStructure;
        }

        /// <summary>
        /// 构造函数 - 绝对时间模式
        /// </summary>
        /// <param name="timeScope">时间范围</param>
        /// <param name="timeScopeCount">时间范围计数</param>
        /// <param name="cacheType">缓存类型(内存/Redis)</param>
        /// <param name="cacheScope">缓存范围(全局/用户/角色/部门)</param>
        /// <param name="redisInstance">Redis实例名称</param>
        /// <param name="isToken">是否需要将Token加入缓存Key</param>
        /// <param name="useHashStructure">是否使用Hash结构</param>
        public ActionCacheAttribute(
            RedisHelper.TimeScope timeScope,
            int timeScopeCount = 1,
            CacheType cacheType = CacheType.内存,
            CacheScope cacheScope = CacheScope.全局,
            string? redisInstance = null,
            Whether isToken = Whether.否,
            bool useHashStructure = true)
        {
            TimeScope = timeScope;
            TimeScopeCount = timeScopeCount;
            CacheType = cacheType;
            CacheScope = cacheScope;
            RedisInstance = redisInstance;
            IsToken = isToken;
            ExpireType = CacheExpireType.绝对时间;
            CachePrefix = "API:";
            UseHashStructure = useHashStructure;
            CacheSeconds = 60; // 默认值，在绝对时间模式下不使用
        }
    }
}
