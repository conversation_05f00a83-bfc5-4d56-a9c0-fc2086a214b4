using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.VideoEntity
{
    /// <summary>
    /// 用户表 - 简化版，只保留核心用户信息
    /// </summary>
    [Table("users")]
    public class User : BaseEntity_GUID
    {
        /// <summary>
        /// 微信OpenID
        /// </summary>
        [MaxLength(100)]
        [Comment("微信OpenID")]
        public string? OpenId { get; set; }

        /// <summary>
        /// 微信UnionID
        /// </summary>
        [MaxLength(100)]
        [Comment("微信UnionID")]
        public string? UnionId { get; set; }

        /// <summary>
        /// 微信昵称
        /// </summary>
        [MaxLength(100)]
        [Comment("微信昵称")]
        public string? Nickname { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        [MaxLength(255)]
        [Comment("头像URL")]
        public string? Avatar { get; set; }

        /// <summary>
        /// 绑定的员工ID（关联SysUser表）
        /// </summary>
        [Comment("绑定的员工ID")]
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        [Comment("最后登录时间")]
        public DateTime? LastLogin { get; set; }
    }
}
