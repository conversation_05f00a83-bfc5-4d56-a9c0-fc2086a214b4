namespace DAL.Databases
{

    /// <summary>
    /// 查询条件类型枚举
    /// 定义了所有支持的查询操作符类型
    /// </summary>
    public enum QueryOperator
    {
        等于,          // 等于：精确匹配，如 Name == "张三"
        包含,       // 包含：字符串包含，如 Name.Contains("张")
        大于,    // 大于：数值比较，如 Age > 18
        小于,       // 小于：数值比较，如 Age < 60
        大于等于, // 大于等于：数值比较，如 Age >= 18
        小于等于,    // 小于等于：数值比较，如 Age <= 60
        日期范围,      // 日期范围：日期区间查询，如 CreateTime 在 StartDate 和 EndDate 之间
        包含于,             // 包含于：集合包含，如 Id in (1,2,3)
        不等于,       // 不等于：不等于比较，如 Status != 0
        开头是,     // 开头是：字符串前缀匹配，如 Name.StartsWith("张")
        结尾是,       // 结尾是：字符串后缀匹配，如 Name.EndsWith("三")
        不包含于,     // 不包含于：集合不包含，如 Id not in (1,2,3)
        排序         // 排序：用于标记排序字段
    }

    /// <summary>
    /// 排序方向枚举
    /// </summary>
    public enum OrderDirection
    {
        升序,      // 升序
        降序      // 降序
    }

    /// <summary>
    /// 查询特性
    /// 用于标记查询模型中的属性，指定查询方式和配置
    /// </summary>
    /// <remarks>
    /// 构造函数
    /// </remarks>
    /// <param name="op">查询操作符</param>
    /// <param name="columnName">数据库列名</param>
    /// <param name="relatedProperty">关联属性</param>
    /// <param name="ignoreNull">是否忽略空值</param>
    /// <param name="orderDirection">排序方向</param>
    /// <param name="orderPriority">排序优先级</param>
    [AttributeUsage(AttributeTargets.Property)]  // 该特性只能用于属性
    public class QueryAttribute(QueryOperator op, string columnName = "", string relatedProperty = "", bool ignoreNull = true,
        OrderDirection orderDirection = OrderDirection.升序, int orderPriority = 0) : Attribute
    {
        /// <summary>
        /// 查询操作符
        /// 指定该属性使用什么操作符进行查询
        /// </summary>
        public QueryOperator Operator { get; } = op;

        /// <summary>
        /// 关联属性
        /// 用于某些需要联其他属性的查询，如日期范围查询需要结束日期
        /// </summary>
        public string RelatedProperty { get; } = relatedProperty;

        /// <summary>
        /// 是否忽略空值
        /// 当属性值为null时，是否跳过该条件的查询
        /// </summary>
        public bool IgnoreNull { get; } = ignoreNull;

        /// <summary>
        /// 数据库列名
        /// 当实体属性名与数据库字段名不一致时，指定数据库中的真实字段名
        /// </summary>
        public string ColumnName { get; } = columnName;

        /// <summary>
        /// 排序方向
        /// 当Operator为OrderBy时生效
        /// </summary>
        public OrderDirection OrderDirection { get; } = orderDirection;

        /// <summary>
        /// 排序优先级
        /// 数值越小优先级越高，默认为0
        /// </summary>
        public int OrderPriority { get; } = orderPriority;
    }
}