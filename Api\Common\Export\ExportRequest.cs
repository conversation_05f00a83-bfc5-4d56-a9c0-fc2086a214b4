namespace Common.Export
{
    /// <summary>
    /// 导出请求
    /// </summary>
    public class ExportRequest
    {
        /// <summary>
        /// 导出类型
        /// </summary>
        public string Type { get; set; } = "csv";

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 导出列配置
        /// </summary>
        public List<ExportColumn> Columns { get; set; } = [];

        /// <summary>
        /// 筛选条件
        /// </summary>
        public Dictionary<string, object> Filters { get; set; } = [];
    }
}