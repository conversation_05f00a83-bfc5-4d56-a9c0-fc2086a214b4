using BLL.Common;
using BLL.SysService;
using Common.Autofac;
using Common.Caches;
using Common.Exceptions;
using Common.JWT;
using DAL.SysDAL;
using DAL.VideoDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Entity.Extensions;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Entity.Entitys.SysEntity;
using Microsoft.EntityFrameworkCore;

namespace BLL.VideoService
{
    /// <summary>
    /// 批次业务服务类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class BatchService(
        BatchDAL batchDAL,
        VideoDAL videoDAL,
        SysLogService logService,
        UserBatchRecordDAL userBatchRecordDAL,
        UserDAL userDAL,
        SysUserDAL sysUserDAL) : BasePermissionService(userDAL, sysUserDAL)
    {
        private readonly BatchDAL _batchDAL = batchDAL;
        private readonly VideoDAL _videoDAL = videoDAL;
        private readonly SysLogService _logService = logService;
        private readonly UserBatchRecordDAL _userBatchRecordDAL = userBatchRecordDAL;

        /// <summary>
        /// 添加批次（带权限验证）
        /// </summary>
        /// <param name="createDto">创建批次DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>批次ID</returns>
        public async Task<int> AddBatchAsync(BatchCreateDto createDto, CurrentUserInfoDto currentUserInfo)
        {
            // 权限验证：转换CurrentUserInfoDto为UserInfo进行验证
            var userInfo = new UserInfo
            {
                UserId = currentUserInfo.UserId,
                UserName = currentUserInfo.UserName,
                UserType = currentUserInfo.UserType
            };
            ValidateUserTypePermission(userInfo, [1, 2, 3], "权限不足，无法创建批次");

            // 验证视频是否存在
            var video = await _videoDAL.GetByIdAsync(createDto.VideoId) ?? throw new BusinessException("指定的视频不存在");

            // 验证时间范围
            if (createDto.StartTime >= createDto.EndTime)
                throw new BusinessException("开始时间必须早于结束时间");

            // 创建批次实体
            var batch = new Batch
            {
                Name = createDto.Name,
                Description = createDto.Description,
                VideoId = createDto.VideoId,
                VideoTitle = video.Title, // 冗余存储视频标题
                VideoDescription = video.Description,
                VideoCoverUrl = video.CoverUrl,
                VideoUrl = video.VideoUrl,
                VideoDuration = video.Duration,
                RewardAmount = video.RewardAmount,
                Questions = video.Questions,
                StartTime = createDto.StartTime,
                EndTime = createDto.EndTime,
                RedPacketAmount = createDto.RedPacketAmount,
                Status = 1 // 默认未开始
            };
            batch.InitializeForAdd(currentUserInfo);
            // CreatedBy 已经在 InitializeForAdd 中设置，无需重复设置
            // 添加批次
            await _batchDAL.AddAsync(batch);

            // 记录业务日志 - 使用Task.Run避免上下文冲突
            _ = Task.Run(async () =>
            {
                try
                {
                    await _logService.LogBusinessOperationAsync(new BusinessLogDto
                    {
                        Module = "批次管理",
                        Operation = "创建批次",
                        BusinessObject = "Batch",
                        ObjectId = batch.Id.ToString(),
                        DetailedInfo = $"创建批次：{batch.Name}",
                        AfterData = batch,
                        UserId = currentUserInfo.UserId,
                        Username = currentUserInfo.UserName,
                        Level = LogLevel.Information
                    });
                }
                catch
                {
                    // 忽略日志记录失败，不影响主业务
                }
            });

            return batch.Id;
        }

        /// <summary>
        /// 删除批次（带权限验证）
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteBatchAsync(int batchId, CurrentUserInfoDto currentUserInfo)
        {
            // 权限验证：转换CurrentUserInfoDto为UserInfo进行验证
            var userInfo = new UserInfo
            {
                UserId = currentUserInfo.UserId,
                UserName = currentUserInfo.UserName,
                UserType = currentUserInfo.UserType
            };
            ValidateUserTypePermission(userInfo, [1, 2], "权限不足，只有管理员及以上级别可以删除批次");

            // 获取批次信息
            var batch = await _batchDAL.GetByIdAsync(batchId)
                ?? throw new BusinessException("批次不存在");

            // 权限检查：管理员只能删除自己权限范围内的批次
            if (userInfo.UserType == 2)
            {
                var accessibleEmployeeIds = await GetAccessibleEmployeeIdsAsync(userInfo);
                if (accessibleEmployeeIds != null && !accessibleEmployeeIds.Contains(batch.CreatedBy ?? ""))
                {
                    throw new BusinessException("权限不足，无法删除此批次");
                }
            }

            // 检查批次状态，进行中的批次不能删除
            if (batch.Status == 1)
                throw new BusinessException("进行中的批次不能删除");

            var result = await _batchDAL.DeleteAsync(batch);

            // 记录业务日志 - 使用Task.Run避免上下文冲突
            _ = Task.Run(async () =>
            {
                try
                {
                    await _logService.LogBusinessOperationAsync(new BusinessLogDto
                    {
                        Module = "批次管理",
                        Operation = "删除批次",
                        BusinessObject = "Batch",
                        ObjectId = batchId.ToString(),
                        DetailedInfo = $"删除批次：{batch.Name}",
                        BeforeData = batch,
                        UserId = currentUserInfo.UserId,
                        Username = currentUserInfo.UserName,
                        Level = LogLevel.Information
                    });
                }
                catch
                {
                    // 忽略日志记录失败，不影响主业务
                }
            });

            return result;
        }

        /// <summary>
        /// 获取批次详情
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <returns>批次响应DTO</returns>
        public async Task<BatchResponseDto?> GetBatchAsync(int batchId)
        {
            var batch = await _batchDAL.GetByIdAsync(batchId);
            if (batch == null) return null;

            return new BatchResponseDto
            {
                Id = batch.Id,
                Name = batch.Name,
                Description = batch.Description,
                VideoId = batch.VideoId,
                VideoTitle = batch.VideoTitle,
                VideoDescription = batch.VideoDescription,
                VideoCoverUrl = batch.VideoCoverUrl,
                VideoUrl = batch.VideoUrl,
                VideoDuration = batch.VideoDuration,
                RewardAmount = batch.RewardAmount,
                Questions = string.IsNullOrEmpty(batch.Questions) ? null :
                    JsonSerializer.Deserialize<List<VideoQuestionDto>>(batch.Questions, MemoryCacheHelper.DefaultJsonOptions),
                StartTime = batch.StartTime,
                EndTime = batch.EndTime,
                CurrentParticipants = batch.CurrentParticipants,
                RedPacketAmount = batch.RedPacketAmount,
                Status = batch.Status,
                CreateTime = batch.CreateTime,
                CreatedBy = batch.CreatedBy ?? string.Empty
            };
        }

        /// <summary>
        /// 分页查询批次列表（带权限过滤）
        /// </summary>
        /// <param name="queryDto">查询条件DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<BatchResponseDto>> GetBatchPagedListAsync(BatchQueryDto queryDto, UserInfo? currentUserInfo = null)
        {
            var queryable = new BatchDAL.Queryable
            {
                Name = queryDto.Name,
                Status = queryDto.Status,
                StartTime = queryDto.StartTime,
                EndTime = queryDto.EndTime,
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            // 根据用户权限过滤数据
            if (currentUserInfo != null)
            {
                // 验证权限
                ValidateUserTypePermission(currentUserInfo, [1, 2, 3], "权限不足，无法查看批次列表");

                // 根据用户类型应用权限过滤
                var accessibleEmployeeIds = await GetAccessibleEmployeeIdsAsync(currentUserInfo);

                if (accessibleEmployeeIds != null)
                {
                    // 管理员或员工：只能查看权限范围内员工创建的批次
                    queryable.CreatedByList = accessibleEmployeeIds;
                }
                // 超级管理员：accessibleEmployeeIds为null，可以查看所有批次
            }

            var result = await _batchDAL.GetPagedListAsync(queryable);

            var responseList = (result.Items ?? []).Select(batch => new BatchResponseDto
            {
                Id = batch.Id,
                Name = batch.Name,
                Description = batch.Description,
                VideoId = batch.VideoId,
                VideoTitle = batch.VideoTitle,
                VideoDescription = batch.VideoDescription,
                VideoCoverUrl = batch.VideoCoverUrl,
                VideoUrl = batch.VideoUrl,
                VideoDuration = batch.VideoDuration,
                RewardAmount = batch.RewardAmount,
                Questions = string.IsNullOrEmpty(batch.Questions) ? null :
                    JsonSerializer.Deserialize<List<VideoQuestionDto>>(batch.Questions, MemoryCacheHelper.DefaultJsonOptions),
                StartTime = batch.StartTime,
                EndTime = batch.EndTime,
                CurrentParticipants = batch.CurrentParticipants,
                RedPacketAmount = batch.RedPacketAmount,
                Status = batch.Status,
                CreateTime = batch.CreateTime,
                CreatedBy = batch.CreatedBy ?? string.Empty
            }).ToList();

            return new PagedResult<BatchResponseDto>
            {
                Items = responseList,
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }

        /// <summary>
        /// 获取批次统计信息（支持权限控制）
        /// 使用新的UserBatchRecord表进行统计
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <param name="currentJwtUserInfo">当前JWT用户信息</param>
        /// <returns>批次统计信息</returns>
        public async Task<BatchStatisticsDto> GetBatchStatisticsAsync(int batchId, UserInfo currentJwtUserInfo)
        {
            // 权限验证
            ValidateUserTypePermission(currentJwtUserInfo, [1, 2, 3], "权限不足，无法查看批次统计");

            // 验证批次是否存在
            var batch = await _batchDAL.GetByIdAsync(batchId) ?? throw new BusinessException("指定的批次不存在");

            // 权限检查：管理员和员工只能查看权限范围内的批次统计
            if (currentJwtUserInfo.UserType == 2)
            {
                var accessibleEmployeeIds = await GetAccessibleEmployeeIdsAsync(currentJwtUserInfo);
                if (accessibleEmployeeIds != null && !accessibleEmployeeIds.Contains(batch.CreatedBy ?? ""))
                {
                    throw new BusinessException("权限不足，无法查看此批次统计");
                }
            }
            else if (currentJwtUserInfo.UserType == 3)
            {
                // 员工只能查看自己创建的批次统计
                if (batch.CreatedBy != currentJwtUserInfo.UserId)
                {
                    throw new BusinessException("权限不足，只能查看自己创建的批次统计");
                }
            }

            // 根据用户权限获取可访问的用户ID列表
            var accessibleUserIds = await GetAccessibleUserIdsAsync(currentJwtUserInfo);

            // 使用新的UserBatchRecord获取统计数据
            var statistics = await _userBatchRecordDAL.GetBatchStatisticsAsync(batchId, accessibleUserIds);

            // 组装统计结果
            return new BatchStatisticsDto
            {
                ViewCount = statistics.ViewerCount,
                CompleteViewCount = statistics.CompletedViewerCount,
                // 修改完播率计算：基于观看用户数而不是参与用户数
                CompleteRate = statistics.ViewerCount > 0 ?
                    Math.Round((decimal)statistics.CompletedViewerCount / statistics.ViewerCount * 100, 2) : 0,

                TotalAnswerCount = statistics.AnswerCount,
                CorrectAnswerCount = (int)(statistics.AnswerCount * statistics.AverageCorrectRate / 100),
                CorrectRate = statistics.AverageCorrectRate,

                RewardCount = statistics.SuccessRewardCount,
                RewardAmount = statistics.TotalRewardAmount,

                TotalParticipants = statistics.TotalParticipants, // 修正：现在是总用户数
                NewUserCount = statistics.TotalParticipants, // 暂时等同于总用户数

                // 对于单个批次，这些字段设为固定值
                TotalCount = 1,
                ActiveCount = batch.Status == 1 ? 1 : 0,
                CompletedCount = DateTime.Now > batch.EndTime ? 1 : 0,
                TotalRedPacketAmount = batch.RedPacketAmount
            };
        }
    }
}
