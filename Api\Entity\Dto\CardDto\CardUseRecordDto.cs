namespace Entity.Dto.CardDto;

public class CreateCardUseRecordDto
{
    /// <summary>
    /// 玩家ID
    /// </summary>
    public string PlayerId { get; set; } = string.Empty;
    /// <summary>
    /// 卡号
    /// </summary>
    public string CardNo { get; set; } = string.Empty;
    /// <summary>
    /// 核销金额
    /// </summary>
    public decimal Amount { get; set; }
    /// <summary>
    /// 核销状态
    /// </summary>
    public CardUseRecordStatusEnum Status { get; set; }
    /// <summary>
    /// 操作IP
    /// </summary>
    public string? Ip { get; set; }
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}