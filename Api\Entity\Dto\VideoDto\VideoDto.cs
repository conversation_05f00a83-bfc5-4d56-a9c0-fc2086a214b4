using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.VideoDto
{
    /// <summary>
    /// 视频查询DTO
    /// </summary>
    public class VideoQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 视频标题(模糊查询)
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 状态:0下架,1上架,2失败,3压缩中
        /// </summary>
        public byte? Status { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 视频创建DTO
    /// </summary>
    public class VideoCreateDto
    {
        /// <summary>
        /// 视频标题
        /// </summary>
        [Required(ErrorMessage = "视频标题不能为空")]
        [MaxLength(255, ErrorMessage = "视频标题长度不能超过255个字符")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 视频描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 封面URL
        /// </summary>
        [MaxLength(255, ErrorMessage = "封面URL长度不能超过255个字符")]
        public string? CoverUrl { get; set; }

        /// <summary>
        /// 视频URL
        /// </summary>
        [Required(ErrorMessage = "视频URL不能为空")]
        [MaxLength(255, ErrorMessage = "视频URL长度不能超过255个字符")]
        public string VideoUrl { get; set; } = string.Empty;

        /// <summary>
        /// 视频时长(秒)
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "视频时长必须大于等于0")]
        public int Duration { get; set; } = 0;

        /// <summary>
        /// 红包金额
        /// </summary>
        [Range(0, 999999.99, ErrorMessage = "红包金额必须在0-999999.99之间")]
        public decimal RewardAmount { get; set; } = 0.00m;

        /// <summary>
        /// 观看次数
        /// </summary>
        public int ViewCount { get; set; } = 0;

        /// <summary>
        /// 问题列表
        /// </summary>
        public List<VideoQuestionDto>? Questions { get; set; }

        /// <summary>
        /// 文件ID，用于关联压缩进度
        /// </summary>
        public string? FileId { get; set; }

    }

    /// <summary>
    /// 视频更新DTO
    /// </summary>
    public class VideoUpdateDto
    {
        /// <summary>
        /// 视频ID
        /// </summary>
        [Required(ErrorMessage = "视频ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 视频标题
        /// </summary>
        [Required(ErrorMessage = "视频标题不能为空")]
        [MaxLength(255, ErrorMessage = "视频标题长度不能超过255个字符")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 视频描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 封面URL
        /// </summary>
        [MaxLength(255, ErrorMessage = "封面URL长度不能超过255个字符")]
        public string? CoverUrl { get; set; }

        /// <summary>
        /// 视频URL
        /// </summary>
        [Required(ErrorMessage = "视频URL不能为空")]
        [MaxLength(255, ErrorMessage = "视频URL长度不能超过255个字符")]
        public string VideoUrl { get; set; } = string.Empty;

        /// <summary>
        /// 视频时长(秒)
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "视频时长必须大于等于0")]
        public int Duration { get; set; } = 0;

        /// <summary>
        /// 红包金额
        /// </summary>
        [Range(0, 999999.99, ErrorMessage = "红包金额必须在0-999999.99之间")]
        public decimal RewardAmount { get; set; } = 0.00m;

        /// <summary>
        /// 观看次数
        /// </summary>
        public int ViewCount { get; set; } = 0;

        /// <summary>
        /// 问题列表
        /// </summary>
        public List<VideoQuestionDto>? Questions { get; set; }

        /// <summary>
        /// 文件ID，用于关联压缩进度
        /// </summary>
        public string? FileId { get; set; }

        /// <summary>
        /// 状态:0下架,1上架,2失败,3压缩中
        /// </summary>
        public byte? Status { get; set; }
    }

    /// <summary>
    /// 视频响应DTO
    /// </summary>
    public class VideoResponseDto
    {
        /// <summary>
        /// 视频ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 视频标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 视频描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 封面URL
        /// </summary>
        public string? CoverUrl { get; set; }

        /// <summary>
        /// 视频URL
        /// </summary>
        public string VideoUrl { get; set; } = string.Empty;

        /// <summary>
        /// 视频时长(秒)
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public string? CreatedBy { get; set; }

        /// <summary>
        /// 创建人姓名
        /// </summary>
        public string? CreatorName { get; set; }

        /// <summary>
        /// 红包金额
        /// </summary>
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 观看次数
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// 问题列表
        /// </summary>
        public List<VideoQuestionDto>? Questions { get; set; }

        /// <summary>
        /// 状态:0下架,1上架,2失败,3压缩中
        /// </summary>
        public byte Status { get; set; }

        /// <summary>
        /// 文件ID，用于关联压缩进度
        /// </summary>
        public string? FileId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 视频问题DTO
    /// </summary>
    public class VideoQuestionDto
    {
        /// <summary>
        /// 问题内容
        /// </summary>
        [Required(ErrorMessage = "问题内容不能为空")]
        [MaxLength(500, ErrorMessage = "问题内容长度不能超过500个字符")]
        public string questionText { get; set; } = string.Empty;

        /// <summary>
        /// 问题序号
        /// </summary>
        [Range(0, 1, ErrorMessage = "问题序号必须在0-1之间")]
        public int orderNum { get; set; }

        /// <summary>
        /// 选项列表
        /// </summary>
        [Required(ErrorMessage = "选项列表不能为空")]
        public List<VideoQuestionOptionDto> options { get; set; } = [];
    }

    /// <summary>
    /// 视频问题选项DTO
    /// </summary>
    public class VideoQuestionOptionDto
    {
        /// <summary>
        /// 选项内容
        /// </summary>
        [Required(ErrorMessage = "选项内容不能为空")]
        [MaxLength(500, ErrorMessage = "选项内容长度不能超过500个字符")]
        public string optionText { get; set; } = string.Empty;

        /// <summary>
        /// 是否正确答案
        /// </summary>
        public bool isCorrect { get; set; }

        /// <summary>
        /// 选项序号
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "选项序号必须大于等于0")]
        public int orderNum { get; set; }
    }

    /// <summary>
    /// 视频状态更新DTO
    /// </summary>
    public class VideoStatusUpdateDto
    {
        /// <summary>
        /// 视频ID
        /// </summary>
        [Required(ErrorMessage = "视频ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 状态:0下架,1上架,2失败,3压缩中
        /// </summary>
        [Range(0, 3, ErrorMessage = "状态值必须为0、1、2或3")]
        public byte Status { get; set; }
    }

    /// <summary>
    /// 视频统计DTO
    /// </summary>
    public class VideoStatisticsDto
    {
        /// <summary>
        /// 总观看次数
        /// </summary>
        public int TotalViewCount { get; set; }

        /// <summary>
        /// 完整观看次数
        /// </summary>
        public int CompleteViewCount { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal CompleteRate { get; set; }

        /// <summary>
        /// 平均观看时长（秒）
        /// </summary>
        public int AverageViewDuration { get; set; }

        /// <summary>
        /// 今日观看次数
        /// </summary>
        public int TodayViewCount { get; set; }

        /// <summary>
        /// 本周观看次数
        /// </summary>
        public int WeekViewCount { get; set; }

        /// <summary>
        /// 本月观看次数
        /// </summary>
        public int MonthViewCount { get; set; }

        /// <summary>
        /// 独立观看用户数
        /// </summary>
        public int UniqueViewerCount { get; set; }

        /// <summary>
        /// 重复观看次数
        /// </summary>
        public int RepeatViewCount { get; set; }

        /// <summary>
        /// 分享次数
        /// </summary>
        public int ShareCount { get; set; }

        /// <summary>
        /// 视频总数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 已发布视频数
        /// </summary>
        public int PublishedCount { get; set; }

        /// <summary>
        /// 待审核视频数
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// 下架视频数
        /// </summary>
        public int OfflineCount { get; set; }

        /// <summary>
        /// 总时长(秒)
        /// </summary>
        public long TotalDuration { get; set; }

        /// <summary>
        /// 最近观看时间
        /// </summary>
        public DateTime? LastViewTime { get; set; }
    }

    /// <summary>
    /// 视频上传DTO
    /// </summary>
    public class VideoUploadDto
    {
        /// <summary>
        /// 视频文件
        /// </summary>
        [Required(ErrorMessage = "视频文件不能为空")]
        public IFormFile VideoFile { get; set; } = null!;

        /// <summary>
        /// 是否启用压缩
        /// </summary>
        public bool EnableCompression { get; set; } = true;

        /// <summary>
        /// 视频标题（可选，如果不提供将使用文件名）
        /// </summary>
        [MaxLength(255, ErrorMessage = "视频标题长度不能超过255个字符")]
        public string? Title { get; set; }

        /// <summary>
        /// 视频描述（可选）
        /// </summary>
        [MaxLength(1000, ErrorMessage = "视频描述长度不能超过1000个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 压缩质量 (1-10, 10为最高质量)
        /// </summary>
        [Range(1, 10, ErrorMessage = "压缩质量必须在1-10之间")]
        public int CompressionQuality { get; set; } = 7;

        /// <summary>
        /// 最大分辨率宽度（像素）
        /// </summary>
        [Range(480, 1920, ErrorMessage = "最大分辨率宽度必须在480-1920之间")]
        public int MaxWidth { get; set; } = 1280;
    }

    /// <summary>
    /// 视频上传响应DTO
    /// </summary>
    public class VideoUploadResponseDto
    {
        /// <summary>
        /// 原始视频URL
        /// </summary>
        public string OriginalVideoUrl { get; set; } = string.Empty;

        /// <summary>
        /// 压缩后视频URL（如果启用压缩）
        /// </summary>
        public string? CompressedVideoUrl { get; set; }

        /// <summary>
        /// 缩略图URL
        /// </summary>
        public string? ThumbnailUrl { get; set; }

        /// <summary>
        /// 视频时长（秒）
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 原始文件大小（字节）
        /// </summary>
        public long OriginalFileSize { get; set; }

        /// <summary>
        /// 压缩后文件大小（字节）
        /// </summary>
        public long? CompressedFileSize { get; set; }

        /// <summary>
        /// 视频格式
        /// </summary>
        public string VideoFormat { get; set; } = string.Empty;

        /// <summary>
        /// 视频分辨率
        /// </summary>
        public string Resolution { get; set; } = string.Empty;

        /// <summary>
        /// 压缩是否正在进行中
        /// </summary>
        public bool CompressionInProgress { get; set; }

        /// <summary>
        /// 压缩进度百分比（0-100）
        /// </summary>
        public int CompressionProgress { get; set; }

        /// <summary>
        /// 文件唯一标识符
        /// </summary>
        public string FileId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 视频完整上传DTO
    /// </summary>
    public class VideoCompleteUploadDto
    {
        /// <summary>
        /// 视频文件
        /// </summary>
        [Required(ErrorMessage = "视频文件不能为空")]
        public IFormFile VideoFile { get; set; } = null!;

        /// <summary>
        /// 封面文件（可选）
        /// </summary>
        public IFormFile? CoverFile { get; set; }

        /// <summary>
        /// 封面URL（可选，如果提供了CoverUrl则优先使用，否则使用CoverFile）
        /// </summary>
        [MaxLength(255, ErrorMessage = "封面URL长度不能超过255个字符")]
        public string? CoverUrl { get; set; }

        /// <summary>
        /// 视频标题
        /// </summary>
        [Required(ErrorMessage = "视频标题不能为空")]
        [MaxLength(255, ErrorMessage = "视频标题长度不能超过255个字符")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 视频描述（可选）
        /// </summary>
        [MaxLength(1000, ErrorMessage = "视频描述长度不能超过1000个字符")]
        public string? Description { get; set; }

        // CreatorId 不需要前端传递，后端从Token中获取

        /// <summary>
        /// 奖励金额
        /// </summary>
        [Required(ErrorMessage = "奖励金额不能为空")]
        [Range(0.01, double.MaxValue, ErrorMessage = "奖励金额必须大于0")]
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 问题数据（JSON字符串）
        /// </summary>
        [Required(ErrorMessage = "问题数据不能为空")]
        public string QuestionsJson { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用压缩
        /// </summary>
        public bool EnableCompression { get; set; } = true;

        /// <summary>
        /// 压缩质量 (1-10, 10为最高质量)
        /// </summary>
        [Range(1, 10, ErrorMessage = "压缩质量必须在1-10之间")]
        public int CompressionQuality { get; set; } = 7;
    }

    /// <summary>
    /// 视频完整上传响应DTO
    /// </summary>
    public class VideoCompleteUploadResponseDto
    {
        /// <summary>
        /// 视频ID
        /// </summary>
        public int VideoId { get; set; }

        /// <summary>
        /// 视频URL
        /// </summary>
        public string VideoUrl { get; set; } = string.Empty;

        /// <summary>
        /// 封面URL
        /// </summary>
        public string? CoverUrl { get; set; }

        /// <summary>
        /// 视频时长（秒）
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 视频格式
        /// </summary>
        public string VideoFormat { get; set; } = string.Empty;

        /// <summary>
        /// 视频分辨率
        /// </summary>
        public string Resolution { get; set; } = string.Empty;

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; } = string.Empty;
    }
}
