using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Reflection;
using System.Text;

namespace BLL.SysService.Exports
{
    /// <summary>
    /// 导入服务实现
    /// </summary>
    public class ImportService : IImportService
    {
        private readonly ILogger<ImportService> _logger;
        private string _templateBasePath;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public ImportService(ILogger<ImportService> logger)
        {
            _logger = logger;
            _templateBasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates");
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="templateBasePath">模板文件基础路径</param>
        public ImportService(ILogger<ImportService> logger, string templateBasePath)
        {
            _logger = logger;
            _templateBasePath = templateBasePath;
        }

        /// <summary>
        /// 获取或设置模板文件的基础路径
        /// </summary>
        public string TemplateBasePath
        {
            get => _templateBasePath;
            set
            {
                if (string.IsNullOrEmpty(value))
                {
                    throw new ArgumentException("模板基础路径不能为空");
                }
                _templateBasePath = value;
            }
        }

        /// <summary>
        /// 从CSV文件导入数据
        /// </summary>
        /// <typeparam name="T">目标数据类型</typeparam>
        /// <param name="file">上传的CSV文件</param>
        /// <param name="columnMappings">列映射配置（CSV列名 -> 属性名）</param>
        /// <returns>导入的数据列表</returns>
        public async Task<List<T>> ImportFromCsvAsync<T>(IFormFile file, Dictionary<string, string> columnMappings) where T : new()
        {
            try
            {
                _logger.LogInformation("开始从CSV文件导入数据");

                if (file == null || file.Length == 0)
                {
                    throw new ArgumentException("文件为空");
                }

                var result = new List<T>();
                using var stream = file.OpenReadStream();
                using var reader = new StreamReader(stream, Encoding.GetEncoding("gb2312"));

                // 读取表头
                var headerLine = await reader.ReadLineAsync();
                if (string.IsNullOrEmpty(headerLine))
                {
                    throw new InvalidDataException("CSV文件格式无效：缺少表头");
                }

                // 解析表头
                var headers = ParseCsvLine(headerLine);
                var columnIndexMap = new Dictionary<string, int>();

                // 创建列名到索引的映射
                for (int i = 0; i < headers.Count; i++)
                {
                    columnIndexMap[headers[i]] = i;
                }

                // 验证所有必需的列是否存在
                foreach (var column in columnMappings.Keys)
                {
                    if (!columnIndexMap.ContainsKey(column))
                    {
                        throw new InvalidDataException($"CSV文件缺少必需的列：{column}");
                    }
                }

                // 读取数据行
                string? line;
                int lineNumber = 1;
                while ((line = await reader.ReadLineAsync()) != null)
                {
                    lineNumber++;
                    if (string.IsNullOrWhiteSpace(line))
                    {
                        continue;
                    }

                    try
                    {
                        var values = ParseCsvLine(line);
                        var item = new T();

                        // 设置属性值
                        foreach (var mapping in columnMappings)
                        {
                            var csvColumnName = mapping.Key;
                            var propertyName = mapping.Value;

                            if (columnIndexMap.TryGetValue(csvColumnName, out int columnIndex) && columnIndex < values.Count)
                            {
                                var value = values[columnIndex];
                                SetPropertyValue(item, propertyName, value);
                            }
                        }

                        result.Add(item);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "解析CSV行 {LineNumber} 失败", lineNumber);
                        // 继续处理下一行
                    }
                }

                _logger.LogInformation("CSV文件导入完成，共导入 {Count} 条记录", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从CSV文件导入数据失败");
                throw new ApplicationException("从CSV文件导入数据失败", ex);
            }
        }

        /// <summary>
        /// 验证CSV文件格式
        /// </summary>
        /// <param name="file">上传的CSV文件</param>
        /// <param name="requiredColumns">必需的列名</param>
        /// <returns>验证结果</returns>
        public async Task<(bool IsValid, string ErrorMessage)> ValidateCsvFileAsync(IFormFile file, List<string> requiredColumns)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return (false, "文件为空");
                }

                using var stream = file.OpenReadStream();
                using var reader = new StreamReader(stream, Encoding.GetEncoding("gb2312"));

                // 读取表头
                var headerLine = await reader.ReadLineAsync();
                if (string.IsNullOrEmpty(headerLine))
                {
                    return (false, "CSV文件格式无效：缺少表头");
                }

                // 解析表头
                var headers = ParseCsvLine(headerLine);
                var missingColumns = requiredColumns.Where(col => !headers.Contains(col)).ToList();

                if (missingColumns.Count != 0)
                {
                    return (false, $"CSV文件缺少必需的列：{string.Join(", ", missingColumns)}");
                }

                return (true, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证CSV文件失败");
                return (false, $"验证CSV文件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 生成CSV导入模板
        /// </summary>
        /// <typeparam name="T">目标数据类型</typeparam>
        /// <param name="columnMappings">列映射配置（CSV列名 -> 属性名）</param>
        /// <param name="includeExampleRow">是否包含示例行</param>
        /// <returns>CSV模板内容</returns>
        public string GenerateCsvImportTemplate<T>(Dictionary<string, string> columnMappings, bool includeExampleRow = true) where T : new()
        {
            try
            {
                _logger.LogInformation("开始生成CSV导入模板");

                var sb = new StringBuilder();

                // 添加表头
                sb.AppendLine(string.Join(",", columnMappings.Keys));

                // 如果需要包含示例行
                if (includeExampleRow)
                {
                    var exampleValues = new List<string>();

                    foreach (var mapping in columnMappings)
                    {
                        var propertyName = mapping.Value;
                        var exampleValue = GetExampleValueForProperty<T>(propertyName);
                        exampleValues.Add(exampleValue);
                    }

                    sb.AppendLine(string.Join(",", exampleValues));
                }

                _logger.LogInformation("CSV导入模板生成完成");
                return sb.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成CSV导入模板失败");
                throw new ApplicationException("生成CSV导入模板失败", ex);
            }
        }

        /// <summary>
        /// 获取或创建CSV导入模板文件（内部实现）
        /// </summary>
        /// <typeparam name="T">目标数据类型</typeparam>
        /// <param name="columnMappings">列映射配置（CSV列名 -> 属性名）</param>
        /// <param name="filePath">文件保存路径</param>
        /// <param name="includeExampleRow">是否包含示例行</param>
        /// <returns>模板文件路径及是否新创建</returns>
        private async Task<(string FilePath, bool IsNewlyCreated, string Message)> GetOrCreateCsvTemplateFileInternalAsync<T>(
            Dictionary<string, string> columnMappings,
            string filePath,
            bool includeExampleRow = true) where T : new()
        {
            try
            {
                _logger.LogInformation("检查CSV导入模板文件");

                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                bool isNewlyCreated = false;
                string message = "使用现有模板文件";

                // 检查文件是否存在
                if (File.Exists(filePath))
                {
                    // 检查列是否有变化
                    var (HasChanged, ChangeDetails) = await HasColumnsChangedAsync(filePath, [.. columnMappings.Keys]);

                    if (HasChanged)
                    {
                        _logger.LogInformation("模板文件的列已变化，重新生成模板");

                        // 备份旧文件
                        string backupPath = $"{filePath}.{DateTime.Now:yyyyMMddHHmmss}.bak";
                        File.Copy(filePath, backupPath);

                        // 创建新模板
                        var templateContent = GenerateCsvImportTemplate<T>(columnMappings, includeExampleRow);
                        await File.WriteAllTextAsync(filePath, templateContent, Encoding.GetEncoding("gb2312"));

                        isNewlyCreated = true;
                        message = $"模板已更新，原模板已备份为：{Path.GetFileName(backupPath)}。变更：{ChangeDetails}";
                    }
                }
                else
                {
                    _logger.LogInformation("模板文件不存在，创建新模板");

                    // 创建新模板
                    var templateContent = GenerateCsvImportTemplate<T>(columnMappings, includeExampleRow);
                    await File.WriteAllTextAsync(filePath, templateContent, Encoding.GetEncoding("gb2312"));

                    isNewlyCreated = true;
                    message = "已创建新的导入模板";
                }

                return (filePath, isNewlyCreated, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取或创建CSV导入模板文件失败");
                throw new ApplicationException("获取或创建CSV导入模板文件失败", ex);
            }
        }

        /// <summary>
        /// 获取或创建CSV导入模板文件（使用统一路径）
        /// </summary>
        /// <typeparam name="T">目标数据类型</typeparam>
        /// <param name="columnMappings">列映射配置（CSV列名 -> 属性名）</param>
        /// <param name="fileName">文件名称（不含路径）</param>
        /// <param name="includeExampleRow">是否包含示例行</param>
        /// <returns>模板文件路径及是否新创建</returns>
        public async Task<(string FilePath, bool IsNewlyCreated, string Message)> GetOrCreateCsvTemplateFileAsync<T>(
            Dictionary<string, string> columnMappings,
            string fileName,
            bool includeExampleRow = true) where T : new()
        {
            // 确保模板目录存在
            if (!Directory.Exists(_templateBasePath))
            {
                Directory.CreateDirectory(_templateBasePath);
            }

            // 构建完整文件路径
            string filePath = Path.Combine(_templateBasePath, fileName);

            // 调用内部方法完成处理
            return await GetOrCreateCsvTemplateFileInternalAsync<T>(columnMappings, filePath, includeExampleRow);
        }

        /// <summary>
        /// 获取模板文件的完整路径
        /// </summary>
        /// <param name="fileName">文件名称（不含路径）</param>
        /// <returns>完整文件路径</returns>
        public string GetTemplateFilePath(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
            {
                throw new ArgumentException("文件名不能为空");
            }

            return Path.Combine(_templateBasePath, fileName);
        }

        /// <summary>
        /// 检查模板文件是否存在
        /// </summary>
        /// <param name="fileName">文件名称（不含路径）</param>
        /// <returns>文件是否存在</returns>
        public bool TemplateFileExists(string fileName)
        {
            string filePath = GetTemplateFilePath(fileName);
            return File.Exists(filePath);
        }

        /// <summary>
        /// 检查CSV文件的列是否有变化
        /// </summary>
        /// <param name="filePath">现有文件路径</param>
        /// <param name="requiredColumns">要求的列名列表</param>
        /// <returns>是否有变化及变化详情</returns>
        private async Task<(bool HasChanged, string ChangeDetails)> HasColumnsChangedAsync(string filePath, List<string> requiredColumns)
        {
            try
            {
                // 读取文件表头
                string? firstLine = null;
                using (var reader = new StreamReader(filePath, Encoding.GetEncoding("gb2312")))
                {
                    firstLine = await reader.ReadLineAsync();
                }

                if (string.IsNullOrEmpty(firstLine))
                {
                    return (true, "现有文件表头为空");
                }

                // 解析表头
                var existingColumns = ParseCsvLine(firstLine);

                // 比较列
                var missingColumns = requiredColumns.Where(col => !existingColumns.Contains(col)).ToList();
                var extraColumns = existingColumns.Where(col => !requiredColumns.Contains(col)).ToList();

                if (missingColumns.Count > 0 || extraColumns.Count > 0)
                {
                    var details = new StringBuilder();

                    if (missingColumns.Count > 0)
                    {
                        details.Append($"新增列：{string.Join(", ", missingColumns)}");
                    }

                    if (extraColumns.Count > 0)
                    {
                        if (details.Length > 0) details.Append('；');
                        details.Append($"移除列：{string.Join(", ", extraColumns)}");
                    }

                    return (true, details.ToString());
                }

                // 检查顺序是否变化
                bool orderChanged = false;
                for (int i = 0; i < requiredColumns.Count; i++)
                {
                    if (i < existingColumns.Count && requiredColumns[i] != existingColumns[i])
                    {
                        orderChanged = true;
                        break;
                    }
                }

                if (orderChanged)
                {
                    return (true, "列顺序已变化");
                }

                return (false, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查CSV列变化时出错");
                // 如果检查出错，返回有变化，以确保重新生成模板
                return (true, $"检查出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取属性的示例值
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="propertyName">属性名</param>
        /// <returns>示例值</returns>
        private static string GetExampleValueForProperty<T>(string propertyName) where T : new()
        {
            try
            {
                // 支持嵌套属性，如 "User.Name"
                var propertyNames = propertyName.Split('.');
                Type currentType = typeof(T);
                Type? propertyType = null;

                for (int i = 0; i < propertyNames.Length; i++)
                {
                    var propName = propertyNames[i];
                    PropertyInfo? property = currentType.GetProperty(propName);

                    if (property == null)
                    {
                        return $"[未知属性:{propName}]";
                    }

                    propertyType = property.PropertyType;
                    currentType = propertyType;
                }

                if (propertyType == null)
                {
                    return "[示例]";
                }

                // 处理可空类型
                Type underlyingType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;

                if (underlyingType == typeof(string))
                {
                    return "示例文本";
                }
                else if (underlyingType == typeof(int) || underlyingType == typeof(long))
                {
                    return "100";
                }
                else if (underlyingType == typeof(double) || underlyingType == typeof(decimal))
                {
                    return "100.00";
                }
                else if (underlyingType == typeof(bool))
                {
                    return "true";
                }
                else if (underlyingType == typeof(DateTime))
                {
                    return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                }
                else if (underlyingType == typeof(Guid))
                {
                    return Guid.NewGuid().ToString();
                }
                else if (underlyingType.IsEnum)
                {
                    // 返回第一个枚举值
                    var enumValues = Enum.GetValues(underlyingType);
                    if (enumValues.Length > 0)
                    {
                        return enumValues.GetValue(0)?.ToString() ?? "0";
                    }
                    return "0";
                }
                else
                {
                    return $"[{underlyingType.Name}类型示例]";
                }
            }
            catch
            {
                return "[示例]";
            }
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        /// <param name="line">CSV行</param>
        /// <returns>字段值列表</returns>
        private static List<string> ParseCsvLine(string line)
        {
            var result = new List<string>();
            if (string.IsNullOrEmpty(line))
            {
                return result;
            }

            bool inQuotes = false;
            var currentField = new StringBuilder();

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                    {
                        // 处理双引号转义
                        currentField.Append('"');
                        i++;
                    }
                    else
                    {
                        // 切换引号状态
                        inQuotes = !inQuotes;
                    }
                }
                else if (c == ',' && !inQuotes)
                {
                    // 字段结束
                    result.Add(currentField.ToString());
                    currentField.Clear();
                }
                else
                {
                    // 普通字符
                    currentField.Append(c);
                }
            }

            // 添加最后一个字段
            result.Add(currentField.ToString());

            return result;
        }

        /// <summary>
        /// 设置属性值
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="obj">目标对象</param>
        /// <param name="propertyName">属性名</param>
        /// <param name="value">属性值</param>
        private void SetPropertyValue<T>(T obj, string propertyName, string value)
        {
            try
            {
                // 支持嵌套属性，如 "User.Name"
                var propertyNames = propertyName.Split('.');
                object? currentObj = obj;

                for (int i = 0; i < propertyNames.Length; i++)
                {
                    if (currentObj == null)
                    {
                        _logger.LogWarning("当前对象为null");
                        return;
                    }

                    var propName = propertyNames[i];
                    PropertyInfo? property = currentObj.GetType().GetProperty(propName);

                    if (property == null)
                    {
                        _logger.LogWarning("属性不存在: {PropertyName}", propName);
                        return;
                    }

                    if (i == propertyNames.Length - 1)
                    {
                        // 最后一个属性，设置值
                        if (string.IsNullOrEmpty(value))
                        {
                            // 如果值为空，则设置为默认值或null
                            property.SetValue(currentObj, null);
                        }
                        else
                        {
                            // 转换值类型并设置
                            var convertedValue = ConvertValue(value, property.PropertyType);
                            property.SetValue(currentObj, convertedValue);
                        }
                    }
                    else
                    {
                        // 中间属性，获取或创建对象
                        var nestedObj = property.GetValue(currentObj);
                        if (nestedObj == null)
                        {
                            // 如果嵌套对象为null，则创建新实例
                            nestedObj = Activator.CreateInstance(property.PropertyType);
                            if (nestedObj != null)
                            {
                                property.SetValue(currentObj, nestedObj);
                            }
                        }
                        currentObj = nestedObj;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "设置属性值失败: {PropertyName}, 值: {Value}", propertyName, value);
            }
        }

        /// <summary>
        /// 转换值类型
        /// </summary>
        /// <param name="value">字符串值</param>
        /// <param name="targetType">目标类型</param>
        /// <returns>转换后的值</returns>
        private static object? ConvertValue(string value, Type targetType)
        {
            if (string.IsNullOrEmpty(value))
            {
                return null;
            }

            // 处理可空类型
            Type underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

            if (underlyingType == typeof(string))
            {
                return value;
            }
            else if (underlyingType == typeof(int))
            {
                return int.Parse(value);
            }
            else if (underlyingType == typeof(long))
            {
                return long.Parse(value);
            }
            else if (underlyingType == typeof(double))
            {
                return double.Parse(value);
            }
            else if (underlyingType == typeof(decimal))
            {
                return decimal.Parse(value);
            }
            else if (underlyingType == typeof(bool))
            {
                return bool.Parse(value);
            }
            else if (underlyingType == typeof(DateTime))
            {
                return DateTime.Parse(value);
            }
            else if (underlyingType == typeof(Guid))
            {
                return Guid.Parse(value);
            }
            else if (underlyingType.IsEnum)
            {
                return Enum.Parse(underlyingType, value);
            }
            else
            {
                throw new NotSupportedException($"不支持的类型转换: {targetType.Name}");
            }
        }

        /// <summary>
        /// 生成CSV导入模板并保存为文件
        /// </summary>
        /// <typeparam name="T">目标数据类型</typeparam>
        /// <param name="columnMappings">列映射配置（CSV列名 -> 属性名）</param>
        /// <param name="filePath">文件保存路径</param>
        /// <param name="includeExampleRow">是否包含示例行</param>
        /// <returns>是否成功</returns>
        public bool GenerateCsvImportTemplateFile<T>(Dictionary<string, string> columnMappings, string filePath, bool includeExampleRow = true) where T : new()
        {
            try
            {
                var templateContent = GenerateCsvImportTemplate<T>(columnMappings, includeExampleRow);
                File.WriteAllText(filePath, templateContent, Encoding.GetEncoding("gb2312"));
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存CSV导入模板文件失败");
                return false;
            }
        }

        /// <summary>
        /// 生成CSV导入模板并提供下载
        /// </summary>
        /// <typeparam name="T">目标数据类型</typeparam>
        /// <param name="columnMappings">列映射配置（CSV列名 -> 属性名）</param>
        /// <param name="fileName">文件名（不含路径）</param>
        /// <param name="includeExampleRow">是否包含示例行</param>
        /// <returns>包含CSV内容的内存流</returns>
        public MemoryStream GenerateCsvImportTemplateForDownload<T>(Dictionary<string, string> columnMappings, string fileName, bool includeExampleRow = true) where T : new()
        {
            try
            {
                _logger.LogInformation("生成CSV导入模板供下载: {FileName}", fileName);

                var templateContent = GenerateCsvImportTemplate<T>(columnMappings, includeExampleRow);
                var contentBytes = Encoding.GetEncoding("gb2312").GetBytes(templateContent);

                var memoryStream = new MemoryStream(contentBytes)
                {
                    Position = 0
                };

                return memoryStream;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成CSV导入模板供下载失败");
                throw new ApplicationException("生成CSV导入模板供下载失败", ex);
            }
        }

        /// <summary>
        /// 生成CSV导入模板并提供下载（使用统一路径）
        /// </summary>
        /// <typeparam name="T">目标数据类型</typeparam>
        /// <param name="columnMappings">列映射配置（CSV列名 -> 属性名）</param>
        /// <param name="fileName">文件名（不含路径）</param>
        /// <param name="includeExampleRow">是否包含示例行</param>
        /// <returns>包含CSV内容的内存流</returns>
        public async Task<MemoryStream> GetOrGenerateCsvTemplateForDownloadAsync<T>(
            Dictionary<string, string> columnMappings,
            string fileName,
            bool includeExampleRow = true) where T : new()
        {
            try
            {
                _logger.LogInformation("获取或生成CSV模板并提供下载: {FileName}", fileName);

                // 确保模板目录存在
                if (!Directory.Exists(_templateBasePath))
                {
                    Directory.CreateDirectory(_templateBasePath);
                }

                // 构建完整文件路径
                string filePath = Path.Combine(_templateBasePath, fileName);

                // 检查文件是否存在，不存在或列有变化则创建
                var (FilePath, IsNewlyCreated, Message) = await GetOrCreateCsvTemplateFileInternalAsync<T>(columnMappings, filePath, includeExampleRow);

                // 读取文件内容并转换为内存流
                var fileBytes = await File.ReadAllBytesAsync(FilePath);
                var memoryStream = new MemoryStream(fileBytes)
                {
                    Position = 0
                };

                return memoryStream;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取或生成CSV模板并提供下载失败");
                throw new ApplicationException("获取或生成CSV模板并提供下载失败", ex);
            }
        }
    }
}