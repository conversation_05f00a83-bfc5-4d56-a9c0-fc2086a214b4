using BLL.VideoService;
using Common;
using Common.Exceptions;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 微信相关接口控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class WechatController(WechatOAuthService wechatOAuthService, WechatAccessTokenService wechatAccessTokenService, ILogger<WechatController> logger) : ControllerBase
    {
        private readonly WechatOAuthService _wechatOAuthService = wechatOAuthService;
        private readonly WechatAccessTokenService _wechatAccessTokenService = wechatAccessTokenService;
        private readonly ILogger<WechatController> _logger = logger;

        /// <summary>
        /// 获取微信OAuth2.0授权URL
        /// </summary>
        /// <param name="requestDto">授权请求参数</param>
        /// <returns>授权URL</returns>
        [HttpGet("authorize")]
        public IActionResult GetOAuthUrl([FromQuery] WechatOAuthRequestDto requestDto)
        {
            _logger.LogInformation("收到获取微信OAuth授权URL请求，RedirectUri: {RedirectUri}, Scope: {Scope}, State: {State}",
                requestDto.RedirectUri, requestDto.Scope, requestDto.State);

            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("获取授权URL参数验证失败: {ModelState}", ModelState);
                    return BadRequest(new { success = false, message = "参数验证失败", errors = ModelState });
                }

                var authUrl = _wechatOAuthService.GenerateOAuthUrl(requestDto);

                _logger.LogInformation("成功生成微信OAuth授权URL: {AuthUrl}", authUrl);
                return Ok(new
                {
                    success = true,
                    data = new { authUrl },
                    message = "获取授权URL成功"
                });
            }
            catch (BusinessException ex)
            {
                _logger.LogError(ex, "获取微信OAuth授权URL业务异常: {Message}", ex.Message);
                return BadRequest(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取微信OAuth授权URL系统异常: {Message}", ex.Message);
                return StatusCode(500, new { success = false, message = "服务器内部错误", detail = ex.Message });
            }
        }



        /// <summary>
        /// 微信OAuth2.0授权回调接口
        /// </summary>
        /// <param name="callbackDto">回调参数</param>
        /// <returns>登录结果</returns>
        [HttpGet("callback")]
        public async Task<IActionResult> OAuthCallback([FromQuery] WechatOAuthCallbackDto callbackDto)
        {
            _logger.LogInformation("收到微信OAuth回调请求，Code: {Code}, State: {State}", callbackDto.Code, callbackDto.State);

            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("OAuth回调参数验证失败: {ModelState}", ModelState);
                    return BadRequest(new { success = false, message = "参数验证失败", errors = ModelState });
                }

                var loginResult = await _wechatOAuthService.HandleOAuthCallbackAsync(callbackDto);

                _logger.LogInformation("微信OAuth登录成功，用户ID: {UserId}, 是否新用户: {IsNewUser}",
                    loginResult.UserInfo.Id, loginResult.IsNewUser);

                return Ok(new
                {
                    success = true,
                    data = loginResult,
                    message = loginResult.IsNewUser ? "新用户注册并登录成功" : "用户登录成功"
                });
            }
            catch (BusinessException ex)
            {
                _logger.LogError(ex, "微信OAuth登录业务异常: {Message}", ex.Message);
                return BadRequest(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "微信OAuth登录系统异常: {Message}", ex.Message);
                return StatusCode(500, new { success = false, message = "登录失败", detail = ex.Message });
            }
        }

        /// <summary>
        /// 手动刷新微信基础access_token
        /// </summary>
        /// <returns>刷新结果</returns>
        [HttpPost("token/refresh")]
        public async Task<IActionResult> RefreshBasicAccessToken()
        {
            _logger.LogInformation("收到手动刷新微信基础access_token请求");

            try
            {
                var success = await _wechatOAuthService.RefreshBasicAccessTokenAsync();

                if (success)
                {
                    _logger.LogInformation("微信基础access_token刷新成功");
                    return Ok(new { success = true, message = "微信基础access_token刷新成功" });
                }
                else
                {
                    _logger.LogWarning("微信基础access_token刷新失败");
                    return BadRequest(new { success = false, message = "微信基础access_token刷新失败" });
                }
            }
            catch (BusinessException ex)
            {
                _logger.LogError(ex, "刷新微信基础access_token业务异常: {Message}", ex.Message);
                return BadRequest(new { success = false, message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新微信基础access_token系统异常: {Message}", ex.Message);
                return StatusCode(500, new { success = false, message = "刷新失败", detail = ex.Message });
            }
        }

        /// <summary>
        /// 获取当前微信基础access_token状态
        /// </summary>
        /// <returns>Token状态</returns>
        [HttpGet("token/status")]
        public async Task<IActionResult> GetTokenStatus()
        {
            try
            {
                var appId = WxSetting.AppId;
                if (string.IsNullOrEmpty(appId))
                {
                    return BadRequest(new { success = false, message = "微信AppID未配置" });
                }

                var token = await _wechatAccessTokenService.GetValidAccessTokenAsync(appId, "basic");

                if (token != null)
                {
                    return Ok(new
                    {
                        success = true,
                        data = new
                        {
                            hasValidToken = true,
                            accessToken = token
                        },
                        message = "Token状态正常"
                    });
                }
                else
                {
                    return Ok(new
                    {
                        success = true,
                        data = new
                        {
                            hasValidToken = false,
                            expiresAt = (DateTime?)null,
                            remainingMinutes = 0
                        },
                        message = "无有效Token"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = "获取Token状态失败", detail = ex.Message });
            }
        }

        /// <summary>
        /// 微信服务器验证接口（用于配置微信公众号）
        /// </summary>
        /// <param name="signature">微信加密签名</param>
        /// <param name="timestamp">时间戳</param>
        /// <param name="nonce">随机数</param>
        /// <param name="echostr">随机字符串</param>
        /// <returns>验证结果</returns>
        [HttpGet("verify")]
        public IActionResult VerifyWechatServer(string signature, string timestamp, string nonce, string echostr)
        {
            try
            {
                // 这里应该实现微信服务器验证逻辑
                // 暂时直接返回echostr用于开发测试
                // 生产环境需要验证signature

                if (string.IsNullOrEmpty(echostr))
                {
                    return BadRequest("Invalid verification request");
                }

                return Content(echostr, "text/plain");
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = "验证失败", detail = ex.Message });
            }
        }
    }
}
