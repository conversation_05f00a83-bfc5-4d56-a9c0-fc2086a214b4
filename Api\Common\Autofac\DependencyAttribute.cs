namespace Common.Autofac
{
    /// <summary>
    /// 依赖注入标记特性，用于自动注册服务
    /// </summary>
    /// <remarks>
    /// 构造函数，初始化服务生命周期类型
    /// </remarks>
    /// <param name="dependencyType">服务生命周期类型，默认为Scoped</param>
    /// <example>
    /// [Dependency(DependencyType.Singleton)]
    /// public class MyService { ... }
    /// </example>
    [AttributeUsage(AttributeTargets.Class, Inherited = false)]
    public class DependencyAttribute(DependencyType dependencyType = DependencyType.Scoped) : Attribute
    {
        /// <summary>
        /// 获取或设置服务的生命周期类型
        /// </summary>
        public DependencyType DependencyType { get; } = dependencyType;
    }

    /// <summary>
    /// 服务生命周期类型枚举
    /// </summary>
    public enum DependencyType
    {
        /// <summary>
        /// 单例模式，全局唯一实例
        /// </summary>
        Singleton,

        /// <summary>
        /// 作用域模式，同一请求范围内共享实例
        /// </summary>
        Scoped,

        /// <summary>
        /// 瞬态模式，每次请求创建新实例
        /// </summary>
        Transient
    }
}