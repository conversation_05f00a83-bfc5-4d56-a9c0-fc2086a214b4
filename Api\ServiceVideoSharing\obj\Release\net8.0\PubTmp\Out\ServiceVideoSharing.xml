<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ServiceVideoSharing</name>
    </assembly>
    <members>
        <member name="T:ServiceVideoSharing.Controllers.Attributes.CacheType">
            <summary>
            缓存类型枚举
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Attributes.CacheExpireType">
            <summary>
            缓存过期类型
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Attributes.CacheScope">
            <summary>
            缓存范围枚举
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute">
            <summary>
            缓存Action [配置特性]
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.IsToken">
            <summary>
            是否在缓存Key内添加用户信息
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.CacheSeconds">
            <summary>
            缓存时间(秒)
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.CacheType">
            <summary>
            缓存类型
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.RedisInstance">
            <summary>
            Redis实例名
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.CachePrefix">
            <summary>
            缓存前缀
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.CacheScope">
            <summary>
            缓存范围
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.ExpireType">
            <summary>
            过期类型
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.TimeScope">
            <summary>
            时间范围(绝对时间模式下使用)
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.TimeScopeCount">
            <summary>
            时间范围计数(绝对时间模式下使用)
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.UseHashStructure">
            <summary>
            是否使用Hash结构存储
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.#ctor">
            <summary>
            默认构造函数
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.#ctor(System.Int32,ServiceVideoSharing.Controllers.Attributes.Whether)">
            <summary>
            构造函数 - 基本参数
            </summary>
            <param name="cacheTime">缓存时间[秒]</param>
            <param name="isToken">是否需要将Token加入缓存Key</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.#ctor(System.Int32,ServiceVideoSharing.Controllers.Attributes.CacheType,ServiceVideoSharing.Controllers.Attributes.CacheScope,System.String,ServiceVideoSharing.Controllers.Attributes.Whether,ServiceVideoSharing.Controllers.Attributes.CacheExpireType,System.Boolean)">
            <summary>
            构造函数 - 完整参数
            </summary>
            <param name="cacheTime">缓存时间[秒]</param>
            <param name="cacheType">缓存类型(内存/Redis)</param>
            <param name="cacheScope">缓存范围(全局/用户/角色/部门)</param>
            <param name="redisInstance">Redis实例名称</param>
            <param name="isToken">是否需要将Token加入缓存Key</param>
            <param name="expireType">过期类型</param>
            <param name="useHashStructure">是否使用Hash结构</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute.#ctor(Common.Redis.RedisHelper.TimeScope,System.Int32,ServiceVideoSharing.Controllers.Attributes.CacheType,ServiceVideoSharing.Controllers.Attributes.CacheScope,System.String,ServiceVideoSharing.Controllers.Attributes.Whether,System.Boolean)">
            <summary>
            构造函数 - 绝对时间模式
            </summary>
            <param name="timeScope">时间范围</param>
            <param name="timeScopeCount">时间范围计数</param>
            <param name="cacheType">缓存类型(内存/Redis)</param>
            <param name="cacheScope">缓存范围(全局/用户/角色/部门)</param>
            <param name="redisInstance">Redis实例名称</param>
            <param name="isToken">是否需要将Token加入缓存Key</param>
            <param name="useHashStructure">是否使用Hash结构</param>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Attributes.CardOpenValidationAttribute">
            <summary>
            CardOpen验证特性
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Attributes.LogAttribute">
            <summary>
            日志记录特性
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.LogAttribute.Module">
            <summary>
            模块名称
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.LogAttribute.Description">
            <summary>
            操作描述
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.LogAttribute.Level">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.LogAttribute.LogParams">
            <summary>
            是否记录参数
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.Attributes.LogAttribute.LogResult">
            <summary>
            是否记录返回值
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Attributes.PermissionAttribute">
            <summary>
            权限特性 - 用于标记需要JWT Token验证的接口
            </summary>
        </member>
        <member name="F:ServiceVideoSharing.Controllers.BasisController.BaseController.UserCacheKeyPrefix">
            <summary>
            用户缓存键前缀
            </summary>
        </member>
        <member name="F:ServiceVideoSharing.Controllers.BasisController.BaseController.UserCacheExpiration">
            <summary>
            用户缓存过期时间
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.BasisController.BaseController.IP">
            <summary>
            当前请求的IP
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.GetHeader(System.String)">
            <summary>
            获取请求头信息
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.TryGetHeader(System.String,System.String)">
            <summary>
            尝试获取请求头信息，如果不存在则返回默认值
            </summary>
            <param name="key">请求头名称</param>
            <param name="defaultValue">默认值</param>
            <returns>请求头值或默认值</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.ValidateToken(System.String)">
            <summary>
            验证并解析JWT Token
            </summary>
            <returns>ClaimsPrincipal对象</returns>
            <exception cref="T:Common.Exceptions.AuthorizationException">当token验证失败时抛出</exception>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.GetCurrentUserId">
            <summary>
            从JWT中获取当前用户ID
            </summary>
            <returns>用户ID</returns>
            <exception cref="T:Common.Exceptions.AuthorizationException">当无法获取用户ID时抛出</exception>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.GetCurrentUserInfo">
            <summary>
            获取当前用户信息（包含用户ID、用户名和用户类型）
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.GetCurrentJwtUserInfo">
            <summary>
            从JWT Token中获取完整的用户信息
            </summary>
            <returns>JWT中的用户信息</returns>
            <exception cref="T:Common.Exceptions.AuthorizationException">当无法获取用户信息时抛出</exception>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.GetCurrentUserType">
            <summary>
            获取当前用户类型
            </summary>
            <returns>用户类型：1-超级管理员，2-管理员，3-员工</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.IsSuperAdmin">
            <summary>
            检查当前用户是否为超级管理员
            </summary>
            <returns>是否为超级管理员</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.IsAdminUser">
            <summary>
            检查当前用户是否为管理员（包括超级管理员）
            </summary>
            <returns>是否为管理员</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.IsEmployee">
            <summary>
            检查当前用户是否为员工
            </summary>
            <returns>是否为员工</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.GetCurrentUserName">
            <summary>
            从JWT中获取当前用户名
            </summary>
            <returns>用户名</returns>
            <exception cref="T:Common.Exceptions.AuthorizationException">当无法获取用户名时抛出</exception>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.IsAdmin">
            <summary>
            检查当前用户是否为管理员
            </summary>
            <returns>是否为管理员</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.GetClaimValue(System.String)">
            <summary>
            获取指定类型的声明值
            </summary>
            <param name="claimType">声明类型</param>
            <returns>声明值</returns>
            <exception cref="T:Common.Exceptions.AuthorizationException">当无法获取指定声明时抛出</exception>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.TryGetClaimValue(System.String,System.String)">
            <summary>
            尝试获取指定类型的声明值，失败时返回默认值而不抛出异常
            </summary>
            <param name="claimType">声明类型</param>
            <param name="defaultValue">获取失败时返回的默认值</param>
            <returns>声明值或默认值</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.GetCurrentUserInfoAsync(BLL.SysService.SysUserService)">
            <summary>
            从JWT中获取当前用户信息
            </summary>
            <param name="userService">用户服务</param>
            <returns>用户信息DTO</returns>
            <exception cref="T:System.ArgumentNullException">当用户服务为空时抛出</exception>
            <exception cref="T:Common.Exceptions.AuthorizationException">当无法获取用户ID时抛出</exception>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.GetJwtToken">
            <summary>
            获取JWT令牌
            </summary>
            <returns>JWT令牌字符串</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.Success``1(``0,System.String)">
            <summary>
            创建成功响应
            </summary>
            <typeparam name="T">响应数据类型</typeparam>
            <param name="data">响应数据</param>
            <param name="message">响应消息</param>
            <returns>成功响应</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.Success(System.String)">
            <summary>
            创建成功响应（无数据）
            </summary>
            <param name="message">响应消息</param>
            <returns>成功响应</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.Fail``1(System.String,System.Int32)">
            <summary>
            创建失败响应
            </summary>
            <typeparam name="T">响应数据类型</typeparam>
            <param name="message">错误消息</param>
            <param name="code">错误码</param>
            <returns>失败响应</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.Fail(System.String,System.Int32)">
            <summary>
            创建失败响应（无数据）
            </summary>
            <param name="message">错误消息</param>
            <param name="code">错误码</param>
            <returns>失败响应</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.BadRequestResult(System.String)">
            <summary>
            创建BadRequest响应
            </summary>
            <param name="message">错误消息</param>
            <returns>BadRequest响应</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.NotFoundResult(System.String)">
            <summary>
            创建NotFound响应
            </summary>
            <param name="message">错误消息</param>
            <returns>NotFound响应</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.UnauthorizedResult(System.String)">
            <summary>
            创建Unauthorized响应
            </summary>
            <param name="message">错误消息</param>
            <returns>Unauthorized响应</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BaseController.ForbiddenResult(System.String)">
            <summary>
            创建Forbidden响应
            </summary>
            <param name="message">错误消息</param>
            <returns>Forbidden响应</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BasisController.UploadFileAsync(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            本地单文件上传
            </summary>
            <param name="file">文件</param>
            <returns></returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.BasisController.BasisController.UploadFilesAsync(System.Collections.Generic.List{Microsoft.AspNetCore.Http.IFormFile})">
            <summary>
            本地多文件上传
            </summary>
            <param name="files">文件列表</param>
            <returns></returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result">
            <summary>
            接口返回规范
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result.Code">
            <summary>
            响应码
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result.Msg">
            <summary>
            返回消息
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result.Success">
            <summary>
            返回结果
            </summary>
        </member>
        <member name="P:ServiceVideoSharing.Controllers.BasisController.ResuItEntity.Result`1.Data">
            <summary>
            接口返回数据
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Filter.ExceptionFilter">
            <summary>
            全局异常过滤器
            用于捕获和处理应用程序中所有未处理的异常
            </summary>
            <remarks>
            构造函数，注入日志服务
            </remarks>
            <param name="logService">日志服务</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Filter.ExceptionFilter.#ctor(BLL.SysService.SysLogService)">
            <summary>
            全局异常过滤器
            用于捕获和处理应用程序中所有未处理的异常
            </summary>
            <remarks>
            构造函数，注入日志服务
            </remarks>
            <param name="logService">日志服务</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Filter.ExceptionFilter.OnExceptionAsync(Microsoft.AspNetCore.Mvc.Filters.ExceptionContext)">
            <summary>
            异常发生时的处理方法
            </summary>
            <param name="context">异常上下文,包含异常信息和HTTP上下文</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Filter.ExceptionFilter.GetFullExceptionMessage(System.Exception)">
            <summary>
            获取完整的异常信息,包括所有内部异常
            </summary>
            <param name="ex">异常对象</param>
            <returns>包含所有内部异常信息的字符串,用箭头连接</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Filter.LogActionFilter">
            <summary>
            日志操作过滤器
            用于处理带有LogAttribute特性的Action
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Filter.LogActionFilter.#ctor(BLL.SysService.SysLogService)">
            <summary>
            日志操作过滤器
            用于处理带有LogAttribute特性的Action
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Filter.LogActionFilter.GetLogAttribute(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)">
            <summary>
            获取Action的Log特性
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Filter.LogActionFilter.LogActionExecution(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,ServiceVideoSharing.Controllers.Attributes.LogAttribute,System.Exception,System.Int64)">
            <summary>
            记录Action执行日志
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Middleware.CachingMiddleware">
            <summary>
            缓存中间件
            </summary>
            <param name="next"></param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.CachingMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            缓存中间件
            </summary>
            <param name="next"></param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.CachingMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            中间件处理方法
            </summary>
            <param name="context">HTTP上下文</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.CachingMiddleware.GenerateCacheKey(Microsoft.AspNetCore.Http.HttpContext,ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute)">
            <summary>
            生成缓存键
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="attribute">缓存特性</param>
            <returns>基础键和哈希字段</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.CachingMiddleware.GenerateShortCode(System.String)">
            <summary>
            生成短码
            </summary>
            <param name="input">输入字符串</param>
            <returns>生成的短码</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.CachingMiddleware.TryGetAndReturnCachedResponse(Microsoft.AspNetCore.Http.HttpContext,ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute,System.String,System.String)">
            <summary>
            尝试获取缓存并返回响应
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="attribute">缓存特性</param>
            <param name="baseKey">基础键</param>
            <param name="hashField">哈希字段</param>
            <returns>是否成功获取缓存</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.CachingMiddleware.SetCache(System.String,System.String,System.String,ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute)">
            <summary>
            设置缓存
            </summary>
            <param name="baseKey">基础键</param>
            <param name="hashField">哈希字段</param>
            <param name="responseContent">响应内容</param>
            <param name="attribute">缓存特性</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.CachingMiddleware.SetRedisExpiry(System.String,ServiceVideoSharing.Controllers.Attributes.ActionCacheAttribute)">
            <summary>
            设置Redis缓存过期时间
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Middleware.CachingMiddlewareExtensions">
            <summary>
            中间件扩展方法
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.CachingMiddlewareExtensions.UseCaching(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            添加缓存中间件到应用程序管道
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Middleware.ExceptionMiddleWare">
            <summary>
            全局异常处理中间件
            用于捕获和处理管道中未被其他过滤器处理的异常
            </summary>
            <param name="next">请求处理委托</param>
            <param name="logger">ILogger实例</param>
            <param name="environment">Web主机环境</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.ExceptionMiddleWare.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.Extensions.Logging.ILogger{ServiceVideoSharing.Controllers.Middleware.ExceptionMiddleWare},Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            全局异常处理中间件
            用于捕获和处理管道中未被其他过滤器处理的异常
            </summary>
            <param name="next">请求处理委托</param>
            <param name="logger">ILogger实例</param>
            <param name="environment">Web主机环境</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.ExceptionMiddleWare.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            处理HTTP请求
            </summary>
            <param name="context">HTTP上下文</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.ExceptionMiddleWare.HandleExceptionAsync(Microsoft.AspNetCore.Http.HttpContext,System.Exception)">
            <summary>
             处理异常
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="ex">异常对象</param>
            <returns></returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.ExceptionMiddleWare.WriteLogAsync(Microsoft.AspNetCore.Http.HttpContext,Entity.Dto.CreateLogDto,System.Exception)">
            <summary>
            写入文本日志和数据库日志
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="logDto">日志信息</param>
            <param name="ex">异常信息</param>
            <returns></returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.ExceptionMiddleWare.DetermineStatusAndMessage(System.Exception)">
            <summary>
            确定状态码和消息 返回是否需要记录文本和数据库日志
            </summary>
            <param name="ex">异常对象</param>
            <returns>状态码, 消息, 是否需要记录文本和数据库日志</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.ExceptionMiddleWare.GetFullExceptionMessage(System.Exception)">
            <summary>
            递归获取异常及其内部异常的完整消息
            </summary>
            <param name="ex">异常对象</param>
            <returns>完整的异常消息</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Middleware.ExceptionMiddlewareExtensions">
            <summary>
            异常中间件扩展方法
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.ExceptionMiddlewareExtensions.UseExceptionMiddleware(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            使用全局异常处理中间件
            </summary>
            <param name="builder">应用构建器</param>
            <returns>应用构建器</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Middleware.PermissionMiddleware">
            <summary>
            权限中间件
            用于验证JWT Token的正确性，确保只有持有有效Token的用户能访问受保护的API端点
            </summary>
            <param name="next">请求处理管道中的下一个中间件</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.PermissionMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            权限中间件
            用于验证JWT Token的正确性，确保只有持有有效Token的用户能访问受保护的API端点
            </summary>
            <param name="next">请求处理管道中的下一个中间件</param>
        </member>
        <member name="F:ServiceVideoSharing.Controllers.Middleware.PermissionMiddleware._next">
            <summary>
            请求处理管道中的下一个中间件
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.PermissionMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            中间件处理方法，验证JWT Token并处理请求
            </summary>
            <param name="context">HTTP上下文</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.PermissionMiddleware.ValidateTokenAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            验证JWT Token
            </summary>
            <param name="context">HTTP上下文</param>
            <returns>验证是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.PermissionMiddleware.IsAnonymousEndpoint(Microsoft.AspNetCore.Http.Endpoint)">
            <summary>
            检查终结点是否允许匿名访问
            </summary>
            <param name="endpoint">HTTP终结点</param>
            <returns>如果允许匿名访问则返回true，否则返回false</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.PermissionMiddleware.HasPermissionAttribute(Microsoft.AspNetCore.Http.Endpoint)">
            <summary>
            检查终结点是否需要权限验证
            </summary>
            <param name="endpoint">HTTP终结点</param>
            <returns>如果需要权限验证则返回true，否则返回false</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.PermissionMiddleware.CreateClaimsPrincipal(Common.JWT.UserInfo)">
            <summary>
            创建用户身份主体
            </summary>
            <param name="userInfo">用户信息</param>
            <returns>用户身份主体</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.PermissionMiddleware.HandleUnauthorized(Microsoft.AspNetCore.Http.HttpContext,System.String)">
            <summary>
            处理未授权响应（401）
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="message">错误消息</param>
            <returns>异步任务</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.PermissionMiddleware.WriteErrorResponse(Microsoft.AspNetCore.Http.HttpContext,System.Int32,System.String,System.Int32)">
            <summary>
            写入HTTP响应
            </summary>
            <param name="context">HTTP上下文</param>
            <param name="code">业务状态码</param>
            <param name="message">响应消息</param>
            <param name="statusCode">HTTP状态码</param>
            <returns>异步任务</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Middleware.PermissionMiddlewareExtensions">
            <summary>
            权限中间件扩展方法
            提供将权限中间件添加到应用程序请求管道的扩展方法
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.PermissionMiddlewareExtensions.UsePermissionValidation(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            向应用程序请求管道添加权限验证中间件
            </summary>
            <param name="builder">应用程序构建器</param>
            <returns>应用程序构建器</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.Middleware.RequestSizeVerifyMiddleWare">
            <summary>
            验证请求是否超过设置的最大值
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.RequestSizeVerifyMiddleWare.#ctor(Microsoft.AspNetCore.Http.RequestDelegate)">
            <summary>
            验证请求是否超过设置的最大值
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.Middleware.RequestSizeVerifyMiddleWare.Verify(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            验证请求是否超过设置的最大值
            </summary>
            <param name="context">HTTP上下文</param>
            <returns>是否验证通过</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.SysControllers.AuthController">
            <summary>
            认证控制器 - 处理管理员和员工认证相关的请求
            </summary>
            <remarks>
            构造函数 - 依赖注入用户服务和日志服务
            </remarks>
            <param name="userService">用户服务实例</param>
            <param name="logService">日志服务实例</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.AuthController.#ctor(BLL.SysService.SysUserService,BLL.SysService.SysLogService)">
            <summary>
            认证控制器 - 处理管理员和员工认证相关的请求
            </summary>
            <remarks>
            构造函数 - 依赖注入用户服务和日志服务
            </remarks>
            <param name="userService">用户服务实例</param>
            <param name="logService">日志服务实例</param>
        </member>
        <member name="F:ServiceVideoSharing.Controllers.SysControllers.AuthController._userService">
            <summary>
            用户服务接口
            </summary>
        </member>
        <member name="F:ServiceVideoSharing.Controllers.SysControllers.AuthController._logService">
            <summary>
            日志服务接口
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.AuthController.Login(Entity.Dto.SysLoginRequestDto)">
            <summary>
            用户登录接口
            </summary>
            <param name="loginRequest">登录请求DTO，包含用户名和密码</param>
            <returns>
            返回登录响应结果:
            - 成功: 返回200状态码和用户Token信息
            - 失败: 返回500状态码和错误信息
            </returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.AuthController.Logout">
            <summary>
            用户登出接口
            </summary>
            <returns>登出结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.AuthController.GetUserInfo">
            <summary>
            获取当前登录用户信息接口
            </summary>
            <returns>
            返回用户信息结果:
            - 成功: 返回200状态码和用户详细信息
            - 未授权: 返回401状态码
            - 失败: 返回500状态码和错误信息
            </returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.SysControllers.LogController">
            <summary>
            系统日志控制器
            提供日志查询、详情和清理等功能
            </summary>
            <remarks>
            构造函数
            </remarks>
            <param name="logService">日志服务</param>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.LogController.#ctor(BLL.SysService.SysLogService)">
            <summary>
            系统日志控制器
            提供日志查询、详情和清理等功能
            </summary>
            <remarks>
            构造函数
            </remarks>
            <param name="logService">日志服务</param>
        </member>
        <member name="F:ServiceVideoSharing.Controllers.SysControllers.LogController._logService">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.LogController.QueryAsync(DAL.SysDAL.SysLogDAL.Queryable)">
            <summary>
            分页查询日志列表
            </summary>
            <param name="queryable">查询条件</param>
            <returns>日志列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.LogController.GetAsync(System.String)">
            <summary>
            获取日志详情
            </summary>
            <param name="id">日志ID</param>
            <returns>日志详情</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.LogController.ExportLogsAsync(DAL.SysDAL.SysLogDAL.Queryable,BLL.SysService.Exports.ExportRequestDto)">
            <summary>
            导出日志列表
            </summary>
            <param name="queryable">查询条件</param>
            <param name="exportRequest">导出配置</param>
            <returns>CSV文件</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.LogController.ClearLogsAsync(Entity.Dto.LogClearDto)">
            <summary>
            清理指定日期之前的日志
            </summary>
            <param name="clearDto">清理日志参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.SysControllers.SysUserController">
            <summary>
            用户管理控制器
            提供用户的增删改查、角色分配、密码管理等功能
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.#ctor(BLL.SysService.SysUserService,BLL.SysService.SysLogService)">
            <summary>
            用户管理控制器
            提供用户的增删改查、角色分配、密码管理等功能
            </summary>
        </member>
        <member name="F:ServiceVideoSharing.Controllers.SysControllers.SysUserController._userService">
            <summary>
            用户服务接口
            </summary>
        </member>
        <member name="F:ServiceVideoSharing.Controllers.SysControllers.SysUserController._logService">
            <summary>
            日志服务接口
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.CreateAsync(Entity.Dto.SysCreateUserDto)">
            <summary>
            创建新用户
            </summary>
            <param name="input">创建用户请求</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.UpdateAsync(Entity.Dto.SysUpdateUserDto)">
            <summary>
            更新用户信息
            </summary>
            <param name="input">更新用户请求</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.DeleteAsync(System.String)">
            <summary>
            删除指定用户
            </summary>
            <param name="id">用户ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.GetAsync(System.String)">
            <summary>
            获取用户详细信息
            </summary>
            <param name="id">用户ID</param>
            <returns>用户详细信息</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.ChangePasswordAsync(Entity.Dto.SysChangePasswordDto)">
            <summary>
            修改用户密码
            </summary>
            <param name="input">修改密码请求</param>
            <returns>修改结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.ResetPasswordAsync(Entity.Dto.SysResetPasswordDto)">
            <summary>
            重置用户密码
            </summary>
            <param name="input">重置密码请求</param>
            <returns>重置结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.ValidateUserCreationPermission(System.Byte,System.Byte)">
            <summary>
            验证用户创建权限
            </summary>
            <param name="targetUserType">要创建的用户类型</param>
            <param name="currentUserType">当前用户类型</param>
            <exception cref="T:System.UnauthorizedAccessException">权限不足时抛出异常</exception>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.GetUserTypeDescription(System.Byte)">
            <summary>
            获取用户类型描述
            </summary>
            <param name="userType">用户类型</param>
            <returns>用户类型描述</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.GetAdministratorsAsync(System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Byte})">
            <summary>
            获取管理员列表（带统计数据）
            </summary>
            <param name="startTime">统计开始时间</param>
            <param name="endTime">统计结束时间</param>
            <param name="pageIndex">页码，默认为1</param>
            <param name="pageSize">每页大小，默认为20</param>
            <param name="userName">用户名（模糊查询）</param>
            <param name="realName">真实姓名（模糊查询）</param>
            <param name="status">用户状态过滤</param>
            <returns>管理员列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.GetEmployeesAsync(System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Byte})">
            <summary>
            获取员工列表（带统计数据）
            </summary>
            <param name="startTime">统计开始时间</param>
            <param name="endTime">统计结束时间</param>
            <param name="pageIndex">页码，默认为1</param>
            <param name="pageSize">每页大小，默认为20</param>
            <param name="userName">用户名（模糊查询）</param>
            <param name="realName">真实姓名（模糊查询）</param>
            <param name="status">用户状态过滤</param>
            <returns>员工列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.GetSubordinatesAsync(System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Byte})">
            <summary>
            获取当前用户的下级列表（带统计数据）
            </summary>
            <param name="startTime">统计开始时间</param>
            <param name="endTime">统计结束时间</param>
            <param name="pageIndex">页码，默认为1</param>
            <param name="pageSize">每页大小，默认为20</param>
            <param name="userName">用户名（模糊查询）</param>
            <param name="realName">真实姓名（模糊查询）</param>
            <param name="status">用户状态过滤</param>
            <returns>下级列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.SysControllers.SysUserController.GetSubordinatesByIdAsync(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime},System.Int32,System.Int32,System.String,System.String,System.Nullable{System.Byte})">
            <summary>
            获取指定用户的下级列表（带统计数据）
            </summary>
            <param name="id">用户ID</param>
            <param name="startTime">统计开始时间</param>
            <param name="endTime">统计结束时间</param>
            <param name="pageIndex">页码，默认为1</param>
            <param name="pageSize">每页大小，默认为20</param>
            <param name="userName">用户名（模糊查询）</param>
            <param name="realName">真实姓名（模糊查询）</param>
            <param name="status">用户状态过滤</param>
            <returns>下级列表</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.BatchController">
            <summary>
            批次管理控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.BatchController.#ctor(BLL.VideoService.BatchService)">
            <summary>
            批次管理控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.BatchController.Add(Entity.Dto.VideoDto.BatchCreateDto)">
            <summary>
            添加批次
            </summary>
            <param name="createDto">创建批次DTO</param>
            <returns>批次ID</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.BatchController.Delete(System.Int32)">
            <summary>
            删除批次
            </summary>
            <param name="batchId">批次ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.BatchController.Get(System.Int32)">
            <summary>
            获取批次详情
            </summary>
            <param name="batchId">批次ID</param>
            <returns>批次详情</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.BatchController.GetList(Entity.Dto.VideoDto.BatchQueryDto)">
            <summary>
            分页查询批次列表（带权限过滤）
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>批次列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.BatchController.GetBatchStatistics(System.Int32)">
            <summary>
            获取批次统计信息
            支持基于用户权限的数据过滤：
            - 超级管理员：可查看所有用户数据
            - 管理员：可查看所有员工及其用户的数据
            - 员工：只能查看自己绑定用户的数据
            </summary>
            <param name="batchId">批次ID</param>
            <returns>批次统计信息</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.DashboardController">
            <summary>
            仪表板控制器
            提供类似截图的综合统计仪表板功能
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.#ctor(BLL.VideoService.DashboardService)">
            <summary>
            仪表板控制器
            提供类似截图的综合统计仪表板功能
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetDashboard(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取综合仪表板数据（主接口）
            返回类似截图的完整仪表板数据，包括：
            - 数据汇总（会员总数、今日新增会员、订单总数）
            - 标签统计（未指定标签用户数）
            - 课程统计（观看人数、完播人数、完播率）
            - 答题统计（答题人数、正确人数、正确率）
            - 红包统计（答题红包数、答题红包金额）
            </summary>
            <param name="startDate">开始时间</param>
            <param name="endDate">结束时间</param>
            <returns>综合仪表板数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetSummary(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取数据汇总
            包括会员总数、今日新增会员、订单总数等关键指标
            </summary>
            <param name="startDate">开始时间</param>
            <param name="endDate">结束时间</param>
            <returns>数据汇总</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetTagStatistics">
            <summary>
            获取标签统计
            包括各标签的用户数量和未指定标签的用户数量
            </summary>
            <returns>标签统计列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetCourseStatistics(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取课程统计
            包括观看人数、完播人数、完播率等
            </summary>
            <param name="startDate">开始时间</param>
            <param name="endDate">结束时间</param>
            <returns>课程统计</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetAnswerStatistics(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取答题统计
            包括答题人数、正确人数、正确率等
            </summary>
            <param name="startDate">开始时间</param>
            <param name="endDate">结束时间</param>
            <returns>答题统计</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetRewardStatistics(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取红包统计
            包括红包数量、红包金额等
            </summary>
            <param name="startDate">开始时间</param>
            <param name="endDate">结束时间</param>
            <returns>红包统计</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetOrderStatistics(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取订单统计
            包括订单数量、订单金额等
            </summary>
            <param name="startDate">开始时间</param>
            <param name="endDate">结束时间</param>
            <returns>订单统计</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetTodaySnapshot">
            <summary>
            获取今日数据快照
            快速获取今日的所有关键数据
            </summary>
            <returns>今日数据快照</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetThisMonthSnapshot">
            <summary>
            获取本月数据快照
            快速获取本月的所有关键数据
            </summary>
            <returns>本月数据快照</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetKeyMetrics(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取关键指标摘要
            提供最核心的几个指标数据
            </summary>
            <param name="startDate">开始时间</param>
            <param name="endDate">结束时间</param>
            <returns>关键指标摘要</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.DashboardController.GetTodayVsYesterday">
            <summary>
            获取数据对比（今日vs昨日）
            提供今日和昨日的数据对比
            </summary>
            <returns>数据对比</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController">
            <summary>
            统计控制器（基于实时统计）
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.#ctor(BLL.VideoService.StatisticsService,BLL.VideoService.DashboardService,BLL.VideoService.UserBatchRecordService)">
            <summary>
            统计控制器（基于实时统计）
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetUserDailyStatistics(Entity.Dto.VideoDto.UserDailyStatisticsQueryDto)">
            <summary>
            获取用户每日统计数据（分页）
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>分页统计数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetUserSummary(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取用户统计汇总数据
            </summary>
            <param name="userId">用户ID</param>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>汇总数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetUsersSummary(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            批量获取用户统计汇总数据
            </summary>
            <param name="userIds">用户ID列表（逗号分隔）</param>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>汇总数据列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetEmployeeSummary(System.Int32,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取员工负责用户的统计汇总
            </summary>
            <param name="employeeId">员工ID</param>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>汇总数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetDailyTrend(System.String,System.Nullable{System.Int32},System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取每日统计趋势
            </summary>
            <param name="userIds">用户ID列表（逗号分隔，可选）</param>
            <param name="employeeId">员工ID（可选）</param>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>趋势数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetStatisticsOverview(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取统计概览数据（用于仪表板）
            </summary>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>概览数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetUserStatistics(System.String,System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取用户统计数据（兼容性接口）
            </summary>
            <param name="userId">用户ID</param>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>用户统计数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetMyStatistics(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取我的统计数据（兼容性接口）
            </summary>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>我的统计数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetStatisticsSummary(Entity.Dto.VideoDto.StatisticsSummaryQueryDto)">
            <summary>
            获取统计汇总数据（兼容性接口）
            </summary>
            <param name="summaryDto">汇总查询DTO</param>
            <returns>汇总统计数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetDashboard(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取仪表板数据
            </summary>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>仪表板数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetKeyMetrics(System.Nullable{System.DateTime},System.Nullable{System.DateTime})">
            <summary>
            获取仪表板关键指标汇总
            </summary>
            <param name="startDate">开始日期</param>
            <param name="endDate">结束日期</param>
            <returns>关键指标汇总</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.StatisticsController.GetTodaySnapshot">
            <summary>
            获取今日数据快照
            </summary>
            <returns>今日数据快照</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController">
            <summary>
            系统配置控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.#ctor(BLL.VideoService.SystemConfigService)">
            <summary>
            系统配置控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.AddSystemConfig(Entity.Dto.VideoDto.SystemConfigCreateDto)">
            <summary>
            添加系统配置
            </summary>
            <param name="createDto">创建系统配置DTO</param>
            <returns>配置ID</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.UpdateSystemConfig(Entity.Dto.VideoDto.SystemConfigUpdateDto)">
            <summary>
            更新系统配置
            </summary>
            <param name="updateDto">更新系统配置DTO</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.DeleteSystemConfig(System.Int32)">
            <summary>
            删除系统配置
            </summary>
            <param name="configId">配置ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetSystemConfig(System.Int32)">
            <summary>
            获取系统配置详情
            </summary>
            <param name="configId">配置ID</param>
            <returns>配置详情</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetSystemConfigByKey(System.String)">
            <summary>
            根据配置键获取配置
            </summary>
            <param name="configKey">配置键</param>
            <returns>配置详情</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetConfigValue(System.String)">
            <summary>
            获取配置值
            </summary>
            <param name="configKey">配置键</param>
            <returns>配置值</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.SetConfigValue(System.String,System.String)">
            <summary>
            设置配置值
            </summary>
            <param name="configKey">配置键</param>
            <param name="configValue">配置值</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetSystemConfigPagedList(Entity.Dto.VideoDto.SystemConfigQueryDto)">
            <summary>
            分页查询系统配置列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>配置列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetAllSystemConfigs">
            <summary>
            获取所有系统配置
            </summary>
            <returns>配置列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetConfigsByGroup(System.String)">
            <summary>
            根据分组获取配置
            </summary>
            <param name="groupName">配置分组</param>
            <returns>配置列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetConfigsByType(System.String)">
            <summary>
            根据配置类型获取配置
            </summary>
            <param name="configType">配置类型</param>
            <returns>配置列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.BatchUpdateConfigs(System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            批量更新配置
            </summary>
            <param name="configUpdates">配置更新字典</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetConfigGroups">
            <summary>
            获取所有配置分组
            </summary>
            <returns>分组列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetWechatConfigs">
            <summary>
            获取微信相关配置
            </summary>
            <returns>微信配置</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetRewardConfigs">
            <summary>
            获取红包相关配置
            </summary>
            <returns>红包配置</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.GetSystemConfigs">
            <summary>
            获取系统相关配置
            </summary>
            <returns>系统配置</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.SystemConfigController.UpdateConfigStatus(System.Int32,System.Boolean)">
            <summary>
            启用/禁用配置
            </summary>
            <param name="configId">配置ID</param>
            <param name="isEnabled">是否启用</param>
            <returns>是否成功</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.UserAuditController">
            <summary>
            用户审核控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserAuditController.#ctor(BLL.VideoService.UserAuditService,BLL.VideoService.UserService)">
            <summary>
            用户审核控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserAuditController.AuditUser(System.String,Entity.Dto.VideoDto.UserAuditDto)">
            <summary>
            员工审核用户
            </summary>
            <param name="userId">用户ID</param>
            <param name="auditDto">审核信息</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserAuditController.GetPendingUsers">
            <summary>
            获取当前员工的待审核用户列表
            </summary>
            <returns>待审核用户列表</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController">
            <summary>
            用户批次记录控制器
            提供观看、答题、红包一体化的API接口
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.#ctor(BLL.VideoService.UserBatchRecordService)">
            <summary>
            用户批次记录控制器
            提供观看、答题、红包一体化的API接口
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.CreateOrGetRecord(Entity.Dto.VideoDto.UserBatchRecordCreateDto)">
            <summary>
            创建或获取用户批次记录
            用户进入视频页面时调用，确保有记录存在
            </summary>
            <param name="createDto">创建记录DTO</param>
            <returns>用户批次记录</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetRecord(System.String,System.Int32)">
            <summary>
            获取用户批次记录
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>用户批次记录</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetUserRecords(System.String)">
            <summary>
            获取用户的所有批次记录
            </summary>
            <param name="userId">用户ID</param>
            <returns>用户批次记录列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.UpdateWatchProgress(System.String,Entity.Dto.VideoDto.WatchProgressUpdateDto)">
            <summary>
            更新观看进度
            </summary>
            <param name="userId">用户ID</param>
            <param name="updateDto">观看进度更新DTO</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.HasWatched(System.String,System.Int32)">
            <summary>
            检查用户是否已观看
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>是否已观看</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.HasCompleted(System.String,System.Int32)">
            <summary>
            检查用户是否完播
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>是否完播</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetWatchStatus(System.String,System.Int32)">
            <summary>
            获取用户观看状态
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>观看状态信息</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.StartWatching(System.String,System.Int32)">
            <summary>
            开始观看（记录开始时间）
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.BatchUpdateProgress(System.Collections.Generic.List{Entity.Dto.VideoDto.BatchWatchProgressUpdateDto})">
            <summary>
            批量更新观看进度
            用于前端定时同步多个用户的观看进度
            </summary>
            <param name="progressUpdates">进度更新列表</param>
            <returns>成功更新的数量</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.SubmitAnswer(System.String,Entity.Dto.VideoDto.AnswerSubmitDto)">
            <summary>
            提交答题结果
            </summary>
            <param name="userId">用户ID</param>
            <param name="answerDto">答题结果DTO</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.HasAnswered(System.String,System.Int32)">
            <summary>
            检查用户是否已答题
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>是否已答题</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetAnswerStatus(System.String,System.Int32)">
            <summary>
            获取用户答题状态
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>答题状态信息</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetAnswerDetail(System.String,System.Int32)">
            <summary>
            获取用户答题详情
            需要权限验证：管理员可查看所有，员工只能查看自己绑定的用户
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>答题详情</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.CheckAnswerEligibility(System.String,System.Int32)">
            <summary>
            验证答题资格
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GrantReward(System.String,Entity.Dto.VideoDto.RewardGrantDto)">
            <summary>
            发放红包
            </summary>
            <param name="userId">用户ID</param>
            <param name="rewardDto">红包发放DTO</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.UpdateRewardStatus(System.String,Entity.Dto.VideoDto.RewardStatusUpdateDto)">
            <summary>
            更新红包状态
            </summary>
            <param name="userId">用户ID</param>
            <param name="statusDto">红包状态更新DTO</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.HasReward(System.String,System.Int32)">
            <summary>
            检查用户是否已获得红包
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>是否已获得红包</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetRewardStatus(System.String,System.Int32)">
            <summary>
            获取用户红包状态
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>红包状态信息</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.CheckRewardEligibility(System.String,System.Int32)">
            <summary>
            验证红包发放资格
            </summary>
            <param name="userId">用户ID</param>
            <param name="batchId">批次ID</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.BatchGrantRewards(System.Int32,System.Decimal)">
            <summary>
            批量发放红包
            为指定批次中符合条件的所有用户发放红包
            </summary>
            <param name="batchId">批次ID</param>
            <param name="rewardAmount">红包金额</param>
            <returns>发放结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetBatchStatistics(System.Int32)">
            <summary>
            获取批次统计数据
            支持基于用户权限的数据过滤：
            - 超级管理员：可查看所有用户数据
            - 管理员：可查看所有员工及其用户的数据
            - 员工：只能查看自己绑定用户的数据
            </summary>
            <param name="batchId">批次ID</param>
            <returns>批次统计数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetBatchRecords(System.Int32)">
            <summary>
            获取批次记录列表
            支持基于用户权限的数据过滤
            </summary>
            <param name="batchId">批次ID</param>
            <returns>批次记录列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.CreateViewRecordCompat(Entity.Dto.VideoDto.UserBatchRecordCreateDto)">
            <summary>
            兼容旧的ViewRecord创建接口
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.UpdateViewProgressCompat(Entity.Dto.VideoDto.WatchProgressUpdateDto)">
            <summary>
            兼容旧的ViewRecord进度更新接口
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetBatchViewRecordsCompat(System.Int32,System.Int32,System.Int32)">
            <summary>
            兼容旧的ViewRecord批次记录查询接口
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetViewStatisticsCompat(System.Nullable{System.Int32})">
            <summary>
            兼容旧的ViewRecord统计接口
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.SubmitAnswerRecordCompat(Entity.Dto.VideoDto.AnswerSubmitDto)">
            <summary>
            兼容旧的AnswerRecord提交接口
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetAnswerStatisticsCompat(System.Int32)">
            <summary>
            兼容旧的AnswerRecord统计接口
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserBatchRecordController.GetUserRewardsCompat(System.String)">
            <summary>
            兼容旧的Reward用户奖励查询接口
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.UserController">
            <summary>
            用户管理控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserController.#ctor(BLL.VideoService.UserService)">
            <summary>
            用户管理控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserController.GetProfile">
            <summary>
            获取当前用户信息
            </summary>
            <returns>用户信息</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserController.GetUser(System.String)">
            <summary>
            获取用户详情
            </summary>
            <param name="userId">用户ID</param>
            <returns>用户详情</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserController.GetUserPagedList(Entity.Dto.VideoDto.UserQueryDto)">
            <summary>
            分页查询用户列表（带权限过滤）
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>用户列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserController.TransferUsers(Entity.Dto.VideoDto.UserTransferDto)">
            <summary>
            转移用户到新员工（带权限验证）
            </summary>
            <param name="transferDto">转移DTO</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserController.AccessPromotion(Entity.Dto.VideoDto.AccessPromotionDto)">
            <summary>
            访问推广链接
            </summary>
            <param name="accessDto">访问推广链接DTO</param>
            <returns>访问结果</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.UserTransferController">
            <summary>
            用户转移控制器 - 简化版，只处理员工绑定转移
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserTransferController.#ctor(BLL.VideoService.UserTransferService)">
            <summary>
            用户转移控制器 - 简化版，只处理员工绑定转移
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserTransferController.TransferUsers(Entity.Dto.VideoDto.UserTransferDto)">
            <summary>
            转移用户到新员工
            </summary>
            <param name="transferDto">用户转移DTO</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.UserTransferController.GetUserTransferPagedList(Entity.Dto.VideoDto.UserTransferQueryDto)">
            <summary>
            分页查询用户转移记录列表
            支持按用户ID、员工ID、操作员ID、时间范围等条件查询
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>转移记录列表</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.VideoController">
            <summary>
            视频管理控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.#ctor(BLL.VideoService.VideoService,BLL.VideoService.VideoProcessingService)">
            <summary>
            视频管理控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.Add(Entity.Dto.VideoDto.VideoCreateDto)">
            <summary>
            添加视频
            </summary>
            <param name="createDto">创建视频DTO</param>
            <returns>视频ID</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.UploadVideo(Entity.Dto.VideoDto.VideoUploadDto)">
            <summary>
            上传视频文件
            </summary>
            <param name="uploadDto">上传视频DTO</param>
            <returns>上传结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.UploadVideoSimple(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            简单视频上传（兼容性接口）
            </summary>
            <param name="videoFile">视频文件</param>
            <returns>上传结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.UploadVideoComplete(Entity.Dto.VideoDto.VideoCompleteUploadDto)">
            <summary>
            完整视频上传（一次性上传视频文件并创建视频记录）
            </summary>
            <param name="uploadDto">完整上传DTO</param>
            <returns>上传结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.Update(Entity.Dto.VideoDto.VideoUpdateDto)">
            <summary>
            更新视频信息
            </summary>
            <param name="updateDto">更新视频DTO</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.Delete(System.Int32)">
            <summary>
            删除视频
            </summary>
            <param name="videoId">视频ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.Get(System.Int32)">
            <summary>
            获取视频详情
            </summary>
            <param name="videoId">视频ID</param>
            <returns>视频详情</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.GetList(Entity.Dto.VideoDto.VideoQueryDto)">
            <summary>
            分页查询视频列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>视频列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.GetCreatorVideos(System.String,System.Nullable{System.Byte})">
            <summary>
            获取创建者的视频列表
            </summary>
            <param name="createdBy">创建者ID</param>
            <param name="status">状态筛选</param>
            <returns>视频列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.GetMyVideos(System.Nullable{System.Byte})">
            <summary>
            获取我的视频列表
            </summary>
            <param name="status">状态筛选</param>
            <returns>视频列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.UpdateVideoStatus(System.Int32,Entity.Dto.VideoDto.VideoStatusUpdateDto)">
            <summary>
            更新视频状态
            </summary>
            <param name="videoId">视频ID</param>
            <param name="statusDto">状态更新DTO</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.SearchVideos(System.String,System.Int32,System.Int32)">
            <summary>
            搜索视频
            </summary>
            <param name="keyword">搜索关键词</param>
            <param name="pageIndex">页码</param>
            <param name="pageSize">页大小</param>
            <returns>搜索结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.GetVideoStatistics(System.Int32)">
            <summary>
            获取视频统计信息
            </summary>
            <param name="videoId">视频ID</param>
            <returns>视频统计</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.GetCompressionProgress(System.String)">
            <summary>
            获取视频压缩进度
            </summary>
            <param name="fileId">文件ID</param>
            <returns>压缩进度</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.FixCompressedVideos">
            <summary>
            修复已压缩但数据库未更新的视频记录（临时接口）
            </summary>
            <returns>修复结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.VideoController.FixCompressedVideosInternal">
            <summary>
            内部修复方法
            </summary>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController">
            <summary>
            微信访问令牌控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.#ctor(BLL.VideoService.WechatAccessTokenService)">
            <summary>
            微信访问令牌控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.CreateAccessToken(Entity.Dto.VideoDto.WechatAccessTokenCreateDto)">
            <summary>
            创建微信访问令牌记录
            </summary>
            <param name="createDto">创建微信访问令牌DTO</param>
            <returns>令牌ID</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.UpdateAccessToken(Entity.Dto.VideoDto.WechatAccessTokenUpdateDto)">
            <summary>
            更新微信访问令牌记录
            </summary>
            <param name="updateDto">更新微信访问令牌DTO</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.ToggleTokenStatus(System.Int32,System.Boolean)">
            <summary>
            停用/激活微信访问令牌
            </summary>
            <param name="tokenId">令牌ID</param>
            <param name="isActive">是否激活</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.GetAccessToken(System.Int32)">
            <summary>
            获取微信访问令牌详情
            </summary>
            <param name="tokenId">令牌ID</param>
            <returns>令牌详情</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.GetAccessTokenPagedList(Entity.Dto.VideoDto.WechatAccessTokenQueryDto)">
            <summary>
            分页查询微信访问令牌列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>令牌列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.GetValidAccessToken(System.String,System.String)">
            <summary>
            获取有效的访问令牌
            </summary>
            <param name="appId">微信应用ID</param>
            <param name="tokenType">令牌类型</param>
            <returns>有效的访问令牌字符串</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.GetAppTokens(System.String,System.Nullable{System.Boolean})">
            <summary>
            获取应用的所有令牌
            </summary>
            <param name="appId">应用ID</param>
            <param name="isActive">是否激活（可选）</param>
            <returns>令牌列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.RefreshAccessToken(Entity.Dto.VideoDto.TokenRefreshRequestDto)">
            <summary>
            刷新访问令牌
            </summary>
            <param name="refreshDto">刷新令牌请求</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.BatchRefreshAccessTokens(System.Collections.Generic.List{Entity.Dto.VideoDto.TokenRefreshRequestDto})">
            <summary>
            批量刷新访问令牌
            </summary>
            <param name="refreshRequests">刷新请求列表</param>
            <returns>刷新结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.GetTokenStatistics">
            <summary>
            获取令牌统计信息
            </summary>
            <returns>令牌统计</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.GetExpiringTokens(System.Int32)">
            <summary>
            获取即将过期的令牌列表
            </summary>
            <param name="bufferMinutes">缓冲时间（分钟）</param>
            <returns>即将过期的令牌列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.IsTokenExpiring(System.String,System.String,System.Int32)">
            <summary>
            检查令牌是否即将过期
            </summary>
            <param name="appId">应用ID</param>
            <param name="tokenType">令牌类型</param>
            <param name="bufferMinutes">缓冲时间（分钟）</param>
            <returns>是否即将过期</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatAccessTokenController.CleanupExpiredTokens">
            <summary>
            清理过期令牌
            </summary>
            <returns>清理的令牌数</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.WechatController">
            <summary>
            微信相关接口控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatController.#ctor(BLL.VideoService.WechatOAuthService,BLL.VideoService.WechatAccessTokenService,Microsoft.Extensions.Logging.ILogger{ServiceVideoSharing.Controllers.VideoControllers.WechatController})">
            <summary>
            微信相关接口控制器
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatController.GetOAuthUrl(Entity.Dto.VideoDto.WechatOAuthRequestDto)">
            <summary>
            获取微信OAuth2.0授权URL
            </summary>
            <param name="requestDto">授权请求参数</param>
            <returns>授权URL</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatController.OAuthCallback(Entity.Dto.VideoDto.WechatOAuthCallbackDto)">
            <summary>
            微信OAuth2.0授权回调接口
            </summary>
            <param name="callbackDto">回调参数</param>
            <returns>登录结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatController.RefreshBasicAccessToken">
            <summary>
            手动刷新微信基础access_token
            </summary>
            <returns>刷新结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatController.GetTokenStatus">
            <summary>
            获取当前微信基础access_token状态
            </summary>
            <returns>Token状态</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatController.VerifyWechatServer(System.String,System.String,System.String,System.String)">
            <summary>
            微信服务器验证接口（用于配置微信公众号）
            </summary>
            <param name="signature">微信加密签名</param>
            <param name="timestamp">时间戳</param>
            <param name="nonce">随机数</param>
            <param name="echostr">随机字符串</param>
            <returns>验证结果</returns>
        </member>
        <member name="T:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController">
            <summary>
            微信红包发放控制器
            用于视频营销系统中的红包发放管理
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.#ctor(BLL.VideoService.WechatPaymentService)">
            <summary>
            微信红包发放控制器
            用于视频营销系统中的红包发放管理
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.SendReward(Entity.Dto.VideoDto.WechatPaymentCreateDto)">
            <summary>
            发放红包
            用户完成视频观看和答题后，系统自动调用此接口发放红包
            </summary>
            <param name="createDto">红包发放DTO</param>
            <returns>支付记录ID</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.GetRewardRecord(System.Int32)">
            <summary>
            获取红包发放记录详情
            </summary>
            <param name="paymentId">支付记录ID</param>
            <returns>红包发放详情</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.GetRewardByTradeNo(System.String)">
            <summary>
            根据商户订单号获取红包发放记录
            </summary>
            <param name="outTradeNo">商户订单号</param>
            <returns>红包发放详情</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.GetRewardPagedList(Entity.Dto.VideoDto.WechatPaymentQueryDto)">
            <summary>
            分页查询红包发放记录列表
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>红包发放记录列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.GetUserRewards(System.String,System.Nullable{System.Byte})">
            <summary>
            获取用户红包记录
            </summary>
            <param name="userId">用户ID</param>
            <param name="status">发放状态（可选）</param>
            <returns>用户红包记录列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.GetMyRewards(System.Nullable{System.Byte})">
            <summary>
            获取我的红包记录
            </summary>
            <param name="status">发放状态（可选）</param>
            <returns>我的红包记录列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.WechatRewardCallback(Entity.Dto.VideoDto.WechatPaymentCallbackDto)">
            <summary>
            微信红包发放回调
            微信服务器调用此接口通知红包发放结果
            </summary>
            <param name="callbackDto">回调数据</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.QueryRewardStatus(System.Int32)">
            <summary>
            查询红包发放状态
            </summary>
            <param name="paymentId">支付记录ID</param>
            <returns>红包发放状态</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.UpdateRewardStatus(System.Int32,System.Byte,System.String)">
            <summary>
            更新红包发放状态
            管理员手动更新红包发放状态（如发放失败需要重新发放）
            </summary>
            <param name="paymentId">支付记录ID</param>
            <param name="status">新状态</param>
            <param name="transactionId">微信交易号（可选）</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.GetRewardStatistics(Entity.Dto.VideoDto.WechatPaymentQueryDto)">
            <summary>
            获取红包发放统计
            </summary>
            <param name="statisticsDto">统计查询DTO</param>
            <returns>红包发放统计</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.ExportRewards(Entity.Dto.VideoDto.WechatPaymentQueryDto)">
            <summary>
            导出红包发放记录
            </summary>
            <param name="exportDto">导出查询DTO</param>
            <returns>导出数据</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.RetryReward(System.Int32)">
            <summary>
            重新发放失败的红包
            对于发放失败的红包，管理员可以手动重新发放
            </summary>
            <param name="paymentId">支付记录ID</param>
            <returns>是否成功</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.GetFailedRewards(Entity.Dto.VideoDto.WechatPaymentQueryDto)">
            <summary>
            获取红包发放失败记录
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>失败记录列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.GetPendingRewards(Entity.Dto.VideoDto.WechatPaymentQueryDto)">
            <summary>
            获取待发放红包记录
            </summary>
            <param name="queryDto">查询条件</param>
            <returns>待发放记录列表</returns>
        </member>
        <member name="M:ServiceVideoSharing.Controllers.VideoControllers.WechatPaymentController.BatchRetryRewards(System.Collections.Generic.List{System.Int32})">
            <summary>
            批量重新发放失败的红包
            </summary>
            <param name="paymentIds">支付记录ID列表</param>
            <returns>处理结果</returns>
        </member>
        <member name="T:ServiceVideoSharing.Infrastructure.AutofacModule">
            <summary>
            Autofac模块
            </summary>
        </member>
        <member name="M:ServiceVideoSharing.Infrastructure.AutofacModule.Load(Autofac.ContainerBuilder)">
            <summary>
            加载Autofac模块
            </summary>
            <param name="builder">容器构建器</param>
        </member>
    </members>
</doc>
