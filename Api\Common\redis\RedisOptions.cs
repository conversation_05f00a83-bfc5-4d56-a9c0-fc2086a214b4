namespace Common.Redis;

/// <summary>
/// Redis配置选项类
/// </summary>
public class RedisOptions
{
    /// <summary>
    /// Redis连接字符串
    /// </summary>
    public string Connection { get; set; } = string.Empty;

    /// <summary>
    /// 默认数据库索引
    /// </summary>
    public int DefaultDb { get; set; } = 0;

    /// <summary>
    /// Redis实例名称
    /// </summary>
    public string InstanceName { get; set; } = "default";

    /// <summary>
    /// 默认Redis实例名称，用于从RedisSetting获取实例
    /// </summary>
    public string DefaultInstanceName { get; set; } = string.Empty;

    /// <summary>
    /// 连接超时时间（毫秒）
    /// </summary>
    public int ConnectionTimeout { get; set; } = 5000;

    /// <summary>
    /// 操作超时时间（毫秒）
    /// </summary>
    public int OperationTimeout { get; set; } = 1000;
}