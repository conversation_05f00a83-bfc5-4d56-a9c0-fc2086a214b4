<?xml version="1.0" encoding="utf-8"?>
<configuration>

	<configSections>
		<section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
	</configSections>

	<system.web>
		<compilation debug="true" targetFramework=".NET Core 3.1" />
		<httpRuntime targetFramework=".NET Core 3.1" />
	</system.web>

	<log4net>
		<!--错误日志：：：记录错误日志-->
		<!--按日期分割日志文件 一天一个-->
		<!-- appender 定义日志输出方式   将日志以回滚文件的形式写到文件中。-->
		<appender name="ErrorAppender" type="log4net.Appender.RollingFileAppender">
			<!--保存路径：log、logError文件-->
			<file value="D:/logs/DataMgrSystem/error/error_" />
			<!-- 如果想在本项目中添加路径，那就直接去掉C:\\  只设置log\\LogError   项目启动中默认创建文件 -->
			<appendToFile value="true"/>
			<!--按照何种方式产生多个日志文件(日期[Date],文件大小[Size],混合[Composite])-->
			<rollingStyle value="Date"/>
			<!--这是按日期产生文件夹-->
			<datePattern value="yyyy-MM-dd'.log'"/>
			<!--是否只写到一个文件中-->
			<staticLogFileName value="false"/>
			<!--保留的log文件数量 超过此数量后 自动删除之前的   好像只有在 按Size分割时有效 设定值value="－1"为不限文件数-->
			<param name="MaxSizeRollBackups" value="100"/>
			<!--每个文件的大小。只在混合方式与文件大小方式下使用。超出大小后在所有文件名后自动增加正整数重新命名，数字最大的最早写入。可用的单位:KB|MB|GB。不要使用小数,否则会一直写入当前日志-->
			<maximumFileSize value="50MB" />
			<!-- layout 控制Appender的输出格式，也可以是xml  一个Appender只能是一个layout-->
			<layout type="log4net.Layout.PatternLayout">
				<!--每条日志末尾的文字说明-->
				<!--输出格式 模板-->
				<!-- <param name="ConversionPattern"  value="记录时间：%date 线程ID:[%thread] 日志级别：%-5level 记录类：%logger   
        操作者ID：%property{Operator} 操作类型：%property{Action}%n  当前机器名:%property%n当前机器名及登录用户：%username %n  
        记录位置：%location%n 消息描述：%property{Message}%n   异常：%exception%n 消息：%message%newline%n%n" />-->

				<!--样例：2008-03-26 13:42:32,111 [10] INFO  Log4NetDemo.MainClass [(null)] - info-->
				<!--<conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %n日志级别： %-5level %n错误描述：%message%newline %n"/>-->
				<conversionPattern value="%n＝＝＝＝＝＝＝＝＝＝
                                  %n【日志级别】%-5level
                                  %n【记录时间】%date
                                  %n【执行时间】[%r]毫秒
                                  %n【错误位置】%logger 属性[%property{NDC}]
                                  %n【错误描述】%message
                                  %n【错误详情】%newline"/>
			</layout>
			<filter type="log4net.Filter.LevelRangeFilter,log4net">
				<levelMin value="ERROR" />
				<levelMax value="FATAL" />
			</filter>
		</appender>

		<!--DEBUG：：：记录DEBUG日志-->
		<!--按日期分割日志文件 一天一个-->
		<!-- appender 定义日志输出方式   将日志以回滚文件的形式写到文件中。-->
		<appender name="DebugAppender" type="log4net.Appender.RollingFileAppender">
			<!--保存路径：下面路径项目启动的时候自动创建log、logError文件-->
			<file value="D:/logs/DataMgrSystem/debug/debug_" />
			<!-- 如果想在本项目中添加路径，那就直接去掉C:\\  只设置log\\LogError   项目启动中默认创建文件 -->
			<appendToFile value="true"/>
			<!--按照何种方式产生多个日志文件(日期[Date],文件大小[Size],混合[Composite])-->
			<rollingStyle value="Date"/>
			<!--这是按日期产生文件夹-->
			<datePattern value="yyyy-MM-dd'.log'"/>
			<!--是否只写到一个文件中-->
			<staticLogFileName value="false"/>
			<!--保留的log文件数量 超过此数量后 自动删除之前的   好像只有在 按Size分割时有效 设定值value="－1"为不限文件数-->
			<param name="MaxSizeRollBackups" value="100"/>
			<!--每个文件的大小。只在混合方式与文件大小方式下使用。超出大小后在所有文件名后自动增加正整数重新命名，数字最大的最早写入。可用的单位:KB|MB|GB。不要使用小数,否则会一直写入当前日志-->
			<maximumFileSize value="50MB" />
			<!-- layout 控制Appender的输出格式，也可以是xml  一个Appender只能是一个layout-->
			<layout type="log4net.Layout.PatternLayout">
				<!--每条日志末尾的文字说明-->
				<!--输出格式 模板-->
				<!-- <param name="ConversionPattern"  value="记录时间：%date 线程ID:[%thread] 日志级别：%-5level 记录类：%logger   
        操作者ID：%property{Operator} 操作类型：%property{Action}%n  当前机器名:%property%n当前机器名及登录用户：%username %n  
        记录位置：%location%n 消息描述：%property{Message}%n   异常：%exception%n 消息：%message%newline%n%n" />-->

				<!--样例：2008-03-26 13:42:32,111 [10] INFO  Log4NetDemo.MainClass [(null)] - info-->
				<!--<conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %n日志级别： %-5level %n错误描述：%message%newline %n"/>-->
				<conversionPattern value="%n＝＝＝＝＝＝＝＝＝＝
                                  %n【日志级别】%-5level
                                  %n【记录时间】%date
                                  %n【执行时间】[%r]毫秒
                                  %n【错误位置】%logger 属性[%property{NDC}]
                                  %n【错误描述】%message
                                  %n【错误详情】%newline"/>
			</layout>
			<filter type="log4net.Filter.LevelRangeFilter,log4net">
				<levelMin value="DEBUG" />
				<levelMax value="WARN" />
			</filter>
		</appender>
		<!--Set root logger level to DEBUG and its only appender to A1-->
		<root>
			<!--控制级别，由低到高: ALL|DEBUG|INFO|WARN|ERROR|FATAL|OFF-->
			<level value="ALL" />
			<appender-ref ref="DebugAppender" />
			<appender-ref ref="ErrorAppender" />
		</root>
	</log4net>
</configuration>