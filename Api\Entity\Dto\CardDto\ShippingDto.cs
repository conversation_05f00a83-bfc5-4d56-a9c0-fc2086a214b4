namespace Entity.Dto.CardDto
{
    /// <summary>
    /// 创建发货记录请求DTO
    /// </summary>
    public class CreateShippingDto
    {
        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; } = string.Empty;

        /// <summary>
        /// 出货状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 操作人
        /// </summary>
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// 发货目标ID
        /// </summary>
        public string TargetId { get; set; } = string.Empty;

        /// <summary>
        /// 发货数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? Remark { get; set; } = null;
    }

    /// <summary>
    /// 更新出货记录请求DTO
    /// </summary>
    public class UpdateShippingDto
    {
        /// <summary>
        /// 出货记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 出货状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 操作人
        /// </summary>
        public string Operator { get; set; } = string.Empty;

        /// <summary>
        /// 出货备注
        /// </summary>
        public string? Remark { get; set; } = null;
    }

    /// <summary>
    /// 创建发货目标请求DTO
    /// </summary>
    public class CreateShippingTargetDto
    {
        /// <summary>
        /// 出货目标ID
        /// </summary>
        public string TargetId { get; set; } = string.Empty;

        /// <summary>
        /// 出货渠道
        /// </summary>
        public string Channel { get; set; } = string.Empty;

        /// <summary>
        /// 目标名称
        /// </summary>
        public string TargetName { get; set; } = string.Empty;

        /// <summary>
        /// 目标描述
        /// </summary>
        public string TargetDesc { get; set; } = string.Empty;

    }

    /// <summary>
    /// 更新出货目标请求DTO
    /// </summary>
    public class UpdateShippingTargetDto
    {
        /// <summary>
        /// 出货目标ID
        /// </summary>
        public string TargetId { get; set; } = string.Empty;

        /// <summary>
        /// 出货目标名称
        /// </summary>
        public string TargetName { get; set; } = string.Empty;

        /// <summary>
        /// 出货目标描述    
        /// </summary>
        public string? TargetDesc { get; set; } = null;

        /// <summary>
        /// 出货渠道
        /// </summary>
        public string? Channel { get; set; } = null;

        /// <summary>
        /// 出货目标状态
        /// </summary>
        public int Status { get; set; }
    }


    /// 发货记录查询DTO
    /// </summary>
    public class ShippingQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 批次号
        /// </summary>
        public string? BatchNo { get; set; } = null;

        /// <summary>
        /// 目标ID
        /// </summary>
        public int? TargetId { get; set; } = null;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartDate { get; set; } = null;

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndDate { get; set; } = null;
    }

    /// <summary>
    /// 发货记录响应DTO
    /// </summary>
    public class ShippingRecordDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; } = string.Empty;

        /// <summary>
        /// 目标ID
        /// </summary>
        public int TargetId { get; set; }

        /// <summary>
        /// 目标名称
        /// </summary>
        public string TargetName { get; set; } = string.Empty;

        /// <summary>
        /// 发货数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 发货状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? Remark { get; set; } = null;
    }
}