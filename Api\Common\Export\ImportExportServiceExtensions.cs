using Microsoft.Extensions.DependencyInjection;

namespace Common.Export
{
    /// <summary>
    /// 导入导出服务扩展方法
    /// </summary>
    public static class ImportExportServiceExtensions
    {
        /// <summary>
        /// 添加导入导出服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddImportExportServices(this IServiceCollection services)
        {
            // 注册导出服务
            services.AddScoped<IExportService, ExportService>();

            // 注册导入服务
            services.AddScoped<IImportService, ImportService>();

            return services;
        }
    }
}