﻿using Newtonsoft.Json;
using StackExchange.Redis;
using System.Collections.Concurrent;

namespace Common.Redis
{
    /// <summary>
    /// Redis操作帮助类,提供Redis常用操作的封装
    /// </summary>
    public class RedisHelper : IDisposable
    {
        /// <summary>
        /// 当前机器名称,用于分布式锁识别
        /// </summary>
        private static readonly RedisValue MachineName = Environment.MachineName;

        /// <summary>
        /// 连接池字典,key为实例名称,value为连接对象
        /// </summary>
        private static readonly ConcurrentDictionary<string, ConnectionMultiplexer> _connections = new();

        #region 连接管理

        /// <summary>
        /// 获取Redis连接对象
        /// </summary>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        /// <returns>ConnectionMultiplexer实例</returns>
        private static ConnectionMultiplexer GetConnect(string? instanceName = null)
        {
            var instance = string.IsNullOrEmpty(instanceName)
                ? RedisSetting.DefaultInstance
                : RedisSetting.GetInstance(instanceName);

            return _connections.GetOrAdd(instance.InstanceName, p => ConnectionMultiplexer.Connect(instance.Connection));
        }

        /// <summary>
        /// 获取Redis数据库
        /// </summary>
        /// <param name="Db">数据库索引,默认为0</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        /// <returns>Redis数据库对象</returns>
        public static IDatabase GetDatabase(int Db = 0, string? instanceName = null)
        {
            var instance = string.IsNullOrEmpty(instanceName)
                ? RedisSetting.DefaultInstance
                : RedisSetting.GetInstance(instanceName);

            return Db != instance.DefaultDb
                ? GetConnect(instanceName).GetDatabase(Db)
                : GetConnect(instanceName).GetDatabase(instance.DefaultDb);
        }

        #endregion

        #region 时间计算

        /// <summary>
        /// 时间范围枚举
        /// </summary>
        public enum TimeScope
        {
            分钟 = 0,
            小时 = 1,
            天 = 2,
            月 = 3,
            年 = 4
        }

        /// <summary>
        /// 计算当前时间到目标时间的秒数差
        /// </summary>
        /// <param name="scope">时间范围</param>
        /// <param name="count">数量</param>
        /// <returns>相差的秒数</returns>
        public static long GetTargetTimeDifference(TimeScope scope, int count = 1)
        {
            if (count < 1) return 0;

            var nowStamp = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds();
            DateTime targetTime = DateTime.Now;

            switch (scope)
            {
                case TimeScope.分钟:
                    targetTime = targetTime.AddMinutes(count).AddSeconds(-targetTime.Second);
                    break;
                case TimeScope.小时:
                    targetTime = targetTime.AddHours(count).AddMinutes(-targetTime.Minute).AddSeconds(-targetTime.Second);
                    break;
                case TimeScope.天:
                    targetTime = new DateTime(targetTime.Year, targetTime.Month, targetTime.Day + count);
                    break;
                case TimeScope.月:
                    targetTime = new DateTime(targetTime.Year, targetTime.Month + count, 1);
                    break;
                case TimeScope.年:
                    targetTime = new DateTime(targetTime.Year + count, 1, 1);
                    break;
            }

            var targetTimeStamp = new DateTimeOffset(targetTime).ToUnixTimeSeconds();
            return targetTimeStamp - nowStamp;
        }

        /// <summary>
        /// 获取目标时间差的TimeSpan对象
        /// </summary>
        public static TimeSpan GetTargetTimeDifferenceTimeSpan(TimeScope scope, int count = 1)
        {
            return TimeSpan.FromSeconds(GetTargetTimeDifference(scope, count));
        }

        #endregion

        #region 基础操作

        /// <summary>
        /// 获取字符串类型的缓存值
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static RedisValue Get(string key, string? instanceName = null)
        {
            return GetDatabase(0, instanceName).StringGet(key);
        }

        /// <summary>
        /// 获取指定类型的缓存值
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static T? Get<T>(string key, string? instanceName = null)
        {
            TryGatValue(key, out T? value, instanceName);
            return value;
        }

        /// <summary>
        /// 尝试获取指定类型的缓存值
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="value">输出缓存值</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static bool TryGatValue<T>(string key, out T? value, string? instanceName = null)
        {
            value = default;
            var data = Get(key, instanceName);
            if (!data.HasValue) return false;

            try
            {
                var stringValue = data.ToString();
                if (string.IsNullOrEmpty(stringValue)) return false;
                value = JsonConvert.DeserializeObject<T>(stringValue);
                return value != null;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 设置缓存值
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="value">缓存值</param>
        /// <param name="expiry">过期时间</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static bool Set<T>(string key, T value, TimeSpan? expiry = null, string? instanceName = null)
        {
            return GetDatabase(0, instanceName).StringSet(key, JsonConvert.SerializeObject(value), expiry);
        }

        /// <summary>
        /// 通过委托方法设置缓存值
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="func">获取缓存值的委托</param>
        /// <param name="expiry">过期时间</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static bool Set<T>(string key, Func<T> func, TimeSpan? expiry = null, string? instanceName = null)
        {
            return Set(key, func.Invoke(), expiry, instanceName);
        }

        /// <summary>
        /// 删除指定的缓存键
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="flags">命令标志</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static bool Remove(RedisKey key, CommandFlags flags = CommandFlags.None, string? instanceName = null)
        {
            return GetDatabase(0, instanceName).KeyDelete(key, flags);
        }

        /// <summary>
        /// 批量删除缓存键
        /// </summary>
        /// <param name="keys">缓存键数组</param>
        /// <param name="flags">命令标志</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static long Remove(RedisKey[] keys, CommandFlags flags = CommandFlags.None, string? instanceName = null)
        {
            return GetDatabase(0, instanceName).KeyDelete(keys, flags);
        }

        #endregion

        #region 高级操作

        /// <summary>
        /// 获取所有匹配的键
        /// </summary>
        /// <param name="database">数据库索引</param>
        /// <param name="pattern">匹配模式</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static List<RedisKey> GetKeys(int database = -1, RedisValue pattern = default, string? instanceName = null)
        {
            var instance = string.IsNullOrEmpty(instanceName)
                ? RedisSetting.DefaultInstance
                : RedisSetting.GetInstance(instanceName);

            return [.. GetConnect(instanceName).GetServer(instance.Connection.Split(",")[0]).Keys(database, pattern)];
        }

        /// <summary>
        /// 获取或设置缓存值(如果不存在则添加)
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="func">获取缓存值的委托</param>
        /// <param name="expiry">过期时间</param>
        /// <param name="cache">是否启用缓存</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static T GetOrSet<T>(RedisKey key, Func<T> func, TimeSpan? expiry = null, bool cache = true, string? instanceName = null)
        {
            if (string.IsNullOrEmpty(key)) throw new Exception("Key不能为空");

            var redis = GetDatabase(0, instanceName);

            if (cache)
            {
                var data = redis.StringGet(key);
                if (data.HasValue)
                {
                    try
                    {
                        var stringValue = data.ToString();
                        if (string.IsNullOrEmpty(stringValue))
                        {
                            throw new Exception("缓存值为空");
                        }
                        var result = JsonConvert.DeserializeObject<T>(stringValue);
                        return result == null ? throw new Exception("反序列化结果为空") : result;
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"该Key中的Value类型和传入类型不符: {ex.Message}");
                    }
                }
            }

            var res = func.Invoke();
            redis.StringSet(key, JsonConvert.SerializeObject(res), expiry);
            return res;
        }

        /// <summary>
        /// 获取或设置缓存值(基于时间范围)
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="func">获取缓存值的委托</param>
        /// <param name="scope">时间范围</param>
        /// <param name="count">数量</param>
        /// <param name="cache">是否启用缓存</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static T GetOrSet<T>(RedisKey key, Func<T> func, TimeScope scope, int count = 1, bool cache = true, string? instanceName = null)
        {
            return GetOrSet(key, func, GetTargetTimeDifferenceTimeSpan(scope, count), cache, instanceName);
        }

        /// <summary>
        /// Redis分布式锁
        /// </summary>
        /// <param name="key">锁键名</param>
        /// <param name="action">要执行的操作</param>
        /// <param name="time">锁过期时间(秒)</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static void RedisLock(string key, Action action, long time = 5, string? instanceName = null)
        {
            if (time < 1) throw new Exception("Redis锁过期时间不能少于1秒");
            if (string.IsNullOrEmpty(key)) throw new Exception("Redis锁Key不能为空");
            if (action == null) throw new Exception("Redis锁 Action 不能为空");

            var redis = GetDatabase(0, instanceName);
            var nowTime = DateTime.Now.AddMinutes(10);

            while (nowTime > DateTime.Now)
            {
                if (!redis.LockTake(key, MachineName, TimeSpan.FromSeconds(time)))
                {
                    Thread.Sleep(5);
                    continue;
                }

                var dateTime = DateTime.Now.AddSeconds(time + 1);
                var tokenSource = new CancellationTokenSource();

                Task.Run(() =>
                {
                    var renewTime = DateTime.Now.AddMinutes(10);
                    while (renewTime > DateTime.Now)
                    {
                        if (tokenSource.IsCancellationRequested) return;
                        redis.KeyExpire(key, TimeSpan.FromSeconds(5));
                        Thread.Sleep(5);
                    }
                }, tokenSource.Token);

                try
                {
                    action.Invoke();
                    return;
                }
                catch (Exception ex)
                {
                    throw new Exception(ex.Message);
                }
                finally
                {
                    tokenSource.Cancel();
                    redis.LockRelease(key, MachineName);
                }
            }
            throw new Exception("获取锁失败");
        }

        /// <summary>
        /// 异步获取或设置缓存值(如果不存在则添加)
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="func">获取缓存值的异步委托</param>
        /// <param name="expiry">过期时间</param>
        /// <param name="cache">是否启用缓存</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static async Task<T> GetOrSetAsync<T>(RedisKey key, Func<Task<T>> func, TimeSpan? expiry = null, bool cache = true, string? instanceName = null)
        {
            if (string.IsNullOrEmpty(key)) throw new Exception("Key不能为空");

            var redis = GetDatabase(0, instanceName);

            if (cache)
            {
                var data = await redis.StringGetAsync(key);
                if (data.HasValue)
                {
                    try
                    {
                        var stringValue = data.ToString();
                        if (string.IsNullOrEmpty(stringValue))
                        {
                            throw new Exception("缓存值为空");
                        }
                        var result = JsonConvert.DeserializeObject<T>(stringValue);
                        return result == null ? throw new Exception("反序列化结果为空") : result;
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"该Key中的Value类型和传入类型不符: {ex.Message}");
                    }
                }
            }

            // 调用异步方法并等待其完成
            var res = await func.Invoke();

            // 序列化并存储结果
            await redis.StringSetAsync(key, JsonConvert.SerializeObject(res), expiry);

            return res;
        }

        /// <summary>
        /// 异步获取或设置缓存值(基于时间范围)
        /// </summary>
        /// <param name="key">缓存键</param>
        /// <param name="func">获取缓存值的异步委托</param>
        /// <param name="scope">时间范围</param>
        /// <param name="count">数量</param>
        /// <param name="cache">是否启用缓存</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static Task<T> GetOrSetAsync<T>(RedisKey key, Func<Task<T>> func, TimeScope scope, int count = 1, bool cache = true, string? instanceName = null)
        {
            return GetOrSetAsync(key, func, GetTargetTimeDifferenceTimeSpan(scope, count), cache, instanceName);
        }

        #endregion

        #region 发布订阅

        /// <summary>
        /// 订阅指定频道
        /// </summary>
        /// <param name="subChannel">订阅频道名</param>
        /// <param name="action">消息处理委托</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static void RedisSub(string subChannel, Action<RedisChannel, RedisValue> action, string? instanceName = null)
        {
            var channel = RedisChannel.Literal(subChannel);
            GetConnect(instanceName).GetSubscriber().Subscribe(channel, action);
        }

        /// <summary>
        /// 发布消息到指定频道
        /// </summary>
        /// <param name="channel">频道名</param>
        /// <param name="msg">消息内容</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static long RedisPub<T>(string channel, T msg, string? instanceName = null)
        {
            var redisChannel = RedisChannel.Literal(channel);
            return GetConnect(instanceName).GetSubscriber().Publish(redisChannel, JsonConvert.SerializeObject(msg));
        }

        /// <summary>
        /// 取消指定频道的订阅
        /// </summary>
        /// <param name="channel">频道名</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static void Unsubscribe(string channel, string? instanceName = null)
        {
            var redisChannel = RedisChannel.Literal(channel);
            GetConnect(instanceName).GetSubscriber().Unsubscribe(redisChannel);
        }

        /// <summary>
        /// 取消所有频道的订阅
        /// </summary>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static void UnsubscribeAll(string? instanceName = null)
        {
            GetConnect(instanceName).GetSubscriber().UnsubscribeAll();
        }

        #endregion

        #region 服务器操作

        /// <summary>
        /// 获取Redis服务器
        /// </summary>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        /// <param name="endPointsIndex">端点索引</param>
        public static IServer GetServer(string? instanceName = null, int endPointsIndex = 0)
        {
            var instance = string.IsNullOrEmpty(instanceName)
                ? RedisSetting.DefaultInstance
                : RedisSetting.GetInstance(instanceName);

            var confOption = ConfigurationOptions.Parse(instance.Connection);
            return GetConnect(instanceName).GetServer(confOption.EndPoints[endPointsIndex]);
        }

        /// <summary>
        /// 获取Redis订阅者
        /// </summary>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static ISubscriber GetSubscriber(string? instanceName = null)
        {
            return GetConnect(instanceName).GetSubscriber();
        }

        /// <summary>
        /// 释放Redis连接资源
        /// </summary>
        /// <remarks>
        /// 该方法会遍历所有已建立的Redis连接，并逐个关闭它们。
        /// 主要用于在应用程序关闭时释放资源，避免连接泄漏。
        /// </remarks>
        public void Dispose()
        {
            if (_connections != null && !_connections.IsEmpty)
            {
                foreach (var item in _connections.Values)
                {
                    item.Close();
                }
            }
            GC.SuppressFinalize(this); // 抑制终结器
        }

        #endregion

        private static T? DeserializeValue<T>(string? value)
        {
            if (string.IsNullOrEmpty(value))
                return default;
            return JsonConvert.DeserializeObject<T>(value);
        }

        /// <summary>
        /// 获取哈希表字段值
        /// </summary>
        /// <param name="key">键名</param>
        /// <param name="field">字段名</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static T? HashGet<T>(string key, string field, string? instanceName = null)
        {
            var value = GetDatabase(0, instanceName).HashGet(key, field);
            return value.HasValue ? DeserializeValue<T>(value.ToString()) : default;
        }

        /// <summary>
        /// 获取字符串值
        /// </summary>
        /// <param name="key">键名</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static T? StringGet<T>(string key, string? instanceName = null)
        {
            var value = GetDatabase(0, instanceName).StringGet(key);
            return value.HasValue ? DeserializeValue<T>(value.ToString()) : default;
        }

        /// <summary>
        /// 发布消息
        /// </summary>
        /// <param name="channel">频道名</param>
        /// <param name="message">消息内容</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static long Publish<T>(string channel, T message, string? instanceName = null)
        {
            var redisChannel = RedisChannel.Literal(channel);
            var value = JsonConvert.SerializeObject(message);
            return GetConnect(instanceName).GetSubscriber().Publish(redisChannel, value);
        }

        /// <summary>
        /// 订阅频道
        /// </summary>
        /// <param name="channel">频道名</param>
        /// <param name="handler">消息处理委托</param>
        /// <param name="instanceName">Redis实例名称，默认为null使用第一个实例</param>
        public static void Subscribe(string channel, Action<RedisChannel, RedisValue> handler, string? instanceName = null)
        {
            var redisChannel = RedisChannel.Literal(channel);
            GetConnect(instanceName).GetSubscriber().Subscribe(redisChannel, handler);
        }
    }
}
