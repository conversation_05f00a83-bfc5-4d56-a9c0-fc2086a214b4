﻿using System.Data;
using System.Linq.Expressions;
using System.Reflection;

namespace Common.EF
{
    public static class EFHelper
    {
        #region Where 扩展方法 WhereIF
        public static IQueryable<TSource> WhereIF<TSource>(this IQueryable<TSource> source, Func<bool> ifFunc, Expression<Func<TSource, bool>> predicate)
        {

            return ifFunc.Invoke() ? source.Where(predicate) : source;
        }


        public static IQueryable<TSource> WhereIF<TSource>(this IQueryable<TSource> source, bool ifBool, Expression<Func<TSource, bool>> predicate)
        {

            return ifBool ? source.Where(predicate) : source;
        }
        #endregion


        public static List<T> ToList<T>(this DataTable dt) where T : class, new()
        {
            var propertyInfos = typeof(T).GetProperties();
            var list = new List<T>();
            foreach (DataRow row in dt.Rows)
            {
                var t = new T();
                foreach (PropertyInfo p in propertyInfos)
                {
                    if (dt.Columns.IndexOf(p.Name) != -1 && row[p.Name] != DBNull.Value)
                        p.SetValue(t, row[p.Name], null);
                }
                list.Add(t);
            }
            return list;
        }
    }
}
