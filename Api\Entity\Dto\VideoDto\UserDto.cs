using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.VideoDto
{
    /// <summary>
    /// 用户查询DTO
    /// </summary>
    public class UserQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 微信昵称(模糊查询)
        /// </summary>
        public string? Nickname { get; set; }

        /// <summary>
        /// 绑定的员工ID
        /// </summary>
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }

    /// <summary>
    /// 用户创建DTO
    /// </summary>
    public class UserCreateDto
    {
        /// <summary>
        /// 微信OpenID
        /// </summary>
        [MaxLength(100, ErrorMessage = "微信OpenID长度不能超过100个字符")]
        public string? OpenId { get; set; }

        /// <summary>
        /// 微信UnionID
        /// </summary>
        [MaxLength(100, ErrorMessage = "微信UnionID长度不能超过100个字符")]
        public string? UnionId { get; set; }

        /// <summary>
        /// 微信昵称
        /// </summary>
        [MaxLength(100, ErrorMessage = "微信昵称长度不能超过100个字符")]
        public string? Nickname { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        [MaxLength(255, ErrorMessage = "头像URL长度不能超过255个字符")]
        public string? Avatar { get; set; }

        /// <summary>
        /// 绑定的员工ID
        /// </summary>
        public string? EmployeeId { get; set; }
    }

    /// <summary>
    /// 用户响应DTO
    /// </summary>
    public class UserResponseDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 微信OpenID
        /// </summary>
        public string? OpenId { get; set; }

        /// <summary>
        /// 微信UnionID
        /// </summary>
        public string? UnionId { get; set; }

        /// <summary>
        /// 微信昵称
        /// </summary>
        public string? Nickname { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        public string? Avatar { get; set; }

        /// <summary>
        /// 绑定的员工ID
        /// </summary>
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 绑定的员工姓名
        /// </summary>
        public string? EmployeeName { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLogin { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 用户转移DTO - 转移用户到不同员工
    /// </summary>
    public class UserTransferDto
    {
        /// <summary>
        /// 被转移用户ID列表
        /// </summary>
        [Required(ErrorMessage = "被转移用户ID列表不能为空")]
        public List<string> UserIds { get; set; } = [];

        /// <summary>
        /// 新员工ID
        /// </summary>
        [Required(ErrorMessage = "新员工ID不能为空")]
        public string ToEmployeeId { get; set; } = string.Empty;

        /// <summary>
        /// 转移原因
        /// </summary>
        [MaxLength(255, ErrorMessage = "转移原因长度不能超过255个字符")]
        public string? Reason { get; set; }
    }



    /// <summary>
    /// 登录响应DTO
    /// </summary>
    public class LoginResponseDto
    {
        /// <summary>
        /// JWT Token
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// 用户信息
        /// </summary>
        public UserResponseDto? UserInfo { get; set; }
    }
}
