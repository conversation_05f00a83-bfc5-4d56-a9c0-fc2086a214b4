using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.VideoEntity
{
    /// <summary>
    /// 系统配置表
    /// </summary>
    [Table("system_configs")]
    public class SystemConfig : BaseEntity_ID
    {
        /// <summary>
        /// 配置键
        /// </summary>
        [Required]
        [MaxLength(100)]
        [Comment("配置键")]
        public string ConfigKey { get; set; } = string.Empty;

        /// <summary>
        /// 配置值
        /// </summary>
        [Comment("配置值")]
        public string? ConfigValue { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        [MaxLength(255)]
        [Comment("配置描述")]
        public string? Description { get; set; }

        /// <summary>
        /// 配置分组
        /// </summary>
        [MaxLength(50)]
        [Comment("配置分组")]
        public string? GroupName { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        [MaxLength(50)]
        [Comment("配置类型")]
        public string? ConfigType { get; set; }

        /// <summary>
        /// 是否启用:0禁用,1启用
        /// </summary>
        [Comment("是否启用:0禁用,1启用")]
        public byte IsEnabled { get; set; } = 1;
    }
}
