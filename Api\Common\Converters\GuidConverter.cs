using System.Text.Json;
using System.Text.Json.Serialization;

namespace Common.Converters
{
    /// <summary>
    /// GUID 格式化工具类
    /// </summary>
    public static class GuidFormatter
    {
        /// <summary>
        /// 获取 32位 无连字符的 GUID 字符串
        /// </summary>
        /// <returns>32 字符的字符串（无连字符）</returns>
        public static string GetGuidString()
        {
            return Guid.NewGuid().ToString("N");
        }

        /// <summary>
        /// 将 GUID 转换为 32 字符的字符串（无连字符）
        /// </summary>
        /// <param name="guid">GUID</param>
        /// <returns>32 字符的字符串（无连字符）</returns>
        public static string Format(Guid guid)
        {
            return guid.ToString("N");
        }

        /// <summary>
        /// 将可空 GUID 转换为 32 字符的字符串（无连字符）
        /// </summary>
        /// <param name="guid">可空 GUID</param>
        /// <returns>32 字符的字符串（无连字符），如果输入为 null 则返回 null</returns>
        public static string? Format(Guid? guid)
        {
            return guid?.ToString("N");
        }

        /// <summary>
        /// 将 32 字符的字符串（无连字符）转换为 GUID   
        /// </summary>  
        /// <param name="value">32 字符的字符串（无连字符）</param>
        /// <returns>GUID</returns>
        public static Guid Parse(string value)
        {
            return Guid.Parse(value);
        }

        /// <summary>
        /// 尝试将 32 字符的字符串（无连字符）转换为 GUID   
        /// </summary>  
        /// <param name="value">32 字符的字符串（无连字符）</param>
        /// <param name="result">转换结果</param>
        /// <returns>是否转换成功</returns>
        public static bool TryParse(string? value, out Guid result)
        {
            return Guid.TryParse(value, out result);
        }
    }

    /// <summary>
    /// GUID JSON转换器
    /// 用于处理无连字符的GUID格式
    /// </summary>
    public class GuidConverter : JsonConverter<Guid>
    {
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public GuidConverter()
        {
        }

        /// <summary>
        /// 从JSON读取GUID
        /// </summary>
        public override Guid Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            string? value = reader.GetString();
            return value == null ? Guid.Empty : GuidFormatter.Parse(value);
        }

        /// <summary>
        /// 将GUID写入JSON
        /// </summary>
        public override void Write(Utf8JsonWriter writer, Guid value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(GuidFormatter.Format(value));
        }
    }

    /// <summary>
    /// 可空GUID JSON转换器
    /// 用于处理无连字符的可空GUID格式
    /// </summary>
    public class NullableGuidConverter : JsonConverter<Guid?>
    {
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public NullableGuidConverter()
        {
        }

        /// <summary>
        /// 从JSON读取可空GUID
        /// </summary>
        public override Guid? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            string? value = reader.GetString();
            if (string.IsNullOrEmpty(value))
                return null;
            return GuidFormatter.Parse(value);
        }

        /// <summary>
        /// 将可空GUID写入JSON
        /// </summary>
        public override void Write(Utf8JsonWriter writer, Guid? value, JsonSerializerOptions options)
        {
            if (value.HasValue)
                writer.WriteStringValue(GuidFormatter.Format(value.Value));
            else
                writer.WriteNullValue();
        }
    }
}