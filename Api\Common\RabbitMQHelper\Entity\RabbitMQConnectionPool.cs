using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;
using System.Collections.Concurrent;

namespace Common.RabbitMQHelper.Entity;

/// <summary>
/// RabbitMQ 连接池实现
/// </summary>
public class RabbitMQConnectionPool : IConnectionPool
{
    private readonly RabbitMQOptions _options;
    private readonly ILogger<RabbitMQConnectionPool> _logger;
    private readonly ConnectionFactory _connectionFactory;
    private readonly ConcurrentBag<IConnection> _connections;
    private readonly ConcurrentDictionary<IConnection, ConcurrentBag<IModel>> _channels;
    private bool _disposed;

    public RabbitMQConnectionPool(IOptions<RabbitMQOptions> options, ILogger<RabbitMQConnectionPool> logger)
    {
        _options = options.Value;
        _logger = logger;
        _connectionFactory = new ConnectionFactory
        {
            HostName = _options.HostName,
            Port = _options.Port,
            UserName = _options.UserName,
            Password = _options.Password,
            VirtualHost = _options.VirtualHost,
            AutomaticRecoveryEnabled = true
        };

        _connections = [];
        _channels = new ConcurrentDictionary<IConnection, ConcurrentBag<IModel>>();

        // 初始化连接池
        for (int i = 0; i < _options.ConnectionPoolSize; i++)
        {
            CreateConnection();
        }
    }

    private void CreateConnection()
    {
        try
        {
            var connection = _connectionFactory.CreateConnection();
            _connections.Add(connection);
            _channels.TryAdd(connection, []);

            connection.ConnectionShutdown += (sender, args) =>
            {
                _logger.LogWarning("RabbitMQ connection shutdown: {ReplyText}", args.ReplyText);
                // 尝试重新创建连接
                if (!_disposed)
                {
                    CreateConnection();
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create RabbitMQ connection");
            throw;
        }
    }

    public IConnection GetConnection()
    {
        if (_connections.TryTake(out var connection))
        {
            _connections.Add(connection);
            return connection;
        }

        CreateConnection();
        return GetConnection();
    }

    public IModel GetChannel()
    {
        var connection = GetConnection();
        var channelBag = _channels[connection];

        if (channelBag.TryTake(out var existingChannel) && !existingChannel.IsClosed)
        {
            return existingChannel;
        }

        var channel = connection.CreateModel();
        return channel;
    }

    public void ReleaseChannel(IModel channel)
    {
        if (channel?.IsOpen == true)
        {
            foreach (var kvp in _channels)
            {
                kvp.Value.Add(channel);
                break;
            }
        }
    }
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        // 释放所有通道资源
        foreach (var channelBag in _channels.Values)
        {
            while (channelBag.TryTake(out var channel))
            {
                try
                {
                    if (channel.IsOpen)
                        channel.Close();
                    channel.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error disposing channel");
                }
            }
        }

        // 释放所有连接资源
        foreach (var connection in _connections)
        {
            try
            {
                if (connection.IsOpen)
                    connection.Close();
                connection.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing connection");
            }
        }

        // 抑制终结器
        GC.SuppressFinalize(this);
    }
}