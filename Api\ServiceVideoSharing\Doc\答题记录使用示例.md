# 答题记录使用示例

## 场景描述

用户观看视频后需要回答2道题目，每道题目有多个选项，用户必须完成所有题目才能提交答题记录。

## 示例数据

### 批次信息
- 批次ID: 1
- 视频ID: 1
- 题目数量: 2

### 题目1
```json
{
  "questionText": "视频中提到的主要观点是什么？",
  "orderNum": 0,
  "options": [
    {
      "optionText": "A. 提高工作效率",
      "isCorrect": true,
      "orderNum": 0
    },
    {
      "optionText": "B. 降低成本",
      "isCorrect": false,
      "orderNum": 1
    },
    {
      "optionText": "C. 增加收入",
      "isCorrect": false,
      "orderNum": 2
    }
  ]
}
```

### 题目2
```json
{
  "questionText": "视频中建议的最佳实践是？",
  "orderNum": 1,
  "options": [
    {
      "optionText": "A. 每天工作12小时",
      "isCorrect": false,
      "orderNum": 0
    },
    {
      "optionText": "B. 合理安排时间",
      "isCorrect": true,
      "orderNum": 1
    },
    {
      "optionText": "C. 减少休息时间",
      "isCorrect": false,
      "orderNum": 2
    }
  ]
}
```

## 用户答题过程

### 1. 用户选择答案
- 题目1：用户选择了选项A（正确）
- 题目2：用户选择了选项A（错误）

### 2. 提交答题记录

**请求URL**: `POST /api/video/AnswerRecord/submit`

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**:
```json
{
  "batchId": 1,
  "answers": [
    {
      "questionOrderNum": 0,
      "questionText": "视频中提到的主要观点是什么？",
      "selectedOptionOrderNum": 0,
      "selectedOptionText": "A. 提高工作效率",
      "isCorrect": true
    },
    {
      "questionOrderNum": 1,
      "questionText": "视频中建议的最佳实践是？",
      "selectedOptionOrderNum": 0,
      "selectedOptionText": "A. 每天工作12小时",
      "isCorrect": false
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "message": "答题记录提交成功",
  "data": 123
}
```

### 3. 查看答题记录

**请求URL**: `GET /api/video/AnswerRecord/123`

**响应**:
```json
{
  "success": true,
  "data": {
    "id": 123,
    "userId": 1,
    "batchId": 1,
    "videoId": 1,
    "answers": [
      {
        "questionOrderNum": 0,
        "questionText": "视频中提到的主要观点是什么？",
        "selectedOptionOrderNum": 0,
        "selectedOptionText": "A. 提高工作效率",
        "isCorrect": true
      },
      {
        "questionOrderNum": 1,
        "questionText": "视频中建议的最佳实践是？",
        "selectedOptionOrderNum": 0,
        "selectedOptionText": "A. 每天工作12小时",
        "isCorrect": false
      }
    ],
    "totalQuestions": 2,
    "correctAnswers": 1,
    "accuracyRate": 50.00,
    "answerTime": "2024-01-01T10:30:00",
    "createTime": "2024-01-01T10:30:00"
  }
}
```

## 数据库存储

### AnswerRecord表记录
```sql
INSERT INTO answer_records (
  user_id,
  batch_id,
  answers_json,
  total_questions,
  correct_answers,
  answer_time,
  created_at
) VALUES (
  1,
  1,
  '[{"questionOrderNum":0,"questionText":"视频中提到的主要观点是什么？","selectedOptionOrderNum":0,"selectedOptionText":"A. 提高工作效率","isCorrect":true},{"questionOrderNum":1,"questionText":"视频中建议的最佳实践是？","selectedOptionOrderNum":0,"selectedOptionText":"A. 每天工作12小时","isCorrect":false}]',
  2,
  1,
  '2024-01-01 10:30:00',
  '2024-01-01 10:30:00'
);
```

## 前端实现建议

### 1. 答题页面状态管理
```javascript
const [answers, setAnswers] = useState([]);
const [currentQuestion, setCurrentQuestion] = useState(0);

// 保存用户选择
const handleAnswerSelect = (questionIndex, optionIndex, optionText, isCorrect) => {
  const newAnswers = [...answers];
  newAnswers[questionIndex] = {
    questionOrderNum: questionIndex,
    questionText: questions[questionIndex].questionText,
    selectedOptionOrderNum: optionIndex,
    selectedOptionText: optionText,
    isCorrect: isCorrect
  };
  setAnswers(newAnswers);
};
```

### 2. 提交答题
```javascript
const submitAnswers = async () => {
  // 检查是否所有题目都已回答
  if (answers.length !== questions.length) {
    alert('请完成所有题目后再提交');
    return;
  }

  try {
    const response = await fetch('/api/video/AnswerRecord/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        batchId: batchId,
        answers: answers
      })
    });

    const result = await response.json();
    if (result.success) {
      alert('答题提交成功！');
      // 跳转到结果页面
    }
  } catch (error) {
    alert('提交失败，请重试');
  }
};
```

## 注意事项

1. **一次性提交**：用户必须完成所有题目后一次性提交，不支持分步提交
2. **不可重复**：同一用户对同一批次只能提交一次答题记录
3. **时间限制**：只能在批次的有效时间内提交
4. **数据完整性**：题目和选项内容会冗余存储，确保数据完整性
5. **正确率计算**：系统自动计算正确率，前端可直接使用
