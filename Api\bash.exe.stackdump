Stack trace:
Frame         Function      Args
0007FFFFB6C0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFA5C0) msys-2.0.dll+0x2118E
0007FFFFB6C0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6C0  0002100469F2 (00021028DF99, 0007FFFFB578, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6C0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFB6C0  00021006A545 (0007FFFFB6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFB6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF879C10000 ntdll.dll
7FF8786C0000 KERNEL32.DLL
7FF877430000 KERNELBASE.dll
7FF85D490000 apphelp.dll
7FF878210000 USER32.dll
7FF8772B0000 win32u.dll
7FF8783C0000 GDI32.dll
7FF8772E0000 gdi32full.dll
7FF8771A0000 msvcp_win.dll
7FF877080000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF878D90000 advapi32.dll
7FF878C20000 msvcrt.dll
7FF878CD0000 sechost.dll
7FF877400000 bcrypt.dll
7FF8783F0000 RPCRT4.dll
7FF8766A0000 CRYPTBASE.DLL
7FF877000000 bcryptPrimitives.dll
7FF878EA0000 IMM32.DLL
