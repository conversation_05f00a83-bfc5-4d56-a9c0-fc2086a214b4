﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Text;

namespace Common.Https
{
    public class HttpHelper
    {
        /// <summary>
        /// GET请求
        /// </summary>
        /// <param name="url">地址</param>
        /// <param name="timeout">请求时间 (秒)</param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        /// <exception cref="Exception"></exception>
        public static async Task<string> GetAsync(string url, int timeout = 30)
        {
            try
            {
                using HttpClient httpClient = new();
                httpClient.Timeout = new TimeSpan(0, 0, timeout);
                httpClient.DefaultRequestHeaders.Add("ContentType", "text/html;charset=UTF-8");

                var response = await httpClient.GetAsync(url) ?? throw new HttpRequestException("Get请求返回为空");
                if (response.IsSuccessStatusCode) return await response.Content.ReadAsStringAsync();
                return "";
            }
            catch (Exception ex)
            {
                throw new Exception($"Get请求错误，请求路径：{url},错误信息：{ex.Message}", ex);
            }
        }


        /// <summary>
        /// GET请求
        /// </summary>
        /// <param name="url">地址</param>
        /// <param name="timeout">请求时间 (秒)</param>
        /// <returns></returns>
        /// <exception cref="HttpRequestException"></exception>
        /// <exception cref="Exception"></exception>
        public static async Task<string> PostAsync<T>(string url, T data, int timeout = 30)
        {
            try
            {
                using HttpClient httpClient = new();
                httpClient.Timeout = new TimeSpan(0, 0, timeout);
                var content = new StringContent(JsonConvert.SerializeObject(data), Encoding.UTF8, "application/json");
                var response = await httpClient.PostAsync(url, content) ?? throw new HttpRequestException("Post请求返回为空");
                if (response.IsSuccessStatusCode) return await response.Content.ReadAsStringAsync();
                return "";
            }
            catch (Exception ex)
            {
                throw new Exception($"Post请求错误，请求路径：{url},错误信息：{ex.Message}", ex);
            }
        }
    }


    public static class IpAddressHelper
    {
        public static string GetIPAddress(this HttpContext context)
        {
            // 先尝试从X-Forwarded-For获取
            string forwardedIp = context.Request.Headers["X-Forwarded-For"].FirstOrDefault() ?? string.Empty;
            if (!string.IsNullOrEmpty(forwardedIp))
            {
                // 获取第一个IP地址（最初的客户端）
                string clientIp = forwardedIp.Split(',')[0].Trim();
                if (System.Net.IPAddress.TryParse(clientIp, out _))
                {
                    return clientIp;
                }
            }

            // 再尝试从RemoteIpAddress获取
            string remoteIp = context.Connection.RemoteIpAddress?.ToString() ?? "未知IP";
            return remoteIp;
        }
    }
}
