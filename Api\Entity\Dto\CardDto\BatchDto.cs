namespace Entity.Dto.CardDto
{
    /// <summary>
    /// 卡片批次DTO
    /// </summary>
    public class BatchDto
    {
        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; } = string.Empty;

        /// <summary>
        /// 卡片所属渠道
        /// </summary>
        public string Channel { get; set; } = string.Empty;

        /// <summary>
        /// 卡片类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 游戏币
        /// </summary>
        public long GameCurrency { get; set; }

        /// <summary>
        /// 实际面值
        /// </summary>
        public decimal ActualValue { get; set; }

        /// <summary>
        /// 卡片售价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 卡片数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 批次状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 制卡时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 出货目标ID
        /// </summary>
        public string TargetId { get; set; } = string.Empty;

        /// <summary>
        /// 出货目标名称
        /// </summary>
        public string TargetName { get; set; } = string.Empty;


        /// <summary>
        /// 批次创建人
        /// </summary>
        public string CreatorName { get; set; } = string.Empty;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 更新人
        /// </summary>
        public string? UpdaterName { get; set; }

        /// <summary>
        /// 卡片激活时间
        /// </summary>
        public DateTime? ActivateTime { get; set; }

        /// <summary>
        /// 激活操作人
        /// </summary>
        public string? ActivatorName { get; set; }

        /// <summary>
        /// 出货时间
        /// </summary>
        public DateTime? ShippingTime { get; set; }

        /// <summary>
        /// 出货操作人
        /// </summary>
        public string? ShippingOperatorName { get; set; }

        /// <summary>
        /// 批次备注信息
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 创建批次DTO
    /// </summary>
    public class CreateBatchDto
    {
        /// <summary>
        /// 卡片所属渠道
        /// </summary>
        public string Channel { get; set; } = string.Empty;

        /// <summary>
        /// 渠道卡片面额配置ID
        /// </summary>
        public int ChannelCardValueConfigID { get; set; }

        /// <summary>
        /// 出货目标ID
        /// </summary>
        public int ShippingTargetID { get; set; }



        /// <summary>
        /// 卡片数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 批次备注信息
        /// </summary>
        public string? Remark { get; set; }
    }

    /// <summary>
    /// 更新批次DTO
    /// </summary>
    public class UpdateBatchDto
    {
        /// <summary>
        /// 批次号
        /// </summary>
        public string BatchNo { get; set; } = string.Empty;

        /// <summary>
        /// 批次状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 批次备注信息
        /// </summary>
        public string? Remark { get; set; }
    }
}