using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.VideoDto
{




    /// <summary>
    /// 统计数据增量DTO
    /// </summary>
    public class StatisticsIncrementDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        [Required(ErrorMessage = "批次ID不能为空")]
        public int BatchId { get; set; }

        /// <summary>
        /// 统计日期
        /// </summary>
        public DateTime? StatDate { get; set; }

        /// <summary>
        /// 观看次数增量
        /// </summary>
        public int ViewCountIncrement { get; set; } = 0;

        /// <summary>
        /// 完整观看次数增量
        /// </summary>
        public int CompleteViewCountIncrement { get; set; } = 0;

        /// <summary>
        /// 新用户数增量
        /// </summary>
        public int NewUserCountIncrement { get; set; } = 0;

        /// <summary>
        /// 正确答题数增量
        /// </summary>
        public int CorrectAnswerCountIncrement { get; set; } = 0;

        /// <summary>
        /// 总答题数增量
        /// </summary>
        public int TotalAnswerCountIncrement { get; set; } = 0;

        /// <summary>
        /// 红包数量增量
        /// </summary>
        public int RewardCountIncrement { get; set; } = 0;

        /// <summary>
        /// 红包金额增量（分）
        /// </summary>
        public int RewardAmountIncrement { get; set; } = 0;

        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 视频ID
        /// </summary>
        public int? VideoId { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        public string? StatType { get; set; }

        /// <summary>
        /// 观看增量
        /// </summary>
        public int ViewIncrement { get; set; } = 0;

        /// <summary>
        /// 分享增量
        /// </summary>
        public int ShareIncrement { get; set; } = 0;

        /// <summary>
        /// 点赞增量
        /// </summary>
        public int LikeIncrement { get; set; } = 0;

        /// <summary>
        /// 评论增量
        /// </summary>
        public int CommentIncrement { get; set; } = 0;

        /// <summary>
        /// 下载增量
        /// </summary>
        public int DownloadIncrement { get; set; } = 0;

        /// <summary>
        /// 时长增量（秒）
        /// </summary>
        public long DurationIncrement { get; set; } = 0;
    }

    /// <summary>
    /// 统计数据响应DTO
    /// </summary>
    public class StatisticsResponseDto
    {
        /// <summary>
        /// 统计ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 统计日期
        /// </summary>
        public DateTime StatDate { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        public int BatchId { get; set; }

        /// <summary>
        /// 批次名称
        /// </summary>
        public string? BatchName { get; set; }

        /// <summary>
        /// 管理员ID
        /// </summary>
        public int? AdminId { get; set; }

        /// <summary>
        /// 管理员姓名
        /// </summary>
        public string? AdminName { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// 员工姓名
        /// </summary>
        public string? EmployeeName { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 视频ID
        /// </summary>
        public int? VideoId { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        public string? StatType { get; set; }

        /// <summary>
        /// 分享次数
        /// </summary>
        public int ShareCount { get; set; }

        /// <summary>
        /// 点赞次数
        /// </summary>
        public int LikeCount { get; set; }

        /// <summary>
        /// 评论次数
        /// </summary>
        public int CommentCount { get; set; }

        /// <summary>
        /// 下载次数
        /// </summary>
        public int DownloadCount { get; set; }

        /// <summary>
        /// 时长
        /// </summary>
        public long Duration { get; set; }

        /// <summary>
        /// 观看次数
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// 完整观看次数
        /// </summary>
        public int CompleteViewCount { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal CompleteRate { get; set; }

        /// <summary>
        /// 新用户数
        /// </summary>
        public int NewUserCount { get; set; }

        /// <summary>
        /// 正确答题数
        /// </summary>
        public int CorrectAnswerCount { get; set; }

        /// <summary>
        /// 总答题数
        /// </summary>
        public int TotalAnswerCount { get; set; }

        /// <summary>
        /// 正确率
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 红包数量
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// 红包金额（分）
        /// </summary>
        public int RewardAmount { get; set; }

        /// <summary>
        /// 红包金额（元）
        /// </summary>
        public decimal RewardAmountYuan => RewardAmount / 100m;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }
    }



    /// <summary>
    /// 统计汇总查询DTO
    /// </summary>
    public class StatisticsSummaryQueryDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 视频ID
        /// </summary>
        public int? VideoId { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        public int? BatchId { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        public string? StatType { get; set; }

        /// <summary>
        /// 批次ID列表
        /// </summary>
        public List<int>? BatchIds { get; set; }

        /// <summary>
        /// 管理员ID
        /// </summary>
        public int? AdminId { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
    }

    /// <summary>
    /// 统计汇总DTO
    /// </summary>
    public class StatisticsSummaryDto
    {
        /// <summary>
        /// 总观看次数
        /// </summary>
        public int TotalViewCount { get; set; }

        /// <summary>
        /// 总完整观看次数
        /// </summary>
        public int TotalCompleteViewCount { get; set; }

        /// <summary>
        /// 平均完成率
        /// </summary>
        public decimal AverageCompleteRate { get; set; }

        /// <summary>
        /// 总新用户数
        /// </summary>
        public int TotalNewUserCount { get; set; }

        /// <summary>
        /// 总正确答题数
        /// </summary>
        public int TotalCorrectAnswerCount { get; set; }

        /// <summary>
        /// 总答题数
        /// </summary>
        public int TotalAnswerCount { get; set; }

        /// <summary>
        /// 平均正确率
        /// </summary>
        public decimal AverageCorrectRate { get; set; }

        /// <summary>
        /// 总红包数量
        /// </summary>
        public int TotalRewardCount { get; set; }

        /// <summary>
        /// 总红包金额（分）
        /// </summary>
        public int TotalRewardAmount { get; set; }

        /// <summary>
        /// 总红包金额（元）
        /// </summary>
        public decimal TotalRewardAmountYuan => TotalRewardAmount / 100m;

        /// <summary>
        /// 总分享次数
        /// </summary>
        public int TotalShareCount { get; set; }

        /// <summary>
        /// 总点赞次数
        /// </summary>
        public int TotalLikeCount { get; set; }

        /// <summary>
        /// 总评论次数
        /// </summary>
        public int TotalCommentCount { get; set; }

        /// <summary>
        /// 总下载次数
        /// </summary>
        public int TotalDownloadCount { get; set; }

        /// <summary>
        /// 总时长（秒）
        /// </summary>
        public int TotalDuration { get; set; }

        /// <summary>
        /// 平均观看次数
        /// </summary>
        public decimal AverageViewCount { get; set; }

        /// <summary>
        /// 平均分享次数
        /// </summary>
        public decimal AverageShareCount { get; set; }

        /// <summary>
        /// 平均点赞次数
        /// </summary>
        public decimal AverageLikeCount { get; set; }

        /// <summary>
        /// 平均评论次数
        /// </summary>
        public decimal AverageCommentCount { get; set; }

        /// <summary>
        /// 平均下载次数
        /// </summary>
        public decimal AverageDownloadCount { get; set; }

        /// <summary>
        /// 平均时长（秒）
        /// </summary>
        public decimal AverageDuration { get; set; }

        /// <summary>
        /// 统计天数
        /// </summary>
        public int StatDays { get; set; }

        /// <summary>
        /// 统计开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 统计结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
    }

    /// <summary>
    /// 统计趋势查询DTO
    /// </summary>
    public class StatisticsTrendQueryDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 视频ID
        /// </summary>
        public int? VideoId { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        public string? StatType { get; set; }

        /// <summary>
        /// 分组方式
        /// </summary>
        public string? GroupBy { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        public int? BatchId { get; set; }

        /// <summary>
        /// 管理员ID
        /// </summary>
        public int? AdminId { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        [Required(ErrorMessage = "开始日期不能为空")]
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        [Required(ErrorMessage = "结束日期不能为空")]
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 趋势类型:view|complete|user|answer|reward
        /// </summary>
        [Required(ErrorMessage = "趋势类型不能为空")]
        public string TrendType { get; set; } = string.Empty;
    }

    /// <summary>
    /// 统计趋势DTO
    /// </summary>
    public class StatisticsTrendDto
    {
        /// <summary>
        /// 日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 数值
        /// </summary>
        public decimal Value { get; set; }

        /// <summary>
        /// 观看次数
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// 分享次数
        /// </summary>
        public int ShareCount { get; set; }

        /// <summary>
        /// 点赞次数
        /// </summary>
        public int LikeCount { get; set; }

        /// <summary>
        /// 评论次数
        /// </summary>
        public int CommentCount { get; set; }

        /// <summary>
        /// 下载次数
        /// </summary>
        public int DownloadCount { get; set; }

        /// <summary>
        /// 时长（秒）
        /// </summary>
        public int Duration { get; set; }

        /// <summary>
        /// 环比增长率
        /// </summary>
        public decimal? GrowthRate { get; set; }
    }



    /// <summary>
    /// 统计报表DTO
    /// </summary>
    public class StatisticsReportDto
    {
        /// <summary>
        /// 报表标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 报表类型
        /// </summary>
        public string ReportType { get; set; } = string.Empty;

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GenerateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 统计周期
        /// </summary>
        public string Period { get; set; } = string.Empty;

        /// <summary>
        /// 汇总数据
        /// </summary>
        public StatisticsSummaryDto Summary { get; set; } = new();

        /// <summary>
        /// 详细数据
        /// </summary>
        public List<StatisticsResponseDto> Details { get; set; } = [];

        /// <summary>
        /// 趋势数据
        /// </summary>
        public List<StatisticsTrendDto> Trends { get; set; } = [];

        /// <summary>
        /// 图表数据
        /// </summary>
        public List<object> Charts { get; set; } = [];
    }

    /// <summary>
    /// 总体统计DTO
    /// </summary>
    public class OverviewStatisticsDto
    {
        /// <summary>
        /// 总用户数
        /// </summary>
        public int TotalUsers { get; set; }

        /// <summary>
        /// 新增用户数
        /// </summary>
        public int NewUsers { get; set; }

        /// <summary>
        /// 总观看次数
        /// </summary>
        public int TotalViews { get; set; }

        /// <summary>
        /// 完播次数
        /// </summary>
        public int CompleteViews { get; set; }

        /// <summary>
        /// 完播率
        /// </summary>
        public decimal CompleteRate { get; set; }

        /// <summary>
        /// 总红包数量
        /// </summary>
        public int TotalRewards { get; set; }

        /// <summary>
        /// 红包总金额
        /// </summary>
        public decimal TotalRewardAmount { get; set; }

        /// <summary>
        /// 平均答题正确率
        /// </summary>
        public decimal AvgCorrectRate { get; set; }
    }

    /// <summary>
    /// 推广统计DTO
    /// </summary>
    public class PromotionStatisticsDto
    {
        /// <summary>
        /// 观看次数
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// 完播次数
        /// </summary>
        public int CompleteViewCount { get; set; }

        /// <summary>
        /// 完播率
        /// </summary>
        public decimal CompleteRate { get; set; }

        /// <summary>
        /// 新增用户数
        /// </summary>
        public int NewUserCount { get; set; }

        /// <summary>
        /// 答题正确次数
        /// </summary>
        public int QuestionCorrectCount { get; set; }

        /// <summary>
        /// 答题总次数
        /// </summary>
        public int QuestionTotalCount { get; set; }

        /// <summary>
        /// 答题正确率
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 红包发放次数
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// 红包发放总金额
        /// </summary>
        public decimal RewardAmount { get; set; }

        /// <summary>
        /// 推广链接
        /// </summary>
        public string? PromotionLink { get; set; }

        /// <summary>
        /// 总观看次数
        /// </summary>
        public int TotalViews { get; set; }

        /// <summary>
        /// 完成观看次数
        /// </summary>
        public int CompletedViews { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        public decimal CompletionRate { get; set; }

        /// <summary>
        /// 独立用户数
        /// </summary>
        public int UniqueUsers { get; set; }
    }

    /// <summary>
    /// 详细统计报表DTO
    /// </summary>
    public class DetailStatisticsDto
    {
        /// <summary>
        /// 统计日期
        /// </summary>
        public DateTime StatDate { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        public int? BatchId { get; set; }

        /// <summary>
        /// 批次名称
        /// </summary>
        public string? BatchName { get; set; }

        /// <summary>
        /// 管理员ID
        /// </summary>
        public int? AdminId { get; set; }

        /// <summary>
        /// 管理员姓名
        /// </summary>
        public string? AdminName { get; set; }

        /// <summary>
        /// 员工ID
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// 员工姓名
        /// </summary>
        public string? EmployeeName { get; set; }

        /// <summary>
        /// 总观看次数
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// 完播次数
        /// </summary>
        public int CompleteViewCount { get; set; }

        /// <summary>
        /// 完播率
        /// </summary>
        public decimal CompleteRate { get; set; }

        /// <summary>
        /// 新增用户数
        /// </summary>
        public int NewUserCount { get; set; }

        /// <summary>
        /// 答题正确次数
        /// </summary>
        public int CorrectAnswerCount { get; set; }

        /// <summary>
        /// 答题总次数
        /// </summary>
        public int TotalAnswerCount { get; set; }

        /// <summary>
        /// 答题正确率
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 红包发放次数
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// 红包发放总金额
        /// </summary>
        public decimal RewardAmount { get; set; }
    }

    /// <summary>
    /// 推广链接生成DTO
    /// </summary>
    public class PromotionLinkDto
    {
        /// <summary>
        /// 批次ID
        /// </summary>
        [Required(ErrorMessage = "批次ID不能为空")]
        public int BatchId { get; set; }
    }

    /// <summary>
    /// 推广链接响应DTO
    /// </summary>
    public class PromotionLinkResponseDto
    {
        /// <summary>
        /// 推广链接
        /// </summary>
        public string PromotionUrl { get; set; } = string.Empty;

        /// <summary>
        /// 二维码URL
        /// </summary>
        public string? QrCodeUrl { get; set; }
    }

    /// <summary>
    /// 系统配置DTO
    /// </summary>
    public class SystemConfigDto
    {
        /// <summary>
        /// 用户审核是否启用
        /// </summary>
        public bool UserAuditEnabled { get; set; }

        /// <summary>
        /// 微信AppID
        /// </summary>
        public string? WechatAppId { get; set; }

        /// <summary>
        /// 微信AppSecret
        /// </summary>
        public string? WechatAppSecret { get; set; }

        /// <summary>
        /// 支付功能是否启用
        /// </summary>
        public bool PaymentEnabled { get; set; }
    }

    /// <summary>
    /// 文件上传响应DTO
    /// </summary>
    public class FileUploadResponseDto
    {
        /// <summary>
        /// 文件URL
        /// </summary>
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小(字节)
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 视频时长(秒，仅视频文件)
        /// </summary>
        public int? Duration { get; set; }
    }

    // ==================== 新增仪表板相关DTO ====================

    /// <summary>
    /// 时间范围查询DTO
    /// </summary>
    public class TimeRangeQueryDto
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 获取实际的开始时间（如果为空则使用今天）
        /// </summary>
        public DateTime GetStartDate()
        {
            return StartDate ?? DateTime.Today;
        }

        /// <summary>
        /// 获取实际的结束时间（如果为空则使用今天结束）
        /// </summary>
        public DateTime GetEndDate()
        {
            return EndDate ?? DateTime.Today.AddDays(1).AddSeconds(-1);
        }
    }

    /// <summary>
    /// 仪表板汇总数据DTO
    /// </summary>
    public class DashboardSummaryDto
    {
        /// <summary>
        /// 会员总数
        /// </summary>
        public int TotalMembers { get; set; }

        /// <summary>
        /// 今日新增会员
        /// </summary>
        public int TodayNewMembers { get; set; }

        /// <summary>
        /// 今日观看用户数
        /// </summary>
        public int TodayViewers { get; set; }
    }

    /// <summary>
    /// 订单统计DTO
    /// </summary>
    public class OrderStatisticsDto
    {
        /// <summary>
        /// 订单总数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功订单数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败订单数
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 待处理订单数
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// 订单总金额（元）
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 成功订单金额（元）
        /// </summary>
        public decimal SuccessAmount { get; set; }

        /// <summary>
        /// 平均订单金额（元）
        /// </summary>
        public decimal AverageAmount { get; set; }

        /// <summary>
        /// 成功率
        /// </summary>
        public decimal SuccessRate { get; set; }



        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 统计结束时间
        /// </summary>
        public DateTime EndDate { get; set; }
    }

    /// <summary>
    /// 标签统计DTO
    /// </summary>
    public class TagStatisticsDto
    {
        /// <summary>
        /// 标签ID
        /// </summary>
        public int? TagId { get; set; }

        /// <summary>
        /// 标签名称
        /// </summary>
        public string TagName { get; set; } = string.Empty;

        /// <summary>
        /// 用户数量
        /// </summary>
        public int UserCount { get; set; }

        /// <summary>
        /// 是否为未指定标签
        /// </summary>
        public bool IsUntagged { get; set; }

        /// <summary>
        /// 占比
        /// </summary>
        public decimal Percentage { get; set; }
    }

    /// <summary>
    /// 课程统计DTO
    /// </summary>
    public class CourseStatisticsDto
    {
        /// <summary>
        /// 观看人数
        /// </summary>
        public int ViewerCount { get; set; }

        /// <summary>
        /// 完播人数
        /// </summary>
        public int CompleteViewerCount { get; set; }

        /// <summary>
        /// 完播率
        /// </summary>
        public decimal CompleteRate { get; set; }

        /// <summary>
        /// 总观看次数
        /// </summary>
        public int TotalViews { get; set; }

        /// <summary>
        /// 总完播次数
        /// </summary>
        public int TotalCompleteViews { get; set; }

        /// <summary>
        /// 平均观看时长（秒）
        /// </summary>
        public int AverageViewDuration { get; set; }



        /// <summary>
        /// 对比数据（相比上一周期的变化）
        /// </summary>
        public CourseStatisticsCompareDto? Compare { get; set; }
    }

    /// <summary>
    /// 课程统计对比DTO
    /// </summary>
    public class CourseStatisticsCompareDto
    {
        /// <summary>
        /// 观看人数变化
        /// </summary>
        public int ViewerCountChange { get; set; }

        /// <summary>
        /// 完播人数变化
        /// </summary>
        public int CompleteViewerCountChange { get; set; }

        /// <summary>
        /// 完播率变化
        /// </summary>
        public decimal CompleteRateChange { get; set; }

        /// <summary>
        /// 观看人数变化描述
        /// </summary>
        public string ViewerCountChangeText { get; set; } = string.Empty;

        /// <summary>
        /// 完播人数变化描述
        /// </summary>
        public string CompleteViewerCountChangeText { get; set; } = string.Empty;

        /// <summary>
        /// 完播率变化描述
        /// </summary>
        public string CompleteRateChangeText { get; set; } = string.Empty;
    }

    /// <summary>
    /// 答题统计DTO
    /// </summary>
    public class AnswerStatisticsDto
    {
        /// <summary>
        /// 答题人数
        /// </summary>
        public int AnswerUserCount { get; set; }

        /// <summary>
        /// 正确人数
        /// </summary>
        public int CorrectUserCount { get; set; }

        /// <summary>
        /// 正确率
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 总答题次数
        /// </summary>
        public int TotalAnswerCount { get; set; }

        /// <summary>
        /// 正确答题次数
        /// </summary>
        public int CorrectAnswerCount { get; set; }

        /// <summary>
        /// 平均答题时间（秒）
        /// </summary>
        public int AverageAnswerTime { get; set; }



        /// <summary>
        /// 对比数据（相比上一周期的变化）
        /// </summary>
        public AnswerStatisticsCompareDto? Compare { get; set; }
    }

    /// <summary>
    /// 答题统计对比DTO
    /// </summary>
    public class AnswerStatisticsCompareDto
    {
        /// <summary>
        /// 答题人数变化
        /// </summary>
        public int AnswerUserCountChange { get; set; }

        /// <summary>
        /// 正确人数变化
        /// </summary>
        public int CorrectUserCountChange { get; set; }

        /// <summary>
        /// 正确率变化
        /// </summary>
        public decimal CorrectRateChange { get; set; }

        /// <summary>
        /// 答题人数变化描述
        /// </summary>
        public string AnswerUserCountChangeText { get; set; } = string.Empty;

        /// <summary>
        /// 正确人数变化描述
        /// </summary>
        public string CorrectUserCountChangeText { get; set; } = string.Empty;

        /// <summary>
        /// 正确率变化描述
        /// </summary>
        public string CorrectRateChangeText { get; set; } = string.Empty;
    }

    /// <summary>
    /// 红包统计DTO（扩展版）
    /// </summary>
    public class RewardStatisticsDto
    {
        /// <summary>
        /// 答题红包数
        /// </summary>
        public int AnswerRewardCount { get; set; }

        /// <summary>
        /// 答题红包金额（元）
        /// </summary>
        public decimal AnswerRewardAmount { get; set; }

        /// <summary>
        /// 观看红包数
        /// </summary>
        public int ViewRewardCount { get; set; }

        /// <summary>
        /// 观看红包金额（元）
        /// </summary>
        public decimal ViewRewardAmount { get; set; }

        /// <summary>
        /// 分享红包数
        /// </summary>
        public int ShareRewardCount { get; set; }

        /// <summary>
        /// 分享红包金额（元）
        /// </summary>
        public decimal ShareRewardAmount { get; set; }

        /// <summary>
        /// 总红包数
        /// </summary>
        public int TotalRewardCount { get; set; }

        /// <summary>
        /// 总红包金额（元）
        /// </summary>
        public decimal TotalRewardAmount { get; set; }

        /// <summary>
        /// 已发放红包数
        /// </summary>
        public int DistributedCount { get; set; }

        /// <summary>
        /// 已发放红包金额（元）
        /// </summary>
        public decimal DistributedAmount { get; set; }

        /// <summary>
        /// 发放成功率
        /// </summary>
        public decimal DistributionSuccessRate { get; set; }



        /// <summary>
        /// 对比数据（相比上一周期的变化）
        /// </summary>
        public RewardStatisticsCompareDto? Compare { get; set; }
    }

    /// <summary>
    /// 红包统计对比DTO
    /// </summary>
    public class RewardStatisticsCompareDto
    {
        /// <summary>
        /// 红包数量变化
        /// </summary>
        public int RewardCountChange { get; set; }

        /// <summary>
        /// 红包金额变化（元）
        /// </summary>
        public decimal RewardAmountChange { get; set; }

        /// <summary>
        /// 红包数量变化描述
        /// </summary>
        public string RewardCountChangeText { get; set; } = string.Empty;

        /// <summary>
        /// 红包金额变化描述
        /// </summary>
        public string RewardAmountChangeText { get; set; } = string.Empty;
    }

    /// <summary>
    /// 综合仪表板DTO
    /// </summary>
    public class DashboardDto
    {
        /// <summary>
        /// 数据汇总
        /// </summary>
        public DashboardSummaryDto Summary { get; set; } = new();

        /// <summary>
        /// 标签统计
        /// </summary>
        public List<TagStatisticsDto> TagStatistics { get; set; } = [];

        /// <summary>
        /// 课程统计
        /// </summary>
        public CourseStatisticsDto CourseStatistics { get; set; } = new();

        /// <summary>
        /// 答题统计
        /// </summary>
        public AnswerStatisticsDto AnswerStatistics { get; set; } = new();

        /// <summary>
        /// 红包统计
        /// </summary>
        public RewardStatisticsDto RewardStatistics { get; set; } = new();

        /// <summary>
        /// 订单统计
        /// </summary>
        public OrderStatisticsDto OrderStatistics { get; set; } = new();

        /// <summary>
        /// 统计开始时间
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 统计结束时间
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 数据更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }
}
