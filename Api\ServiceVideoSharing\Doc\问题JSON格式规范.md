# 问题JSON格式规范

## 概述

本文档定义了视频问题JSON的标准格式，用于存储在Video和Batch实体的Questions字段中。

## JSON格式定义

### 完整格式示例

```json
[
  {
    "questionText": "视频中提到的主要观点是什么？",
    "orderNum": 0,
    "options": [
      {
        "optionText": "A. 提高工作效率",
        "isCorrect": true,
        "orderNum": 0
      },
      {
        "optionText": "B. 降低工作质量",
        "isCorrect": false,
        "orderNum": 1
      },
      {
        "optionText": "C. 增加工作时间",
        "isCorrect": false,
        "orderNum": 2
      },
      {
        "optionText": "D. 减少工作内容",
        "isCorrect": false,
        "orderNum": 3
      }
    ]
  },
  {
    "questionText": "视频中建议的最佳实践是？",
    "orderNum": 1,
    "options": [
      {
        "optionText": "A. 每天工作12小时",
        "isCorrect": false,
        "orderNum": 0
      },
      {
        "optionText": "B. 合理安排工作时间",
        "isCorrect": true,
        "orderNum": 1
      },
      {
        "optionText": "C. 忽略工作质量",
        "isCorrect": false,
        "orderNum": 2
      }
    ]
  }
]
```

## 字段说明

### 问题对象 (Question Object)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| questionText | string | 是 | 问题内容，最大长度500字符 |
| orderNum | number | 是 | 问题序号，从0开始，用于排序 |
| options | array | 是 | 选项列表，至少包含2个选项 |

### 选项对象 (Option Object)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| optionText | string | 是 | 选项内容，最大长度500字符 |
| isCorrect | boolean | 是 | 是否为正确答案，每个问题必须有且仅有一个正确答案 |
| orderNum | number | 是 | 选项序号，从0开始，用于排序 |

## 验证规则

### 基本规则
1. **问题数量**：每个视频/批次至少包含1个问题，最多不超过10个问题
2. **问题序号**：orderNum必须从0开始连续递增
3. **选项数量**：每个问题至少包含2个选项，最多不超过6个选项
4. **选项序号**：orderNum必须从0开始连续递增
5. **正确答案**：每个问题必须有且仅有一个正确答案(isCorrect=true)

### 内容规则
1. **问题内容**：不能为空，长度不超过500字符
2. **选项内容**：不能为空，长度不超过500字符
3. **重复检查**：同一问题下的选项内容不能重复

## 使用场景

### 1. 视频创建时
- 管理员在创建视频时设置问题
- 问题JSON存储在Video.Questions字段

### 2. 批次创建时
- 系统自动从关联视频复制问题JSON
- 问题JSON冗余存储在Batch.Questions字段

### 3. 用户答题时
- 前端根据问题JSON渲染答题界面
- 用户提交答案时验证选项有效性

### 4. 答题记录存储
- 用户答案按照UserAnswerDto格式存储
- 参考答题记录JSON格式说明文档

## 相关DTO类

### VideoQuestionDto
```csharp
public class VideoQuestionDto
{
    public string QuestionText { get; set; } = string.Empty;
    public int OrderNum { get; set; }
    public List<VideoQuestionOptionDto> Options { get; set; } = new();
}
```

### VideoQuestionOptionDto
```csharp
public class VideoQuestionOptionDto
{
    public string OptionText { get; set; } = string.Empty;
    public bool IsCorrect { get; set; }
    public int OrderNum { get; set; }
}
```

## 注意事项

1. **JSON序列化**：使用System.Text.Json进行序列化/反序列化
2. **字段命名**：采用camelCase命名规范
3. **编码格式**：使用UTF-8编码，支持中文字符
4. **数据完整性**：存储前必须验证JSON格式的完整性和正确性
5. **向后兼容**：修改格式时需要考虑已有数据的兼容性

## 错误处理

### 常见错误
1. **格式错误**：JSON格式不正确
2. **字段缺失**：必填字段为空或缺失
3. **序号错误**：orderNum不连续或重复
4. **答案错误**：没有正确答案或有多个正确答案
5. **长度超限**：文本内容超过最大长度限制

### 处理建议
1. 在保存前进行格式验证
2. 提供友好的错误提示信息
3. 记录详细的错误日志
4. 提供数据修复工具
