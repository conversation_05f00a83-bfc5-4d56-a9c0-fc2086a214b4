---
description: architecture
globs: 
alwaysApply: false
---
# 项目架构与设计模式

## 架构概览
本项目采用面向服务的N层架构，主要分为：
- 表示层（API控制器）
- 业务逻辑层（BLL）
- 数据访问层（DAL）
- 实体层（Entity）
- 通用组件层（Common）

## 设计模式与约定

### 依赖注入模式
- 项目使用ASP.NET Core原生依赖注入和Autofac进行服务注册
- 依赖注入配置主要在 [DataMgrSystem/Program.cs](mdc:DataMgrSystem/Program.cs) 和 [Common/Autofac/](mdc:Common/Autofac) 中

### 数据访问模式
- 使用Entity Framework Core作为ORM框架
- 可能采用Repository模式进行数据访问封装
- 核心DbContext定义在 [DAL/Databases/](mdc:DAL/Databases) 目录中

### 服务层设计
- 遵循接口实现分离原则
- 基础服务定义在 [BLL/BaseService/](mdc:BLL/BaseService) 中
- 业务服务接口通常有对应的实现类

### API响应规范
- 使用统一的响应格式：Result<T> 和 Result 类
- 控制器基类 [BaseController](mdc:DataMgrSystem/Controllers/BasisController/BaseController.cs) 提供了响应帮助方法

### 错误处理
- 使用全局异常处理中间件
- 可能使用Filter进行控制器级别的异常处理
- 自定义异常类型定义在 [Common/Exceptions/](mdc:Common/Exceptions) 中

## 认证与授权
- 使用JWT进行身份验证
- JWT相关实现位于 [Common/JWT/](mdc:Common/JWT) 目录
- 可能使用自定义Attribute进行权限控制

## 跨域请求处理
- 在API项目中配置CORS策略
- 可能通过中间件或全局配置实现

