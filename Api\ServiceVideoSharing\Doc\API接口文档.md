# 微信服务号视频营销系统 API 接口文档

## 一、接口概述

### 1.1 基础信息
- **项目名称**: 微信服务号视频营销系统
- **API版本**: v1.0
- **基础URL**: `https://api.example.com/api`
- **认证方式**: JWT <PERSON>
- **数据格式**: JSON

### 1.2 统一响应格式
```json
{
  "code": 200,
  "success": true,
  "msg": "操作成功",
  "data": {}
}
```

### 1.3 状态码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 二、认证相关接口

### 2.1 超级管理员登录
**接口地址**: `POST /auth/admin/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "123456"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": 1,
      "username": "admin",
      "role": "super_admin"
    }
  }
}
```

### 2.2 微信授权登录
**接口地址**: `POST /auth/wechat/login`

**请求参数**:
```json
{
  "code": "微信授权码",
  "state": "状态参数"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": 1,
      "openid": "oxxxxxxxxxxxxxx",
      "nickname": "微信用户",
      "avatar": "头像URL",
      "role": "admin"
    }
  }
}
```

## 三、用户管理接口

### 3.1 获取用户列表
**接口地址**: `GET /users`

**请求参数**:
```json
{
  "pageIndex": 1,
  "pageSize": 10,
  "role": "admin|employee|user",
  "parentId": 0,
  "status": "0|1|2",
  "keyword": "搜索关键词"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "openid": "oxxxxxxxxxxxxxx",
        "nickname": "用户昵称",
        "avatar": "头像URL",
        "role": "admin",
        "parentId": 0,
        "superParentId": 0,
        "phone": "13800138000",
        "realName": "真实姓名",
        "status": 1,
        "auditStatus": 1,
        "lastLogin": "2024-01-01 12:00:00",
        "createdAt": "2024-01-01 10:00:00"
      }
    ],
    "totalCount": 100,
    "pageIndex": 1,
    "pageSize": 10
  }
}
```

### 3.2 创建用户注册链接
**接口地址**: `POST /users/register-link`

**请求参数**:
```json
{
  "description": "链接描述",
  "role": "admin|employee",  // 根据当前用户权限决定可选角色
  "expireTime": "2024-01-31 23:59:59"  // 可选，链接有效期
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "生成成功",
  "data": {
    "registerUrl": "https://h5.example.com/register?type=admin&creator=xxx&token=xxxxxx",
    "qrCodeUrl": "二维码图片URL",
    "expireTime": "2024-01-31 23:59:59"
  }
}
```

### 3.4 用户审核
**接口地址**: `POST /users/{userId}/audit`

**请求参数**:
```json
{
  "status": 1,
  "remark": "审核备注"
}
```
**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "审核成功"
}
```

### 3.5 用户转移
**接口地址**: `POST /users/transfer`

**请求参数**:
```json
{
  "userIds": [1, 2, 3],
  "toEmployeeId": 10,
  "toAdminId": 5,
  "reason": "转移原因"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "转移成功"
}
```

## 四、视频管理接口

### 4.1 获取视频列表
**接口地址**: `GET /videos`

**请求参数**:
```json
{
  "pageIndex": 1,
  "pageSize": 10,
  "status": "0|1",
  "keyword": "搜索关键词"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "视频标题",
        "description": "视频描述",
        "coverUrl": "封面URL",
        "videoUrl": "视频URL",
        "duration": 120,
        "creatorId": 1,
        "rewardAmount": 5.00,
        "questions": [
          {
            "questionText": "问题内容",
            "orderNum": 0,
            "options": [
              {
                "optionText": "选项1",
                "isCorrect": true,
                "orderNum": 0
              }
            ]
          }
        ],
        "status": 1,
        "createdAt": "2024-01-01 10:00:00"
      }
    ],
    "totalCount": 50,
    "pageIndex": 1,
    "pageSize": 10
  }
}
```

### 4.2 创建/更新视频
**接口地址**: `POST /videos[/{videoId}]`

**请求参数**:
```json
{
  "title": "视频标题",
  "description": "视频描述",
  "coverUrl": "封面URL",
  "videoUrl": "视频URL",
  "duration": 120,
  "rewardAmount": 5.00,
  "questions": [
    {
      "questionText": "问题内容",
      "orderNum": 0,
      "options": [
        {
          "optionText": "选项1",
          "isCorrect": true,
          "orderNum": 0
        },
        {
          "optionText": "选项2",
          "isCorrect": false,
          "orderNum": 1
        }
      ]
    }
  ]
}
```
**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "操作成功"
}
```

### 4.4 删除视频
**接口地址**: `POST /videos/{videoId}`

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "删除成功"
}
```


## 五、批次管理接口

### 5.1 获取批次列表
**接口地址**: `GET /batches`

**请求参数**:
```json
{
  "pageIndex": 1,
  "pageSize": 10,
  "status": "0|1",
  "keyword": "搜索关键词"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "批次名称",
        "description": "批次描述",
        "videoId": 1,
        "videoTitle": "视频标题",
        "startTime": "2024-01-01 00:00:00",
        "endTime": "2024-01-31 23:59:59",
        "creatorId": 1,
        "status": 1,
        "statistics": {
          "viewCount": 100,
          "completeViewCount": 80,
          "rewardCount": 60
        }
      }
    ],
    "totalCount": 20,
    "pageIndex": 1,
    "pageSize": 10
  }
}
```

### 5.2 创建批次
**接口地址**: `POST /batches`

**请求参数**:
```json
{
  "name": "批次名称",
  "description": "批次描述",
  "videoId": 1,
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-01-31 23:59:59"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "创建成功"
}
```


### 5.4 批次删除
**接口地址**: `POST /batches/{batchId}/delete`

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "删除成功"
}
```

## 六、推广链接接口

### 6.1 生成推广链接
**接口地址**: `POST /promotion/generate-link`

**请求参数**:
```json
{
  "batchId": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "生成成功",
  "data": {
    "promotionUrl": "https://h5.example.com/video?batch=1&employee=10&admin=5",
    "qrCodeUrl": "二维码图片URL"
  }
}
```

### 6.2 获取推广数据
**接口地址**: `GET /promotion/statistics`

**请求参数**:
```json
{
  "batchId": 1,
  "startDate": "2024-01-01",
  "endDate": "2024-01-31"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "获取成功",
  "data": {
    "viewCount": 100,
    "completeViewCount": 80,
    "completeRate": 80.00,
    "newUserCount": 50,
    "questionCorrectCount": 70,
    "questionTotalCount": 80,
    "correctRate": 87.50,
    "rewardCount": 60,
    "rewardAmount": 300.00
  }
}
```

## 七、用户观看接口

### 7.1 用户访问推广链接
**接口地址**: `GET /watch/access`

**请求参数**:
```json
{
  "batchId": 1,
  "employeeId": 10,
  "adminId": 5,
  "code": "微信授权码"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "访问成功",
  "data": {
    "needAudit": true,
    "auditStatus": 0,
    "batch": {
      "id": 1,
      "name": "批次名称",
      "video": {
        "id": 1,
        "title": "视频标题",
        "description": "视频描述",
        "coverUrl": "封面URL",
        "videoUrl": "视频URL",
        "duration": 120,
        "rewardAmount": 5.00
      }
    }
  }
}
```

### 7.2 记录观看进度
**接口地址**: `POST /watch/progress`

**请求参数**:
```json
{
  "batchId": 1,
  "videoId": 1,
  "viewDuration": 60,
  "isComplete": false
}
```

### 7.3 提交答题结果
**接口地址**: `POST /watch/answer`

**请求参数**:
```json
{
  "batchId": 1,
  "videoId": 1,
  "answers": [
    {
      "questionText": "问题内容",
      "optionText": "选项内容",
      "isCorrect": true
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "提交成功",
  "data": {
    "correctCount": 2,
    "totalCount": 2,
    "correctRate": 100.00,
    "canGetReward": true,
    "rewardAmount": 5.00
  }
}
```

### 7.4 领取红包
**接口地址**: `POST /watch/claim-reward`

**请求参数**:
```json
{
  "batchId": 1,
  "videoId": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "红包发放成功",
  "data": {
    "rewardId": 1,
    "amount": 5.00,
    "transactionId": "微信支付交易ID"
  }
}
```

## 八、数据统计接口

### 8.1 获取总体统计数据
**接口地址**: `GET /statistics/overview`

**请求参数**:
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "adminId": 5,
  "employeeId": 10,
  "batchId": 1
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "获取成功",
  "data": {
    "totalUsers": 1000,
    "newUsers": 100,
    "totalViews": 500,
    "completeViews": 400,
    "completeRate": 80.00,
    "totalRewards": 300,
    "totalRewardAmount": 1500.00,
    "avgCorrectRate": 85.50
  }
}
```

### 8.2 获取详细统计报表
**接口地址**: `GET /statistics/report`

**请求参数**:
```json
{
  "pageIndex": 1,
  "pageSize": 10,
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "groupBy": "date|admin|employee|batch"
}
```

### 8.3 获取用户观看记录
**接口地址**: `GET /statistics/view-records`

**请求参数**:
```json
{
  "pageIndex": 1,
  "pageSize": 10,
  "batchId": 1,
  "userId": 100,
  "startDate": "2024-01-01",
  "endDate": "2024-01-31"
}
```

### 8.4 获取红包发放记录
**接口地址**: `GET /statistics/reward-records`

**请求参数**:
```json
{
  "pageIndex": 1,
  "pageSize": 10,
  "status": "0|1|2",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31"
}
```

## 九、系统配置接口

### 9.1 获取系统配置
**接口地址**: `GET /system/configs`

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "获取成功",
  "data": {
    "userAuditEnabled": true,
    "wechatAppId": "微信AppID",
    "wechatAppSecret": "微信AppSecret",
    "paymentEnabled": true
  }
}
```

### 9.2 更新系统配置
**接口地址**: `PUT /system/configs`

**请求参数**:
```json
{
  "userAuditEnabled": true,
  "wechatAppId": "微信AppID",
  "wechatAppSecret": "微信AppSecret",
  "paymentEnabled": true
}
```

## 十、文件上传接口

### 10.1 上传视频文件
**接口地址**: `POST /upload/video`

**请求参数**: FormData
- `file`: 视频文件

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "上传成功",
  "data": {
    "url": "https://cdn.example.com/videos/xxx.mp4",
    "duration": 120,
    "size": 10485760
  }
}
```

### 10.2 上传图片文件
**接口地址**: `POST /upload/image`

**请求参数**: FormData
- `file`: 图片文件

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "msg": "上传成功",
  "data": {
    "url": "https://cdn.example.com/images/xxx.jpg"
  }
}
```

## 十一、错误码说明

### 11.1 业务错误码
- `10001`: 用户不存在
- `10002`: 用户已存在
- `10003`: 用户状态异常
- `10004`: 用户审核未通过
- `20001`: 视频不存在
- `20002`: 视频已下架
- `30001`: 批次不存在
- `30002`: 批次已结束
- `30003`: 批次未开始
- `40001`: 推广链接无效
- `40002`: 推广链接已过期
- `50001`: 红包发放失败
- `50002`: 红包已领取
- `50003`: 答题正确率不足

### 11.2 系统错误码
- `90001`: 参数验证失败
- `90002`: 数据库操作失败
- `90003`: 第三方服务调用失败
- `90004`: 文件上传失败
- `90005`: 权限不足

## 十二、接口调用示例

### 12.1 完整的用户观看流程示例

```javascript
// 1. 用户点击推广链接，获取微信授权
const accessResponse = await fetch('/api/watch/access', {
  method: 'GET',
  params: {
    batchId: 1,
    employeeId: 10,
    adminId: 5,
    code: 'wx_auth_code'
  }
});

// 2. 如果需要审核，等待审核通过
if (accessResponse.data.needAudit && accessResponse.data.auditStatus === 0) {
  // 显示等待审核页面
  return;
}

// 3. 观看视频，记录进度
await fetch('/api/watch/progress', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    batchId: 1,
    videoId: 1,
    viewDuration: 120,
    isComplete: true
  })
});

// 4. 提交答题结果
const answerResponse = await fetch('/api/watch/answer', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    batchId: 1,
    videoId: 1,
    answers: [
      {
        questionText: "问题1",
        optionText: "正确答案",
        isCorrect: true
      }
    ]
  })
});

// 5. 如果可以领取红包，发起领取
if (answerResponse.data.canGetReward) {
  await fetch('/api/watch/claim-reward', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      batchId: 1,
      videoId: 1
    })
  });
}
```

## 十三、注意事项

### 13.1 安全性
- 所有接口都需要进行JWT token验证
- 敏感操作需要进行权限验证
- 文件上传需要进行类型和大小限制
- 防止SQL注入和XSS攻击

### 13.2 性能优化
- 使用Redis缓存热点数据
- 数据库查询使用索引优化
- 大数据量接口使用分页
- 静态资源使用CDN加速

### 13.3 数据一致性
- 关键业务操作使用事务
- 红包发放使用分布式锁
- 统计数据定时同步
- 异常情况下的数据回滚机制
