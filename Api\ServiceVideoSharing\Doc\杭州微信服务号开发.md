# 杭州微信服务号项目开发文档

## 一、项目概述

本项目旨在设计并实现一个基于微信服务号的视频营销系统，通过推广链接引导用户观看视频内容，完成相关问答后可获取现金红包奖励。系统将建立完整的用户绑定机制、推广关系管理、数据统计分析及后台管理功能，为推广活动提供全方位支持。

**核心业务价值：**

- 提升用户粘性，促进内容传播
- 建立精准的推广渠道分析
- 收集高质量用户信息
- 通过视频内容加问答机制提升品牌记忆

## 二、系统角色划分

系统分为四个主要角色：

1. **超级管理**：系统最高权限，负责管理管理和内容审核
2. **管理**：由超级管理创建，负责员工管理和内容推广
3. **员工**：由管理创建，负责具体推广工作
4. **用户**：普通微信用户，观看视频并参与问答

## 三、核心业务流程

### 1. 注册与授权流程

#### 1.1 管理注册流程

- 超级管理生成专属管理注册链接
- 管理点击链接进入注册页面
- 超级管理审核管理注册信息
- 审核通过后管理账号激活

#### 1.2 员工注册流程

- 管理生成专属员工注册链接
- 员工点击链接进入注册页面
- 管理审核员工注册信息
- 审核通过后员工账号激活

### 2. 内容管理流程

#### 2.1 视频上传与配置

- 超级管理或管理上传视频
- 设置视频基本信息（标题、描述、封面）
- 配置视频问答（选择题，最大2题）
- 设置红包规则（金额）(答题正确率达到50%及以上即可领取)

#### 2.2 批次管理流程

- 创建批次（名称、时间范围）
- 选择单个视频（每个批次关联一个视频）
- 批次上线后可供推广

### 3. 推广流程

#### 3.1 员工推广流程

- 员工选择批次生成推广链接
- 分享链接给潜在用户
- 实时查看推广数据

#### 3.2 用户访问流程

- 用户点击员工分享的批次推广链接
- 系统记录来源参数（批次ID、员工ID、管理ID）
- 用户微信授权绑定
- 如果用户审核功能开启，用户需等待员工审核通过后才能观看视频
- 用户观看视频
- 完播后回答问题
- 答题正确率达到50%及以上可领取红包

#### 3.3 用户审核机制

- 用户点击推广链接并完成微信授权绑定后进入审核队列
- 超级管理可在系统配置中开启或关闭全局用户审核功能
- 审核功能开启时，员工必须审核用户才能让用户观看视频
- 审核功能关闭时，用户无需审核即可直接观看视频

## 四、系统功能设计

### 1. 超级管理端功能

#### 1.1 登录页面

- **登录功能**
  - 超级管理账号密码登录
  - 登录安全策略

#### 1.2 管理管理功能

- **管理列表**
  - 管理信息展示（ID、名称、状态）
  - 管理状态管理（启用/禁用）

- **管理创建**
  - 生成专属管理注册链接
  - 链接管理与追踪

- **管理审核**
  - 待审核管理列表
  - 管理注册信息审核

- **客户转移功能**
  - 单个用户在不同管理或员工之间转移
  - 批量用户转移

#### 1.3 内容管理功能

- **视频管理**
  - 视频列表与搜索
  - 视频上传与编辑
  - 视频状态控制（上架/下架）

- **视频内容配置**
  - 视频基本信息设置（标题、描述、封面）
  - 视频问答配置（直接关联到视频）
    - 选择题创建（唯一题型）
    - 选项设置与正确答案标记
    - 问题数量设置（最大两道题）
  - 视频红包配置
    - 红包金额设置（直接关联到视频）
    - 红包发放规则设置（答题正确率达到50%及以上即可领取）

#### 1.4 批次管理功能

- **批次创建**
  - 批次基本信息设置（名称、时间范围）
  - 选择单个视频（每个批次关联一个视频）

- **批次列表**
  - 批次信息展示（包含关联的视频信息）
  - 批次数据统计
  - 批次详情查看

#### 1.5 系统配置页面

- **审核开关设置**
  - 全局用户审核开关（开启时员工需审核用户，关闭时用户无需审核）

### 2. 管理端功能

#### 2.1 登录与个人中心

- **登录页面**
  - 微信登录
  - 记住登录状态

#### 2.2 员工管理功能

- **员工列表**
  - 员工信息展示（ID、姓名）
  - 员工筛选与搜索
  - 仅显示当前管理创建的员工

- **员工创建**
  - 生成专属员工注册链接

- **员工审核**
  - 待审核员工列表
  - 员工注册信息审核
  - 审核通过/拒绝操作

- **员工数据统计**
  - 员工数据信息

- **员工离职功能**
  - 员工离职标记


#### 2.3 内容管理功能

- **视频管理**
  - 与超级管理端相同

#### 2.4 批次管理功能

- **批次列表**
  - 批次状态查看
  - 批次详情浏览

#### 2.5 数据统计功能

- **个人业绩**
  - 推广数据统计
  - 新增用户统计
  - 完播率统计
  - 红包发放统计

### 3. 员工端功能

#### 3.1 登录与个人中心

- **登录页面**
  - 微信登录

#### 3.2 批次推广功能

- **推广链接生成**
  - 选择批次生成推广链接
  - 链接二维码生成
  - 链接分享功能

- **链接数据统计**
  - 用户观看、红包领取等信息

#### 3.3 内容浏览功能

- **视频列表**
  - 可推广批次浏览
  - 视频详情查看

#### 3.4 用户审核功能

- **待审核列表**
  - 待审核用户队列（仅当超级管理开启全局审核功能时显示）
  - 用户基本信息展示

- **审核操作**
  - 审核通过/拒绝按钮

#### 3.5 数据统计功能

- **个人业绩**
  - 与管理端数据统计功能相同，但仅显示员工个人数据

## 五、批次管理设计

### 1. 批次概念定义

- **批次**：一个推广单元，每个批次关联一个视频
- **批次周期**：批次的有效时间范围，包括开始时间和结束时间
- **批次视频**：每个批次关联的视频（包含视频问答和红包配置）

### 2. 批次创建流程

- **批次基础设置**
  - 批次名称与描述
  - 批次有效时间范围

- **批次视频选择**
  - 从已配置好的视频库中选择一个视频
  - 视频已包含问答和红包配置

### 3. 批次数据记录

- **观看记录**
  - 用户观看批次视频的记录
  - 记录批次ID、视频ID、用户ID、员工ID、管理ID
  - 记录观看时间、观看时长、是否完播

- **问答记录**
  - 用户回答问题的记录
  - 记录批次ID、视频ID、问题ID、用户ID
  - 记录答题时间、答题结果

- **红包记录**
  - 用户领取红包的记录
  - 记录批次ID、视频ID、用户ID、红包金额
  - 记录发放时间、发放状态

