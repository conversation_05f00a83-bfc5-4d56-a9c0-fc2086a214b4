using BLL.SysService;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.SysControllers
{
    /// <summary>
    /// 用户管理控制器
    /// 提供用户的增删改查、角色分配、密码管理等功能
    /// </summary>
    [Permission]
    public class SysUserController(SysUserService userService, SysLogService logService) : BaseController
    {
        /// <summary>
        /// 用户服务接口
        /// </summary>
        private readonly SysUserService _userService = userService;

        /// <summary>
        /// 日志服务接口
        /// </summary>
        private readonly SysLogService _logService = logService;

        #region 基础CRUD操作

        /// <summary>
        /// 创建新用户
        /// </summary>
        /// <param name="input">创建用户请求</param>
        /// <returns>创建结果</returns>

        [HttpPost("create", Name = "SysUser_Create")]
        public async Task<Result<string>> CreateAsync(SysCreateUserDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 在控制器层进行基础权限检查
            ValidateUserCreationPermission(input.UserType, currentUser.UserType);

            // 执行业务操作
            var userId = await _userService.CreateAsync(input, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "创建用户",
                BusinessObject = "SysUser",
                ObjectId = userId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功创建了新用户 {input.UserName}（类型：{GetUserTypeDescription(input.UserType)}），用户ID: {userId}",
                AfterData = new { UserId = userId, input.UserName, input.RealName, input.UserType },
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(userId, "创建成功");
        }

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="input">更新用户请求</param>
        /// <returns>更新结果</returns>

        [HttpPost("update", Name = "SysUser_Update")]
        public async Task<Result> UpdateAsync([FromBody] SysUpdateUserDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取更新前的用户信息，用于日志记录
            var beforeUser = await _userService.GetAsync(input.UserId);

            // 执行业务操作
            await _userService.UpdateAsync(input, currentUser);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "更新用户",
                BusinessObject = "SysUser",
                ObjectId = input.UserId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功更新了用户 {input.UserId} 的信息",
                BeforeData = beforeUser,
                AfterData = input,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("更新成功");
        }

        /// <summary>
        /// 删除指定用户
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>删除结果</returns>

        [HttpPost("delete/{id}", Name = "SysUser_Delete")]
        public async Task<Result> DeleteAsync(string id)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取删除前的用户信息，用于日志记录
            var beforeUser = await _userService.GetAsync(id);

            // 执行业务操作
            await _userService.DeleteAsync(id);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "删除用户",
                BusinessObject = "SysUser",
                ObjectId = id,
                DetailedInfo = $"用户 {currentUser.UserName} 成功删除了用户 {beforeUser.UserName}，用户ID: {id}",
                BeforeData = beforeUser,
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("删除成功");
        }

        /// <summary>
        /// 获取用户详细信息
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户详细信息</returns>
        [HttpGet("{id}", Name = "SysUser_Get")]
        public async Task<Result<SysUserDto>> GetAsync(string id)
        => Success(await _userService.GetAsync(id), "获取成功");



        #endregion

        #region 密码管理

        /// <summary>
        /// 修改用户密码
        /// </summary>
        /// <param name="input">修改密码请求</param>
        /// <returns>修改结果</returns>
        [HttpPost("change-password", Name = "SysUser_ChangePassword")]
        public async Task<Result> ChangePasswordAsync(SysChangePasswordDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 执行业务操作
            await _userService.ChangePasswordAsync(input);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "修改密码",
                BusinessObject = "SysUser",
                ObjectId = input.UserId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功修改了密码",
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("密码修改成功");
        }

        /// <summary>
        /// 重置用户密码
        /// </summary>
        /// <param name="input">重置密码请求</param>
        /// <returns>重置结果</returns>

        [HttpPost("reset-password", Name = "SysUser_ResetPassword")]
        public async Task<Result> ResetPasswordAsync(SysResetPasswordDto input)
        {
            // 获取当前用户信息
            var currentUser = GetCurrentUserInfo();

            // 获取重置密码前的用户信息，用于日志记录
            var targetUser = await _userService.GetAsync(input.UserId);

            // 执行业务操作
            await _userService.ResetPasswordAsync(input);

            // 记录操作成功的业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "重置密码",
                BusinessObject = "SysUser",
                ObjectId = input.UserId,
                DetailedInfo = $"用户 {currentUser.UserName} 成功重置了用户 {targetUser.UserName} 的密码",
                BeforeData = new { UserId = targetUser.Id, targetUser.UserName },
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success("密码重置成功");
        }

        #endregion



        #region 私有辅助方法

        /// <summary>
        /// 验证用户创建权限
        /// </summary>
        /// <param name="targetUserType">要创建的用户类型</param>
        /// <param name="currentUserType">当前用户类型</param>
        /// <exception cref="UnauthorizedAccessException">权限不足时抛出异常</exception>
        private static void ValidateUserCreationPermission(byte targetUserType, byte currentUserType)
        {
            switch (targetUserType)
            {
                case 1: // 超级管理员
                    throw new UnauthorizedAccessException("不允许创建超级管理员账户");

                case 2: // 管理员
                    if (currentUserType != 1)
                        throw new UnauthorizedAccessException("只有超级管理员才能创建管理员账户");
                    break;

                case 3: // 员工
                    if (currentUserType != 1 && currentUserType != 2)
                        throw new UnauthorizedAccessException("只有超级管理员和管理员才能创建员工账户");
                    break;

                default:
                    throw new ArgumentException("无效的用户类型");
            }
        }

        /// <summary>
        /// 获取用户类型描述
        /// </summary>
        /// <param name="userType">用户类型</param>
        /// <returns>用户类型描述</returns>
        private static string GetUserTypeDescription(byte userType)
        {
            return userType switch
            {
                1 => "超级管理员",
                2 => "管理员",
                3 => "员工",
                _ => "未知类型"
            };
        }

        #endregion

        #region 层级查询和统计接口

        /// <summary>
        /// 获取管理员列表（带统计数据）
        /// </summary>
        /// <param name="startTime">统计开始时间</param>
        /// <param name="endTime">统计结束时间</param>
        /// <param name="pageIndex">页码，默认为1</param>
        /// <param name="pageSize">每页大小，默认为20</param>
        /// <param name="userName">用户名（模糊查询）</param>
        /// <param name="realName">真实姓名（模糊查询）</param>
        /// <param name="status">用户状态过滤</param>
        /// <returns>管理员列表</returns>
        [HttpGet("administrators", Name = "SysUser_GetAdministrators")]
        public async Task<Result<List<SysUserWithStatisticsDto>>> GetAdministratorsAsync(
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? userName = null,
            [FromQuery] string? realName = null,
            [FromQuery] byte? status = null)
        {
            var currentUser = GetCurrentUserInfo();
            var query = new SubordinateQueryDto
            {
                StartTime = startTime,
                EndTime = endTime,
                PageIndex = pageIndex,
                PageSize = pageSize,
                UserName = userName,
                RealName = realName,
                Status = status
            };

            var administrators = await _userService.GetAdministratorsWithStatisticsAsync(query, currentUser);

            // 记录操作日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "查询管理员列表",
                BusinessObject = "SysUser",
                DetailedInfo = $"用户 {currentUser.UserName} 查询了管理员列表，时间范围：{startTime} - {endTime}",
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(administrators, "获取管理员列表成功");
        }

        /// <summary>
        /// 获取员工列表（带统计数据）
        /// </summary>
        /// <param name="startTime">统计开始时间</param>
        /// <param name="endTime">统计结束时间</param>
        /// <param name="pageIndex">页码，默认为1</param>
        /// <param name="pageSize">每页大小，默认为20</param>
        /// <param name="userName">用户名（模糊查询）</param>
        /// <param name="realName">真实姓名（模糊查询）</param>
        /// <param name="status">用户状态过滤</param>
        /// <returns>员工列表</returns>
        [HttpGet("employees", Name = "SysUser_GetEmployees")]
        public async Task<Result<List<SysUserWithStatisticsDto>>> GetEmployeesAsync(
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? userName = null,
            [FromQuery] string? realName = null,
            [FromQuery] byte? status = null)
        {
            var currentUser = GetCurrentUserInfo();
            var query = new SubordinateQueryDto
            {
                StartTime = startTime,
                EndTime = endTime,
                PageIndex = pageIndex,
                PageSize = pageSize,
                UserName = userName,
                RealName = realName,
                Status = status
            };

            var employees = await _userService.GetEmployeesWithStatisticsAsync(query, currentUser);

            // 记录操作日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "查询员工列表",
                BusinessObject = "SysUser",
                DetailedInfo = $"用户 {currentUser.UserName} 查询了员工列表，时间范围：{startTime} - {endTime}",
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(employees, "获取员工列表成功");
        }

        /// <summary>
        /// 获取当前用户的下级列表（带统计数据）
        /// </summary>
        /// <param name="startTime">统计开始时间</param>
        /// <param name="endTime">统计结束时间</param>
        /// <param name="pageIndex">页码，默认为1</param>
        /// <param name="pageSize">每页大小，默认为20</param>
        /// <param name="userName">用户名（模糊查询）</param>
        /// <param name="realName">真实姓名（模糊查询）</param>
        /// <param name="status">用户状态过滤</param>
        /// <returns>下级列表</returns>
        [HttpGet("subordinates", Name = "SysUser_GetSubordinates")]
        public async Task<Result<List<SysUserWithStatisticsDto>>> GetSubordinatesAsync(
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? userName = null,
            [FromQuery] string? realName = null,
            [FromQuery] byte? status = null)
        {
            var currentUser = GetCurrentUserInfo();
            var query = new SubordinateQueryDto
            {
                StartTime = startTime,
                EndTime = endTime,
                PageIndex = pageIndex,
                PageSize = pageSize,
                UserName = userName,
                RealName = realName,
                Status = status
            };

            var subordinates = await _userService.GetSubordinatesWithStatisticsAsync(query, currentUser);

            // 记录操作日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "查询下级列表",
                BusinessObject = "SysUser",
                DetailedInfo = $"用户 {currentUser.UserName} 查询了自己的下级列表，时间范围：{startTime} - {endTime}",
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(subordinates, "获取下级列表成功");
        }

        /// <summary>
        /// 获取指定用户的下级列表（带统计数据）
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <param name="startTime">统计开始时间</param>
        /// <param name="endTime">统计结束时间</param>
        /// <param name="pageIndex">页码，默认为1</param>
        /// <param name="pageSize">每页大小，默认为20</param>
        /// <param name="userName">用户名（模糊查询）</param>
        /// <param name="realName">真实姓名（模糊查询）</param>
        /// <param name="status">用户状态过滤</param>
        /// <returns>下级列表</returns>
        [HttpGet("subordinates/{id}", Name = "SysUser_GetSubordinatesById")]
        public async Task<Result<List<SysUserWithStatisticsDto>>> GetSubordinatesByIdAsync(
            string id,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? userName = null,
            [FromQuery] string? realName = null,
            [FromQuery] byte? status = null)
        {
            var currentUser = GetCurrentUserInfo();
            var query = new SubordinateQueryDto
            {
                StartTime = startTime,
                EndTime = endTime,
                PageIndex = pageIndex,
                PageSize = pageSize,
                UserName = userName,
                RealName = realName,
                Status = status
            };

            var subordinates = await _userService.GetSubordinatesByIdWithStatisticsAsync(id, query, currentUser);

            // 记录操作日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户管理",
                Operation = "查询指定用户下级列表",
                BusinessObject = "SysUser",
                ObjectId = id,
                DetailedInfo = $"用户 {currentUser.UserName} 查询了用户 {id} 的下级列表，时间范围：{startTime} - {endTime}",
                UserId = currentUser.UserId,
                Username = currentUser.UserName,
                Ip = IP
            });

            return Success(subordinates, "获取下级列表成功");
        }

        #endregion
    }
}