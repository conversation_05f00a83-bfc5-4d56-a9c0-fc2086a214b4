using BLL.VideoService;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 用户转移控制器 - 简化版，只处理员工绑定转移
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class UserTransferController(UserTransferService userTransferService) : BaseController
    {
        private readonly UserTransferService _userTransferService = userTransferService;

        /// <summary>
        /// 转移用户到新员工
        /// </summary>
        /// <param name="transferDto">用户转移DTO</param>
        /// <returns>是否成功</returns>
        [HttpPost(Name = "UserTransfer_Transfer")]
        public async Task<Result<bool>> TransferUsers([FromBody] UserTransferDto transferDto)
        {
            var result = await _userTransferService.TransferUsersAsync(transferDto, GetCurrentUserInfo());
            return Success(result, "用户转移成功");
        }

        /// <summary>
        /// 分页查询用户转移记录列表
        /// 支持按用户ID、员工ID、操作员ID、时间范围等条件查询
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>转移记录列表</returns>
        [HttpGet]
        public async Task<Result<PagedResult<UserTransferResponseDto>>> GetUserTransferPagedList([FromQuery] UserTransferQueryDto queryDto)
        {
            var result = await _userTransferService.GetUserTransferPagedListAsync(queryDto);
            return Success(result);
        }
    }
}
