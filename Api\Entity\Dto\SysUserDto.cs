using System.ComponentModel.DataAnnotations;

namespace Entity.Dto
{
    /// <summary>
    /// 系统用户基础信息DTO
    /// </summary>
    public class SysUserBaseDto
    {
        /// <summary>
        /// 真实姓名,最大长度50个字符
        /// </summary>
        [MaxLength(50)]
        public string RealName { get; set; } = string.Empty;

        /// <summary>
        /// 头像URL地址,最大长度200个字符
        /// </summary>
        [MaxLength(200)]
        public string Avatar { get; set; } = string.Empty;

        /// <summary>
        /// 电子邮箱地址,最大长度100个字符,必须符合邮箱格式
        /// </summary>
        //[EmailAddress]
        [MaxLength(100)]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 手机号码,最大长度20个字符,必须符合手机号格式
        /// </summary>
        //[Phone]
        [MaxLength(20)]
        public string Mobile { get; set; } = string.Empty;

        /// <summary>
        /// 备注信息,最大长度500个字符
        /// </summary>
        [MaxLength(500)]
        public string Remark { get; set; } = string.Empty;

        /// <summary>
        /// 用户状态,0:禁用 1:启用
        /// </summary>
        public byte Status { get; set; }
    }

    /// <summary>
    /// 创建系统用户DTO,继承自SysUserBaseDto
    /// </summary>
    public class SysCreateUserDto : SysUserBaseDto
    {
        /// <summary>
        /// 用户名,必填,最大长度50个字符
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        [MaxLength(50)]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 密码,必填,最大长度100个字符
        /// </summary>
        [Required(ErrorMessage = "密码不能为空")]
        [MaxLength(100)]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 用户类型,必填
        /// 1:超级管理员 2:管理员 3:员工
        /// </summary>
        [Required(ErrorMessage = "用户类型不能为空")]
        [Range(2, 3, ErrorMessage = "用户类型必须在2-3之间")]
        public byte UserType { get; set; }
    }

    /// <summary>
    /// 更新系统用户DTO,继承自SysUserBaseDto
    /// </summary>
    public class SysUpdateUserDto : SysUserBaseDto
    {
        /// <summary>
        /// 用户ID,必填
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public string UserId { get; set; } = string.Empty;
    }

    /// <summary>
    /// 系统用户信息DTO,继承自SysUserBaseDto,用于返回完整的用户信息
    /// </summary>
    public class SysUserDto : SysUserBaseDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName { get; set; } = string.Empty;

        /// <summary>
        /// 角色编码
        /// </summary>
        public string RoleCode { get; set; } = string.Empty;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 最后登录IP地址
        /// </summary>
        public string? LastLoginIp { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }

    // /// <summary>
    // /// 用户查询DTO,用于查询用户列表时的条件参数
    // /// </summary>
    // public class QueryUserDto : BaseQueryDto
    // {
    //     /// <summary>
    //     /// 用户名(模糊查询)
    //     /// </summary>
    //     public string UserName { get; set; } = string.Empty;

    //     /// <summary>
    //     /// 真实姓名(模糊查询)
    //     /// </summary>
    //     public string RealName { get; set; } = string.Empty;

    //     /// <summary>
    //     /// 手机号码(模糊查询)
    //     /// </summary>
    //     public string Mobile { get; set; } = string.Empty;

    //     /// <summary>
    //     /// 电子邮箱(模糊查询)
    //     /// </summary>
    //     public string Email { get; set; } = string.Empty;

    //     /// <summary>
    //     /// 用户状态(精确查询) 0:禁用 1:启用
    //     /// </summary>
    //     public byte? Status { get; set; }

    //     /// <summary>
    //     /// 开始时间,用于按创建时间范围查询
    //     /// </summary>
    //     public DateTime? StartTime { get; set; }

    //     /// <summary>
    //     /// 结束时间,用于按创建时间范围查询
    //     /// </summary>
    //     public DateTime? EndTime { get; set; }
    // }

    /// <summary>
    /// 修改密码DTO,用于系统用户修改自己的密码
    /// </summary>
    public class SysChangePasswordDto
    {
        /// <summary>
        /// 用户ID,必填
        /// </summary>
        [Required]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 旧密码,必填
        /// </summary>
        [Required]
        public string OldPassword { get; set; } = string.Empty;

        /// <summary>
        /// 新密码,必填,最小长度6个字符
        /// </summary>
        [Required]
        [MinLength(6)]
        public string NewPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 重置密码DTO,用于管理员重置系统用户密码
    /// </summary>
    public class SysResetPasswordDto
    {
        /// <summary>
        /// 用户ID,必填
        /// </summary>
        [Required]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 新密码,必填,最小长度6个字符
        /// </summary>
        [Required]
        [MinLength(6)]
        public string NewPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// 系统用户角色信息DTO，用于返回系统用户关联的角色信息
    /// </summary>
    public class SysUserRoleDto
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public string RoleId { get; set; } = string.Empty;

        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName { get; set; } = string.Empty;

        /// <summary>
        /// 角色编码
        /// </summary>
        public string RoleCode { get; set; } = string.Empty;

        /// <summary>
        /// 角色描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 下级查询DTO，用于查询下级用户时的条件参数
    /// </summary>
    public class SubordinateQueryDto
    {
        /// <summary>
        /// 开始时间，用于统计数据的时间范围过滤
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间，用于统计数据的时间范围过滤
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 页码，默认为1
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 每页大小，默认为20
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 用户名（模糊查询）
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 真实姓名（模糊查询）
        /// </summary>
        public string? RealName { get; set; }

        /// <summary>
        /// 用户状态过滤，0:禁用 1:启用
        /// </summary>
        public byte? Status { get; set; }
    }

    /// <summary>
    /// 系统用户统计汇总DTO
    /// </summary>
    public class SysUserStatisticsSummaryDto
    {
        /// <summary>
        /// 总观看视频数量
        /// </summary>
        public int TotalViewCount { get; set; }

        /// <summary>
        /// 总答题数量
        /// </summary>
        public int TotalAnswerCount { get; set; }

        /// <summary>
        /// 总奖励金额
        /// </summary>
        public decimal TotalRewardAmount { get; set; }

        /// <summary>
        /// 完播视频数量
        /// </summary>
        public int CompleteViewCount { get; set; }

        /// <summary>
        /// 正确答题数量
        /// </summary>
        public int CorrectAnswerCount { get; set; }

        /// <summary>
        /// 奖励领取次数
        /// </summary>
        public int RewardClaimCount { get; set; }

        /// <summary>
        /// 完播率（百分比）
        /// </summary>
        public decimal CompleteRate { get; set; }

        /// <summary>
        /// 答题正确率（百分比）
        /// </summary>
        public decimal CorrectRate { get; set; }

        /// <summary>
        /// 统计时间范围开始
        /// </summary>
        public DateTime? StatisticsStartTime { get; set; }

        /// <summary>
        /// 统计时间范围结束
        /// </summary>
        public DateTime? StatisticsEndTime { get; set; }
    }

    /// <summary>
    /// 带统计数据的系统用户DTO
    /// </summary>
    public class SysUserWithStatisticsDto : SysUserDto
    {
        /// <summary>
        /// 用户类型，1:超级管理员 2:管理员 3:员工
        /// </summary>
        public byte UserType { get; set; }

        /// <summary>
        /// 上级用户ID
        /// </summary>
        public string? ParentUserId { get; set; }

        /// <summary>
        /// 上级用户姓名
        /// </summary>
        public string? ParentUserName { get; set; }

        /// <summary>
        /// 直接下级数量
        /// </summary>
        public int DirectSubordinateCount { get; set; }

        /// <summary>
        /// 所有下级用户数量（包括间接下级）
        /// </summary>
        public int TotalSubordinateUserCount { get; set; }

        /// <summary>
        /// 下级用户统计数据汇总
        /// </summary>
        public SysUserStatisticsSummaryDto Statistics { get; set; } = new();

        /// <summary>
        /// 用户类型名称
        /// </summary>
        public string UserTypeName => UserType switch
        {
            1 => "超级管理员",
            2 => "管理员",
            3 => "员工",
            _ => "未知"
        };
    }
}