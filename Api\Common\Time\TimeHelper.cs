﻿namespace Common.Time
{
    /// <summary>
    /// 时间工具类
    /// </summary>
    public class TimeHelper
    {

        #region 获取当前时间戳

        #region UTC
        /// <summary> 
        /// 获取时间戳   单位毫秒
        /// </summary> 
        /// <returns>UTC</returns> 
        public static long GetNowTimeStampMSUTC()
        {
            TimeSpan ts = DateTime.Now - new DateTime(1970, 1, 1, 0, 0, 0, 0);
            return Convert.ToInt64(ts.TotalMilliseconds);
        }
        /// <summary>
        /// 获取时间戳   单位秒
        /// </summary>
        /// <returns></returns>
        public static long GetNowTimeStampUTC()
        {
            TimeSpan ts = DateTime.Now - new DateTime(1970, 1, 1, 0, 0, 0);
            return Convert.ToInt64(ts.TotalSeconds);
        }
        #endregion

        /// <summary>
        ///  获取时间戳   单位秒
        /// </summary>
        /// <returns></returns>
        public static long GetNowTimeStamp()
        {

            TimeSpan ts = DateTime.Now - new DateTime(1970, 1, 1, 8, 0, 0);
            return Convert.ToInt64(ts.TotalSeconds);
        }

        /// <summary> 
        /// 获取时间戳   单位毫秒
        /// </summary> 
        /// <returns>UTC</returns> 
        public static long GetNowTimeStampMS()
        {
            TimeSpan ts = DateTime.Now - new DateTime(1970, 1, 1, 8, 0, 0, 0);
            return Convert.ToInt64(ts.TotalMilliseconds);
        }

        #endregion

        #region 时间转换时间戳

        /// <summary>
        ///  获取时间戳   单位秒
        /// </summary>
        /// <returns></returns>
        public static long GetTimeStamp(DateTime dt)
        {

            //System.DateTime startTime = TimeZoneInfo.ConvertTime(new DateTime(1970, 1, 1), TimeZoneInfo.FindSystemTimeZoneById("China Standard Time"));
            //long t = (dt.Ticks - startTime.Ticks) / 10000;   //除10000调整为13位      


            TimeSpan ts = dt - new DateTime(1970, 1, 1, 8, 0, 0, 0);
            return Convert.ToInt64(ts.TotalSeconds);
        }

        /// <summary> 
        /// 获取时间戳   单位毫秒
        /// </summary> 
        /// <returns>UTC</returns> 
        public static long GetTimeStampMS(DateTime dt)
        {
            TimeSpan ts = dt - new DateTime(1970, 1, 1, 8, 0, 0, 0);
            return Convert.ToInt64(ts.TotalMilliseconds);
        }

        #endregion


        /// <summary>
        /// 转换时间戳为C#时间 单位：毫秒
        /// </summary>
        /// <param name="timeStamp">时间戳 </param>
        /// <returns>C#时间</returns>
        public static DateTime ConvertTimeStampMSToDateTime(long timeStamp)
        {
            DateTime startTime = new(1970, 1, 1, 8, 0, 0, 0); // 当地时区
            DateTime dt = startTime.AddMilliseconds(timeStamp);
            return dt;
        }

        /// <summary>
        /// 转换时间戳为C#时间 单位：秒
        /// </summary>
        /// <param name="timeStamp">时间戳 </param>
        /// <returns>C#时间</returns>
        public static DateTime ConvertTimeStampToDateTime(long timeStamp)
        {
            DateTime startTime = new(1970, 1, 1, 8, 0, 0, 0);// 当地时区
            DateTime dt = startTime.AddSeconds(timeStamp);
            return dt;
        }


    }
}
