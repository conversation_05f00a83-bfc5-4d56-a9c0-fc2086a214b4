using Common.Exceptions;
using Common.JWT;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using Newtonsoft.Json;
using ServiceVideoSharing.Controllers.Attributes;
using System.Security.Claims;

namespace ServiceVideoSharing.Controllers.Middleware
{
    /// <summary>
    /// 权限中间件
    /// 用于验证JWT Token的正确性，确保只有持有有效Token的用户能访问受保护的API端点
    /// </summary>
    /// <param name="next">请求处理管道中的下一个中间件</param>
    public class PermissionMiddleware(RequestDelegate next)
    {
        /// <summary>
        /// 请求处理管道中的下一个中间件
        /// </summary>
        private readonly RequestDelegate _next = next;

        /// <summary>
        /// 中间件处理方法，验证JWT Token并处理请求
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>异步任务</returns>
        public async Task InvokeAsync(HttpContext context)
        {
            // 获取当前请求的终结点
            var endpoint = context.GetEndpoint();

            // 如果终结点为空或允许匿名访问，则直接放行
            if (endpoint == null || IsAnonymousEndpoint(endpoint))
            {
                await _next(context);
                return;
            }

            // 检查是否需要权限验证
            if (HasPermissionAttribute(endpoint))
            {
                // 验证Token
                if (!await ValidateTokenAsync(context))
                    return;
            }

            await _next(context);
        }



        #region Token验证

        /// <summary>
        /// 验证JWT Token
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>验证是否成功</returns>
        private static async Task<bool> ValidateTokenAsync(HttpContext context)
        {
            try
            {
                // 从请求中获取JWT令牌
                var token = JWTHelper.GetToken(context);
                if (string.IsNullOrEmpty(token))
                {
                    await HandleUnauthorized(context, "未找到有效的Token");
                    return false;
                }

                // 从令牌中解析用户信息，这里会自动验证Token的正确性
                var userInfo = JWTHelper.GetUserInfo(token);
                if (userInfo == null)
                {
                    await HandleUnauthorized(context, "Token验证失败");
                    return false;
                }

                // 设置用户身份信息到HttpContext中
                context.User = CreateClaimsPrincipal(userInfo);

                return true;
            }
            catch (AuthorizationException ex)
            {
                await HandleUnauthorized(context, ex.Message);
                return false;
            }
            catch (Exception)
            {
                await HandleUnauthorized(context, "Token验证失败");
                return false;
            }
        }

        /// <summary>
        /// 检查终结点是否允许匿名访问
        /// </summary>
        /// <param name="endpoint">HTTP终结点</param>
        /// <returns>如果允许匿名访问则返回true，否则返回false</returns>
        private static bool IsAnonymousEndpoint(Endpoint endpoint) =>
            endpoint.Metadata.GetMetadata<AllowAnonymousAttribute>() != null;

        /// <summary>
        /// 检查终结点是否需要权限验证
        /// </summary>
        /// <param name="endpoint">HTTP终结点</param>
        /// <returns>如果需要权限验证则返回true，否则返回false</returns>
        private static bool HasPermissionAttribute(Endpoint endpoint)
        {
            // 获取控制器动作描述符
            var actionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();
            if (actionDescriptor == null) return false;

            // 获取方法和控制器上的所有特性
            var methodAttributes = actionDescriptor.MethodInfo.GetCustomAttributes(true);
            var controllerAttributes = actionDescriptor.ControllerTypeInfo.GetCustomAttributes(true);

            // 检查方法或控制器上是否有权限特性
            return methodAttributes.Any(a => a is PermissionAttribute) ||
                   controllerAttributes.Any(a => a is PermissionAttribute);
        }

        /// <summary>
        /// 创建用户身份主体
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        /// <returns>用户身份主体</returns>
        private static ClaimsPrincipal CreateClaimsPrincipal(UserInfo userInfo)
        => new(new ClaimsIdentity(
            [
                new(ClaimTypes.NameIdentifier, userInfo.UserId),
                new(ClaimTypes.Name, userInfo.UserName)
            ], "Bearer"));

        #endregion

        /// <summary>
        /// 处理未授权响应（401）
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="message">错误消息</param>
        /// <returns>异步任务</returns>
        private static async Task<bool> HandleUnauthorized(HttpContext context, string message)
            => await WriteErrorResponse(context, 401, message);

        /// <summary>
        /// 写入HTTP响应
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="code">业务状态码</param>
        /// <param name="message">响应消息</param>
        /// <param name="statusCode">HTTP状态码</param>
        /// <returns>异步任务</returns>
        private static async Task<bool> WriteErrorResponse(HttpContext context, int code, string message, int statusCode = 200)
        {
            // 设置响应状态码和内容类型
            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";
            // 将结果序列化为JSON并写入响应
            await context.Response.WriteAsync(JsonConvert.SerializeObject(new { code, success = false, msg = message }));

            return false;
        }
    }

    /// <summary>
    /// 权限中间件扩展方法
    /// 提供将权限中间件添加到应用程序请求管道的扩展方法
    /// </summary>
    public static class PermissionMiddlewareExtensions
    {
        /// <summary>
        /// 向应用程序请求管道添加权限验证中间件
        /// </summary>
        /// <param name="builder">应用程序构建器</param>
        /// <returns>应用程序构建器</returns>
        public static IApplicationBuilder UsePermissionValidation(this IApplicationBuilder builder) =>
            builder.UseMiddleware<PermissionMiddleware>();
    }
}
