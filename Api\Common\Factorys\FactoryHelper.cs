﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Reflection;

namespace Common.Factorys
{
    public class FactoryHelper
    {


        public static T CreateHelper<T>(string DllName, string TypeName)
        {

            Assembly assembly = Assembly.Load(DllName);
            Type? type = assembly.GetType(TypeName) ?? throw new Exception($"需要反射生成的类：{TypeName} 不存在");
            try
            {
                var oClass = Activator.CreateInstance(type, true);
                return oClass == null ? throw new Exception($"需要反射生成的类：{TypeName} 不存在") : (T)oClass;
            }
            catch (Exception ex)
            {
                throw new Exception($"生成类：{TypeName} 失败 错误信息：{ex.Message}");
            }

        }

        /// <summary>
        /// 将对象的属性值拷贝到另一个对象
        /// </summary>
        /// <typeparam name="T1"></typeparam>
        /// <typeparam name="T2"></typeparam>
        /// <param name="t1"></param>
        /// <param name="t2"></param>
        public static void CopyToProperty<T1, T2>(T1 t1, T2 t2)
        {
            Type type = typeof(T1);
            Type type1 = typeof(T2);
            foreach (var item in type1.GetProperties())
            {

                if (item.IsDefined(typeof(ColumnAttribute), true))
                {
                    item.GetCustomAttribute(typeof(ColumnAttribute), true);
                }

                string name = item.Name;
                if (item.CustomAttributes.Any())
                {
                    foreach (var attribute in item.CustomAttributes)
                    {
                        if (attribute.AttributeType == typeof(ColumnAttribute))
                        {

                            if (attribute.ConstructorArguments.Count > 0)
                            {
                                name = attribute.ConstructorArguments[0].ToString().Replace("\"", string.Empty);
                            }
                        }
                    }

                }
                var property = type.GetProperty(name);
                if (property == null) continue;

                if (property.PropertyType.Name != item.PropertyType.Name) continue;

                var value = property.GetValue(t1);
                item.SetValue(t2, value);
            }
        }
    }
}
