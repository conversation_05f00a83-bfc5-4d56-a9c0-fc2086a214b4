using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.VideoEntity
{
    /// <summary>
    /// 用户批次记录表 - 整合观看、答题、红包记录
    /// 一个用户在一个批次中只有一条记录，记录完整的用户行为数据
    /// </summary>
    [Table("user_batch_records")]
    [Index(nameof(UserId), nameof(BatchId), IsUnique = true, Name = "IX_UserBatchRecord_UserId_BatchId")]
    public class UserBatchRecord : BaseEntity_ID
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [MaxLength(50)]
        [Comment("用户ID")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 批次ID
        /// </summary>
        [Comment("批次ID")]
        public int BatchId { get; set; }

        #region 观看相关字段

        /// <summary>
        /// 观看时长(秒)
        /// </summary>
        [Comment("观看时长(秒)")]
        public int ViewDuration { get; set; } = 0;

        /// <summary>
        /// 观看进度(0-1之间的小数)
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        [Comment("观看进度(0-1之间的小数)")]
        public decimal WatchProgress { get; set; } = 0;

        /// <summary>
        /// 是否完播
        /// </summary>
        [Comment("是否完播")]
        public bool IsCompleted { get; set; } = false;

        /// <summary>
        /// 开始观看时间
        /// </summary>
        [Comment("开始观看时间")]
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 最后观看时间
        /// </summary>
        [Comment("最后观看时间")]
        public DateTime? LastWatchTime { get; set; }

        #endregion

        #region 答题相关字段

        /// <summary>
        /// 总题目数
        /// </summary>
        [Comment("总题目数")]
        public int TotalQuestions { get; set; } = 0;

        /// <summary>
        /// 正确答案数
        /// </summary>
        [Comment("正确答案数")]
        public int CorrectAnswers { get; set; } = 0;

        /// <summary>
        /// 答题详情(JSON格式)
        /// 存储完整的答题信息，包括题目、选项、是否正确等
        /// </summary>
        [Comment("答题详情(JSON格式)")]
        public string? AnswerDetails { get; set; }

        /// <summary>
        /// 答题完成时间
        /// </summary>
        [Comment("答题完成时间")]
        public DateTime? AnswerTime { get; set; }

        #endregion

        #region 红包相关字段

        /// <summary>
        /// 红包金额
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        [Comment("红包金额")]
        public decimal RewardAmount { get; set; } = 0.00m;

        /// <summary>
        /// 红包状态:0未发放,1发放成功,2发放失败
        /// </summary>
        [Comment("红包状态:0未发放,1发放成功,2发放失败")]
        public byte RewardStatus { get; set; } = 0;

        /// <summary>
        /// 红包发放时间
        /// </summary>
        [Comment("红包发放时间")]
        public DateTime? RewardTime { get; set; }

        /// <summary>
        /// 微信支付交易号
        /// </summary>
        [MaxLength(100)]
        [Comment("微信支付交易号")]
        public string? TransactionId { get; set; }

        /// <summary>
        /// 微信支付订单号
        /// </summary>
        [MaxLength(100)]
        [Comment("微信支付订单号")]
        public string? OutTradeNo { get; set; }

        /// <summary>
        /// 红包发放失败原因
        /// </summary>
        [MaxLength(255)]
        [Comment("红包发放失败原因")]
        public string? RewardFailReason { get; set; }

        /// <summary>
        /// 红包发放重试次数
        /// </summary>
        [Comment("红包发放重试次数")]
        public int RewardRetryCount { get; set; } = 0;

        #endregion

        #region 推广相关字段

        /// <summary>
        /// 推广链接
        /// </summary>
        [MaxLength(500)]
        [Comment("推广链接")]
        public string? PromotionLink { get; set; }

        #endregion

        #region 计算属性

        /// <summary>
        /// 答题正确率(百分比)
        /// </summary>
        [NotMapped]
        public decimal CorrectRate => TotalQuestions > 0 ? Math.Round((decimal)CorrectAnswers / TotalQuestions * 100, 2) : 0;

        /// <summary>
        /// 观看进度百分比
        /// </summary>
        [NotMapped]
        public decimal WatchProgressPercent => Math.Round(WatchProgress * 100, 2);

        /// <summary>
        /// 是否已答题
        /// </summary>
        [NotMapped]
        public bool HasAnswered => TotalQuestions > 0;

        /// <summary>
        /// 是否已获得红包
        /// </summary>
        [NotMapped]
        public bool HasReward => RewardAmount > 0 && RewardStatus == 1;

        #endregion
    }
}
