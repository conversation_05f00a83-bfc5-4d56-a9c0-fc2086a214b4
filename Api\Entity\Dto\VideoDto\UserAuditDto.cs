using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.VideoDto
{
    /// <summary>
    /// 用户审核创建DTO
    /// </summary>
    public class UserAuditCreateDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public int UserId { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        public int? BatchId { get; set; }

        /// <summary>
        /// 推广链接ID
        /// </summary>
        public int? PromotionLinkId { get; set; }
    }

    /// <summary>
    /// 用户审核DTO - 简化版，员工审核用户
    /// </summary>
    public class UserAuditDto
    {
        /// <summary>
        /// 审核状态:1通过,2拒绝
        /// </summary>
        [Required(ErrorMessage = "审核状态不能为空")]
        [Range(1, 2, ErrorMessage = "审核状态必须为1或2")]
        public byte Status { get; set; }

        /// <summary>
        /// 审核备注
        /// </summary>
        [MaxLength(255, ErrorMessage = "审核备注长度不能超过255个字符")]
        public string? Remark { get; set; }
    }



    /// <summary>
    /// 用户审核查询DTO
    /// </summary>
    public class UserAuditQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 审核状态:0待审核,1通过,2拒绝
        /// </summary>
        public byte? AuditStatus { get; set; }

        /// <summary>
        /// 审核人ID（员工ID）
        /// </summary>
        public string? AuditorId { get; set; }

        /// <summary>
        /// 批次ID
        /// </summary>
        public string? BatchId { get; set; }

        /// <summary>
        /// 推广链接ID
        /// </summary>
        public string? PromotionLinkId { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

    }

    /// <summary>
    /// 用户审核响应DTO
    /// </summary>
    public class UserAuditResponseDto
    {
        /// <summary>
        /// 审核ID
        /// </summary>
        public string? Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string? UserNickname { get; set; }

        /// <summary>
        /// 审核人ID（员工ID）
        /// </summary>
        public string? AuditorId { get; set; }

        /// <summary>
        /// 审核人姓名
        /// </summary>
        public string? AuditorName { get; set; }

        /// <summary>
        /// 审核状态:0待审核,1通过,2拒绝
        /// </summary>
        public byte AuditStatus { get; set; }

        /// <summary>
        /// 审核状态名称
        /// </summary>
        public string AuditStatusName { get; set; } = string.Empty;

        /// <summary>
        /// 审核备注
        /// </summary>
        public string? AuditRemark { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? AuditTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 用户审核统计DTO
    /// </summary>
    public class UserAuditStatisticsDto
    {
        /// <summary>
        /// 总审核数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 待审核数
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// 已通过数
        /// </summary>
        public int ApprovedCount { get; set; }

        /// <summary>
        /// 已拒绝数
        /// </summary>
        public int RejectedCount { get; set; }

        /// <summary>
        /// 通过率
        /// </summary>
        public decimal ApprovalRate { get; set; }

        /// <summary>
        /// 今日审核数
        /// </summary>
        public int TodayCount { get; set; }

        /// <summary>
        /// 审核类型统计
        /// </summary>
        public Dictionary<string, int> TypeStatistics { get; set; } = [];
    }
}
