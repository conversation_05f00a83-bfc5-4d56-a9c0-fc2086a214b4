using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.VideoDto
{
    /// <summary>
    /// 用户每日统计DTO
    /// </summary>
    public class UserDailyStatisticsDto
    {
        /// <summary>
        /// 统计ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 统计日期
        /// </summary>
        public DateTime StatDate { get; set; }

        /// <summary>
        /// 负责员工ID
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// 负责员工姓名
        /// </summary>
        public string? EmployeeName { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 观看次数
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// 完播次数
        /// </summary>
        public int CompleteViewCount { get; set; }

        /// <summary>
        /// 总观看时长（秒）
        /// </summary>
        public int TotalViewDuration { get; set; }

        /// <summary>
        /// 答题次数
        /// </summary>
        public int AnswerCount { get; set; }

        /// <summary>
        /// 正确答题次数
        /// </summary>
        public int CorrectAnswerCount { get; set; }

        /// <summary>
        /// 获得红包次数
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// 获得红包总金额（分）
        /// </summary>
        public long RewardAmount { get; set; }

        /// <summary>
        /// 分享次数
        /// </summary>
        public int ShareCount { get; set; }

        /// <summary>
        /// 点赞次数
        /// </summary>
        public int LikeCount { get; set; }

        /// <summary>
        /// 评论次数
        /// </summary>
        public int CommentCount { get; set; }

        /// <summary>
        /// 下载次数
        /// </summary>
        public int DownloadCount { get; set; }

        /// <summary>
        /// 完播率（百分比）
        /// </summary>
        public decimal CompleteRate => ViewCount > 0 ? Math.Round((decimal)CompleteViewCount / ViewCount * 100, 2) : 0;

        /// <summary>
        /// 答题正确率（百分比）
        /// </summary>
        public decimal CorrectRate => AnswerCount > 0 ? Math.Round((decimal)CorrectAnswerCount / AnswerCount * 100, 2) : 0;

        /// <summary>
        /// 平均观看时长（秒）
        /// </summary>
        public decimal AvgViewDuration => ViewCount > 0 ? Math.Round((decimal)TotalViewDuration / ViewCount, 2) : 0;

        /// <summary>
        /// 红包金额（元）
        /// </summary>
        public decimal RewardAmountYuan => RewardAmount / 100m;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }
    }

    /// <summary>
    /// 用户每日统计查询DTO
    /// </summary>
    public class UserDailyStatisticsQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 用户ID（可选）
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 员工ID（可选，用于权限过滤）
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 用户ID列表（批量查询）
        /// </summary>
        public List<string>? UserIds { get; set; }
    }

    /// <summary>
    /// 用户每日统计创建DTO
    /// </summary>
    public class UserDailyStatisticsCreateDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 统计日期
        /// </summary>
        [Required(ErrorMessage = "统计日期不能为空")]
        public DateTime StatDate { get; set; }

        /// <summary>
        /// 负责员工ID
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// 观看次数
        /// </summary>
        public int ViewCount { get; set; } = 0;

        /// <summary>
        /// 完播次数
        /// </summary>
        public int CompleteViewCount { get; set; } = 0;

        /// <summary>
        /// 总观看时长（秒）
        /// </summary>
        public int TotalViewDuration { get; set; } = 0;

        /// <summary>
        /// 答题次数
        /// </summary>
        public int AnswerCount { get; set; } = 0;

        /// <summary>
        /// 正确答题次数
        /// </summary>
        public int CorrectAnswerCount { get; set; } = 0;

        /// <summary>
        /// 获得红包次数
        /// </summary>
        public int RewardCount { get; set; } = 0;

        /// <summary>
        /// 获得红包总金额（分）
        /// </summary>
        public long RewardAmount { get; set; } = 0;

        /// <summary>
        /// 分享次数
        /// </summary>
        public int ShareCount { get; set; } = 0;

        /// <summary>
        /// 点赞次数
        /// </summary>
        public int LikeCount { get; set; } = 0;

        /// <summary>
        /// 评论次数
        /// </summary>
        public int CommentCount { get; set; } = 0;

        /// <summary>
        /// 下载次数
        /// </summary>
        public int DownloadCount { get; set; } = 0;
    }

    /// <summary>
    /// 用户每日统计更新DTO
    /// </summary>
    public class UserDailyStatisticsUpdateDto
    {
        /// <summary>
        /// 统计ID
        /// </summary>
        [Required(ErrorMessage = "统计ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 观看次数增量
        /// </summary>
        public int ViewCountIncrement { get; set; } = 0;

        /// <summary>
        /// 完播次数增量
        /// </summary>
        public int CompleteViewCountIncrement { get; set; } = 0;

        /// <summary>
        /// 观看时长增量（秒）
        /// </summary>
        public int ViewDurationIncrement { get; set; } = 0;

        /// <summary>
        /// 答题次数增量
        /// </summary>
        public int AnswerCountIncrement { get; set; } = 0;

        /// <summary>
        /// 正确答题次数增量
        /// </summary>
        public int CorrectAnswerCountIncrement { get; set; } = 0;

        /// <summary>
        /// 红包次数增量
        /// </summary>
        public int RewardCountIncrement { get; set; } = 0;

        /// <summary>
        /// 红包金额增量（分）
        /// </summary>
        public long RewardAmountIncrement { get; set; } = 0;

        /// <summary>
        /// 分享次数增量
        /// </summary>
        public int ShareCountIncrement { get; set; } = 0;

        /// <summary>
        /// 点赞次数增量
        /// </summary>
        public int LikeCountIncrement { get; set; } = 0;

        /// <summary>
        /// 评论次数增量
        /// </summary>
        public int CommentCountIncrement { get; set; } = 0;

        /// <summary>
        /// 下载次数增量
        /// </summary>
        public int DownloadCountIncrement { get; set; } = 0;
    }

    /// <summary>
    /// 用户统计汇总DTO
    /// </summary>
    public class UserStatisticsSummaryDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 负责员工ID
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// 负责员工姓名
        /// </summary>
        public string? EmployeeName { get; set; }

        /// <summary>
        /// 统计开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 统计结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 总观看次数
        /// </summary>
        public int TotalViewCount { get; set; }

        /// <summary>
        /// 总完播次数
        /// </summary>
        public int TotalCompleteViewCount { get; set; }

        /// <summary>
        /// 总观看时长（秒）
        /// </summary>
        public int TotalViewDuration { get; set; }

        /// <summary>
        /// 总答题次数
        /// </summary>
        public int TotalAnswerCount { get; set; }

        /// <summary>
        /// 总正确答题次数
        /// </summary>
        public int TotalCorrectAnswerCount { get; set; }

        /// <summary>
        /// 总红包次数
        /// </summary>
        public int TotalRewardCount { get; set; }

        /// <summary>
        /// 总红包金额（分）
        /// </summary>
        public long TotalRewardAmount { get; set; }

        /// <summary>
        /// 总分享次数
        /// </summary>
        public int TotalShareCount { get; set; }

        /// <summary>
        /// 总点赞次数
        /// </summary>
        public int TotalLikeCount { get; set; }

        /// <summary>
        /// 总评论次数
        /// </summary>
        public int TotalCommentCount { get; set; }

        /// <summary>
        /// 总下载次数
        /// </summary>
        public int TotalDownloadCount { get; set; }

        /// <summary>
        /// 平均完播率（百分比）
        /// </summary>
        public decimal AvgCompleteRate { get; set; }

        /// <summary>
        /// 平均答题正确率（百分比）
        /// </summary>
        public decimal AvgCorrectRate { get; set; }

        /// <summary>
        /// 平均观看时长（秒）
        /// </summary>
        public decimal AvgViewDuration { get; set; }

        /// <summary>
        /// 总红包金额（元）
        /// </summary>
        public decimal TotalRewardAmountYuan => TotalRewardAmount / 100m;

        /// <summary>
        /// 统计天数
        /// </summary>
        public int StatisticsDays { get; set; }
    }

    /// <summary>
    /// 每日统计趋势DTO
    /// </summary>
    public class DailyStatisticsTrendDto
    {
        /// <summary>
        /// 统计日期
        /// </summary>
        public DateTime StatDate { get; set; }

        /// <summary>
        /// 观看次数
        /// </summary>
        public int ViewCount { get; set; }

        /// <summary>
        /// 完播次数
        /// </summary>
        public int CompleteViewCount { get; set; }

        /// <summary>
        /// 答题次数
        /// </summary>
        public int AnswerCount { get; set; }

        /// <summary>
        /// 正确答题次数
        /// </summary>
        public int CorrectAnswerCount { get; set; }

        /// <summary>
        /// 红包次数
        /// </summary>
        public int RewardCount { get; set; }

        /// <summary>
        /// 红包金额（元）
        /// </summary>
        public decimal RewardAmountYuan { get; set; }

        /// <summary>
        /// 活跃用户数
        /// </summary>
        public int ActiveUserCount { get; set; }

        /// <summary>
        /// 完播率（百分比）
        /// </summary>
        public decimal CompleteRate => ViewCount > 0 ? Math.Round((decimal)CompleteViewCount / ViewCount * 100, 2) : 0;

        /// <summary>
        /// 答题正确率（百分比）
        /// </summary>
        public decimal CorrectRate => AnswerCount > 0 ? Math.Round((decimal)CorrectAnswerCount / AnswerCount * 100, 2) : 0;
    }

    /// <summary>
    /// 统计概览DTO（用于实时统计）
    /// </summary>
    public class StatisticsOverviewDto
    {
        /// <summary>
        /// 总观看次数
        /// </summary>
        public int TotalViewCount { get; set; }

        /// <summary>
        /// 总完播次数
        /// </summary>
        public int TotalCompleteViewCount { get; set; }

        /// <summary>
        /// 总答题次数
        /// </summary>
        public int TotalAnswerCount { get; set; }

        /// <summary>
        /// 总正确答题次数
        /// </summary>
        public int TotalCorrectAnswerCount { get; set; }

        /// <summary>
        /// 总红包次数
        /// </summary>
        public int TotalRewardCount { get; set; }

        /// <summary>
        /// 总红包金额（元）
        /// </summary>
        public decimal TotalRewardAmountYuan { get; set; }

        /// <summary>
        /// 平均完播率（百分比）
        /// </summary>
        public decimal AvgCompleteRate => TotalViewCount > 0 ? Math.Round((decimal)TotalCompleteViewCount / TotalViewCount * 100, 2) : 0;

        /// <summary>
        /// 平均答题正确率（百分比）
        /// </summary>
        public decimal AvgCorrectRate => TotalAnswerCount > 0 ? Math.Round((decimal)TotalCorrectAnswerCount / TotalAnswerCount * 100, 2) : 0;
    }

    /// <summary>
    /// 用户统计增量更新DTO
    /// 用于业务操作时实时更新统计数据
    /// </summary>
    public class UserStatisticsIncrementDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required(ErrorMessage = "用户ID不能为空")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 统计日期（默认为今天）
        /// </summary>
        public DateTime StatDate { get; set; } = DateTime.Today;

        /// <summary>
        /// 负责员工ID
        /// </summary>
        public int? EmployeeId { get; set; }

        /// <summary>
        /// 统计类型
        /// </summary>
        [Required(ErrorMessage = "统计类型不能为空")]
        public UserStatisticsType StatType { get; set; }

        /// <summary>
        /// 增量值
        /// </summary>
        public int IncrementValue { get; set; } = 1;

        /// <summary>
        /// 红包金额增量（分，仅当StatType为Reward时使用）
        /// </summary>
        public long RewardAmountIncrement { get; set; } = 0;

        /// <summary>
        /// 观看时长增量（秒，仅当StatType为View时使用）
        /// </summary>
        public int ViewDurationIncrement { get; set; } = 0;
    }

    /// <summary>
    /// 用户统计类型枚举
    /// </summary>
    public enum UserStatisticsType
    {
        /// <summary>
        /// 观看
        /// </summary>
        View = 1,

        /// <summary>
        /// 完播
        /// </summary>
        CompleteView = 2,

        /// <summary>
        /// 答题
        /// </summary>
        Answer = 3,

        /// <summary>
        /// 正确答题
        /// </summary>
        CorrectAnswer = 4,

        /// <summary>
        /// 红包
        /// </summary>
        Reward = 5,

        /// <summary>
        /// 分享
        /// </summary>
        Share = 6,

        /// <summary>
        /// 点赞
        /// </summary>
        Like = 7,

        /// <summary>
        /// 评论
        /// </summary>
        Comment = 8,

        /// <summary>
        /// 下载
        /// </summary>
        Download = 9
    }
}
