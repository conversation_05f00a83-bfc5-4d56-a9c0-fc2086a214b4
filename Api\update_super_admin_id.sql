-- 更新超管ID的SQL脚本
-- 将所有 'super_admin_001' 更新为 'super_admin'

-- 1. 更新批次表中的超管ID
UPDATE `servicevideosharing`.`batches` 
SET `CreatedBy` = 'super_admin', `UpdatedBy` = 'super_admin' 
WHERE `CreatedBy` = 'super_admin_001' OR `UpdatedBy` = 'super_admin_001';

-- 2. 更新视频表中的超管ID（如果存在）
UPDATE `servicevideosharing`.`videos` 
SET `CreatedBy` = 'super_admin', `UpdatedBy` = 'super_admin' 
WHERE `CreatedBy` = 'super_admin_001' OR `UpdatedBy` = 'super_admin_001';

-- 3. 更新系统用户表中的超管ID
UPDATE `servicevideosharing`.`sysusers` 
SET `UserId` = 'super_admin' 
WHERE `UserId` = 'super_admin_001';

-- 4. 更新系统用户表中引用超管的父级ID
UPDATE `servicevideosharing`.`sysusers` 
SET `ParentUserId` = 'super_admin' 
WHERE `ParentUserId` = 'super_admin_001';

-- 5. 更新用户表中的员工ID（如果有超管作为员工ID的情况）
UPDATE `servicevideosharing`.`users` 
SET `EmployeeId` = 'super_admin' 
WHERE `EmployeeId` = 'super_admin_001';

-- 6. 更新用户批次记录表中的超管ID（如果存在）
UPDATE `servicevideosharing`.`userbatchrecords` 
SET `CreatedBy` = 'super_admin', `UpdatedBy` = 'super_admin' 
WHERE `CreatedBy` = 'super_admin_001' OR `UpdatedBy` = 'super_admin_001';

-- 7. 更新用户转移记录表中的超管ID（如果存在）
UPDATE `servicevideosharing`.`usertransfers` 
SET `FromEmployeeId` = 'super_admin' 
WHERE `FromEmployeeId` = 'super_admin_001';

UPDATE `servicevideosharing`.`usertransfers` 
SET `ToEmployeeId` = 'super_admin' 
WHERE `ToEmployeeId` = 'super_admin_001';

-- 8. 更新用户审核记录表中的审核员ID（如果存在）
UPDATE `servicevideosharing`.`useraudits` 
SET `AuditorId` = 'super_admin' 
WHERE `AuditorId` = 'super_admin_001';

-- 查询验证更新结果
SELECT '批次表更新结果' as 表名, COUNT(*) as 超管创建的批次数量 
FROM `servicevideosharing`.`batches` 
WHERE `CreatedBy` = 'super_admin';

SELECT '系统用户表更新结果' as 表名, COUNT(*) as 超管用户数量 
FROM `servicevideosharing`.`sysusers` 
WHERE `UserId` = 'super_admin';
