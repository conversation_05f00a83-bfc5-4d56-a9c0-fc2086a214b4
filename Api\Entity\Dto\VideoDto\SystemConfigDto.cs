using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.VideoDto
{
    /// <summary>
    /// 系统配置创建DTO
    /// </summary>
    public class SystemConfigCreateDto
    {
        /// <summary>
        /// 配置键
        /// </summary>
        [Required(ErrorMessage = "配置键不能为空")]
        [MaxLength(100, ErrorMessage = "配置键长度不能超过100个字符")]
        public string ConfigKey { get; set; } = string.Empty;

        /// <summary>
        /// 配置值
        /// </summary>
        public string? ConfigValue { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        [MaxLength(255, ErrorMessage = "配置描述长度不能超过255个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 配置分组
        /// </summary>
        [MaxLength(50, ErrorMessage = "配置分组长度不能超过50个字符")]
        public string? GroupName { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        [MaxLength(50, ErrorMessage = "配置类型长度不能超过50个字符")]
        public string? ConfigType { get; set; }

        /// <summary>
        /// 是否启用:0禁用,1启用
        /// </summary>
        public byte IsEnabled { get; set; } = 1;
    }

    /// <summary>
    /// 系统配置更新DTO
    /// </summary>
    public class SystemConfigUpdateDto
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        [Required(ErrorMessage = "配置ID不能为空")]
        public int Id { get; set; }

        /// <summary>
        /// 配置键
        /// </summary>
        [Required(ErrorMessage = "配置键不能为空")]
        [MaxLength(100, ErrorMessage = "配置键长度不能超过100个字符")]
        public string ConfigKey { get; set; } = string.Empty;

        /// <summary>
        /// 配置值
        /// </summary>
        public string? ConfigValue { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        [MaxLength(50, ErrorMessage = "配置类型长度不能超过50个字符")]
        public string? ConfigType { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        [MaxLength(255, ErrorMessage = "配置描述长度不能超过255个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 是否启用:0禁用,1启用
        /// </summary>
        public byte IsEnabled { get; set; } = 1;
    }

    /// <summary>
    /// 系统配置查询DTO
    /// </summary>
    public class SystemConfigQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 配置键
        /// </summary>
        public string? ConfigKey { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        public string? ConfigType { get; set; }

        /// <summary>
        /// 配置分组
        /// </summary>
        public string? GroupName { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public byte? IsEnabled { get; set; }
    }

    /// <summary>
    /// 系统配置响应DTO
    /// </summary>
    public class SystemConfigResponseDto
    {
        /// <summary>
        /// 配置ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 配置键
        /// </summary>
        public string ConfigKey { get; set; } = string.Empty;

        /// <summary>
        /// 配置值
        /// </summary>
        public string? ConfigValue { get; set; }

        /// <summary>
        /// 配置描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 配置分组
        /// </summary>
        public string? GroupName { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        public string? ConfigType { get; set; }

        /// <summary>
        /// 是否启用:0禁用,1启用
        /// </summary>
        public byte IsEnabled { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }
    }
}
