-- 更新视频状态，统一使用Status字段
-- 状态: 0=下架, 1=上架, 2=失败, 3=压缩中

-- 注意：此脚本需要在删除CompressionStatus字段之前执行

-- 1. 更新正在压缩的视频状态
-- 将CompressionStatus=1（压缩中）的视频状态更新为3（压缩中）
UPDATE Video
SET Status = 3
WHERE CompressionStatus = 1;

-- 2. 更新压缩失败的视频状态
-- 将CompressionStatus=3（压缩失败）的视频状态更新为2（失败）
UPDATE Video
SET Status = 2
WHERE CompressionStatus = 3;

-- 3. 记录更新日志（如果有ChangeLog表）
-- INSERT INTO ChangeLog (ChangeType, Description, AppliedDate)
-- VALUES ('Schema', '统一视频状态字段，删除CompressionStatus字段', GETDATE());

-- 执行完此脚本后，可以通过EF Core迁移删除CompressionStatus字段：
-- Add-Migration RemoveCompressionStatus
-- Update-Database