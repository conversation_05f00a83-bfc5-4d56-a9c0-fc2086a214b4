using BLL.SysService;
using Common.Autofac;
using Common.Https;
using Entity.Dto;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using ServiceVideoSharing.Controllers.Attributes;
using System.Diagnostics;
using System.Security.Claims;
using System.Text.Json;

namespace ServiceVideoSharing.Controllers.Filter
{
    /// <summary>
    /// 日志操作过滤器
    /// 用于处理带有LogAttribute特性的Action
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class LogActionFilter(SysLogService logService) : IAsyncActionFilter
    {
        private readonly SysLogService _logService = logService;

        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            // 获取Action特性
            var logAttr = GetLogAttribute(context);
            if (logAttr == null)
            {
                await next();
                return;
            }

            var stopwatch = Stopwatch.StartNew();
            try
            {
                // 执行Action并记录结果
                var actionContext = await next();
                object? result = actionContext.Result;
                stopwatch.Stop();

                // 记录正常执行的日志
                await LogActionExecution(context, logAttr, null, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();

                // 记录异常日志
                await LogActionExecution(context, logAttr, ex, stopwatch.ElapsedMilliseconds);
                throw; // 重新抛出异常，让全局异常处理器处理
            }
        }

        /// <summary>
        /// 获取Action的Log特性
        /// </summary>
        private static LogAttribute? GetLogAttribute(ActionExecutingContext context)
        {
            var actionDescriptor = context.ActionDescriptor as ControllerActionDescriptor;
            return actionDescriptor?.MethodInfo
                .GetCustomAttributes(typeof(LogAttribute), false)
                .FirstOrDefault() as LogAttribute;
        }

        /// <summary>
        /// 记录Action执行日志
        /// </summary>
        private async Task LogActionExecution(
            ActionExecutingContext context,
            LogAttribute logAttr,
            Exception? error,
            long executionTime)
        {
            // 获取用户信息
            var userId = context.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
            var username = context.HttpContext.User.FindFirst(ClaimTypes.Name)?.Value ?? string.Empty;

            // 获取请求信息
            var path = $"{context.HttpContext.Request.Path}{context.HttpContext.Request.QueryString}";
            var ip = context.HttpContext.GetIPAddress();

            // 创建日志DTO
            var logDto = new CreateLogDto
            {
                UserId = userId,
                Username = username,
                Operation = logAttr.Module,
                Method = context.HttpContext.Request.Method,
                Params = logAttr.LogParams ? JsonSerializer.Serialize(context.ActionArguments) : null,
                Time = executionTime,
                Ip = ip,
                Path = path,
                LogType = "User",
                LogLevel = (error != null ? LogLevel.Error : logAttr.Level).ToString(),
                Message = logAttr.Description,
                Exception = error?.ToString()
            };


            // 记录日志
            await _logService.CreateAsync(logDto);
        }
    }
}