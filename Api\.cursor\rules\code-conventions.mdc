---
description: code-conventions
globs: 
alwaysApply: false
---
# 代码约定与最佳实践

## 命名规范
- **类名、方法名、公共成员**: 使用PascalCase (如 `UserService`, `GetById`)
- **局部变量、私有字段**: 使用camelCase (如 `userId`, `currentItem`)
- **常量**: 使用全大写 (如 `MAX_RETRY_COUNT`)
- **接口**: 以"I"为前缀 (如 `IUserService`)
- **特性/Attribute**: 以"Attribute"为后缀 (如 `AuthorizeAttribute`)

## 代码风格
- 使用C# 8.0+特性，包括记录类型、模式匹配等
- 优先使用表达式体成员和Lambda表达式
- 对于明显类型，使用`var`进行隐式类型推断
- 使用字符串插值而非字符串连接
- 使用空合并运算符 `??` 和空条件运算符 `?.`

## 异步编程
- I/O绑定操作使用异步方法
- 异步方法命名以"Async"为后缀
- 使用`Task`和`Task<T>`作为返回类型
- 避免使用`async void`（除特殊情况如事件处理器）
- 使用`await`而非直接访问`.Result`或`.Wait()`

## API设计
- 遵循RESTful API设计原则
- 使用属性路由配置端点
- 控制器继承自 [BaseController](mdc:DataMgrSystem/Controllers/BasisController/BaseController.cs)
- 使用标准HTTP状态码表示请求结果
- 使用统一的响应格式 [Result](mdc:DataMgrSystem/Controllers/BasisController/ResuItEntity/Result.cs)

## 依赖注入
- 构造函数注入服务依赖
- 遵循IoC原则，面向接口编程
- 避免服务定位器模式
- 适当使用生命周期（Transient/Scoped/Singleton）

## 错误处理
- 使用自定义异常类表示业务错误
- 全局异常处理中间件记录异常
- 使用适当的日志级别（Debug/Info/Warning/Error）
- 避免空异常处理（catch {} 块）

## 数据访问
- 使用EF Core进行数据访问
- 避免N+1查询问题
- 使用异步方法（例如 `FindAsync`, `ToListAsync`）
- 大数据集实现分页
- 避免在循环中执行数据库操作

