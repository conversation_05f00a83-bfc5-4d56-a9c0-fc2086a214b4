﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="File\**" />
    <EmbeddedResource Remove="File\**" />
    <None Remove="File\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AlibabaCloud.OpenApiClient" Version="0.1.13" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="log4net" Version="2.0.15" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="RabbitMQ.Client" Version="6.8.1" />
    <PackageReference Include="StackExchange.Redis" Version="2.7.17" />
  </ItemGroup>

</Project>
