using BLL.VideoService;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 用户审核控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class UserAuditController(UserAuditService userAuditService, UserService userService) : BaseController
    {
        private readonly UserAuditService _userAuditService = userAuditService;
        private readonly UserService _userService = userService;

        /// <summary>
        /// 员工审核用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="auditDto">审核信息</param>
        /// <returns>是否成功</returns>
        [HttpPost("audit-user/{userId}", Name = "UserAudit_AuditUser")]
        public async Task<Result<bool>> AuditUser(string userId, [FromBody] UserAuditDto auditDto)
        {
            // 获取当前登录的员工信息
            var currentUserId = GetCurrentUserId();

            // 员工审核用户，使用当前登录员工的ID作为审核员ID
            var result = await _userAuditService.AuditUserByEmployeeAsync(
                userId,
                currentUserId,
                auditDto.Status,
                auditDto.Remark,
                GetCurrentUserInfo());
            return Success(result, auditDto.Status == 1 ? "审核通过" : "审核拒绝");
        }

        /// <summary>
        /// 获取当前员工的待审核用户列表
        /// </summary>
        /// <returns>待审核用户列表</returns>
        [HttpGet("pending-users", Name = "UserAudit_GetPendingUsers")]
        public async Task<Result<List<UserResponseDto>>> GetPendingUsers()
        {
            // 获取当前登录的员工信息
            var currentUserId = GetCurrentUserId();

            // 只获取属于当前员工的待审核用户
            var result = await _userService.GetPendingAuditUsersByEmployeeAsync(currentUserId);
            return Success(result);
        }

    }
}
