using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.CardDto;

public class CardOpenApiBaseParasDto
{
    /// <summary>
    /// 签名
    /// </summary>
    public string Sign { get; set; } = string.Empty;

    /// <summary>
    /// 时间戳
    /// </summary>
    public long Tick { get; set; }

    /// <summary>
    /// 玩家Token
    /// </summary>
    public string PlayerToken { get; set; } = string.Empty;


    /// <summary>
    /// 玩家ID
    /// </summary>
    public string PlayerId { get; set; } = string.Empty;
}

/// <summary>
/// 获取卡片信息请求DTO
/// </summary>
public class GetCardInfoAsync_ReqDto : CardOpenApiBaseParasDto
{
}

/// <summary>
/// 查询指定用户的 转出卡和使用卡日志 100条请求DTO
/// </summary>
public class GetCardTransferRecordAsync_ReqDto : GetCardInfoAsync_ReqDto
{

}
/// <summary>
/// 使用卡片请求DTO
/// </summary>
public class UseTheCard_ReqDto : CardOpenApiBaseParasDto
{
    /// <summary>
    /// 俱乐部ID
    /// </summary>
    [Required]
    public string HallId { get; set; } = string.Empty;

    /// <summary>
    /// 游戏币
    /// </summary>
    [Required]
    public long GameCurrency { get; set; }



    /// <summary>
    /// 目标ID
    /// </summary>
    /// <value></value>
    [Required]
    public string TargetId { get; set; } = string.Empty;


    /// <summary>
    /// 使用数量   
    /// </summary>
    [Required]
    public int UseCount { get; set; }

}

/// <summary>
/// 卡片转让请求DTO
/// </summary>
public class TransferCard_ReqDto : CardOpenApiBaseParasDto
{

    /// <summary>
    /// 目标ID
    /// </summary>
    [Required]
    public string TargetId { get; set; } = string.Empty;

    /// <summary>
    /// 转让金额
    /// </summary>
    [Required]
    // [Range(1, long.MaxValue, ErrorMessage = "转让金额必须大于0")]
    public long GameCurrency { get; set; }


    /// <summary>
    /// 转让数量
    /// </summary>
    [Required]
    public int TransferCount { get; set; }
}