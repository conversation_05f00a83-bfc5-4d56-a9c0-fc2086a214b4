using BLL.SysService;
using Common.Autofac;
using Common.Exceptions;
using DAL.SysDAL;
using DAL.VideoDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Microsoft.Extensions.Logging;
using static DAL.SysDAL.SysUserDAL;

namespace BLL.VideoService
{
    /// <summary>
    /// 用户转移业务服务类 - 简化版，只处理员工绑定转移
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserTransferService(UserTransferDAL userTransferDAL, UserDAL userDAL, SysUserDAL sysUserDAL, SysLogService logService)
    {
        private readonly UserTransferDAL _userTransferDAL = userTransferDAL;
        private readonly UserDAL _userDAL = userDAL;
        private readonly SysUserDAL _sysUserDAL = sysUserDAL;
        private readonly SysLogService _logService = logService;

        /// <summary>
        /// 转移用户到新员工
        /// </summary>
        /// <param name="transferDto">用户转移DTO</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> TransferUsersAsync(UserTransferDto transferDto, CurrentUserInfoDto currentUserInfo)
        {
            // 验证新员工是否存在
            _ = await _sysUserDAL.GetFirstAsync(new UserDALQuery { UserId = transferDto.ToEmployeeId }) ?? throw new BusinessException("指定的目标员工不存在");

            // 获取要转移的用户信息
            var users = new List<User>();
            foreach (var userId in transferDto.UserIds)
            {
                var user = await _userDAL.GetByIdAsync(userId) ?? throw new BusinessException($"用户ID {userId} 不存在");
                users.Add(user);
            }

            // 创建转移记录
            var transfers = new List<UserTransfer>();
            foreach (var user in users)
            {
                var transfer = new UserTransfer
                {
                    UserId = user.Id,
                    FromEmployeeId = user.EmployeeId, // 原员工ID
                    ToEmployeeId = transferDto.ToEmployeeId,
                    Reason = transferDto.Reason,
                    OperatorId = currentUserInfo.UserId,
                    OperatorName = currentUserInfo.UserName,
                    TransferTime = DateTime.Now
                };
                transfers.Add(transfer);

                // 更新用户的员工绑定
                user.EmployeeId = transferDto.ToEmployeeId;
                await _userDAL.UpdateAsync(user);
            }

            // 批量添加转移记录
            foreach (var transfer in transfers)
            {
                await _userTransferDAL.AddAsync(transfer);
            }

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户转移",
                Operation = "转移用户到新员工",
                BusinessObject = "UserTransfer",
                ObjectId = string.Join(",", transferDto.UserIds),
                DetailedInfo = $"将 {transferDto.UserIds.Count} 个用户转移到员工 {transferDto.ToEmployeeId}",
                AfterData = transferDto,
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName,
                Level = LogLevel.Information
            });

            return true;
        }

        /// <summary>
        /// 分页查询转移记录列表
        /// </summary>
        /// <param name="queryDto">查询条件DTO</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<UserTransferResponseDto>> GetUserTransferPagedListAsync(UserTransferQueryDto queryDto)
        {
            var queryable = new UserTransferDAL.Queryable
            {
                UserId = queryDto.UserId,
                FromEmployeeId = queryDto.FromEmployeeId,
                ToEmployeeId = queryDto.ToEmployeeId,
                OperatorId = queryDto.OperatorId,
                OperatorName = queryDto.OperatorName,
                Reason = queryDto.Reason,
                StartTime = queryDto.StartTime,
                EndTime = queryDto.EndTime,
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            var result = await _userTransferDAL.GetPagedListAsync(queryable);

            var responseList = (result.Items ?? []).Select(transfer => new UserTransferResponseDto
            {
                Id = transfer.Id,
                UserId = transfer.UserId,
                FromEmployeeId = transfer.FromEmployeeId,
                ToEmployeeId = transfer.ToEmployeeId,
                Reason = transfer.Reason,
                OperatorId = transfer.OperatorId,
                OperatorName = transfer.OperatorName,
                TransferTime = transfer.TransferTime
            }).ToList();

            return new PagedResult<UserTransferResponseDto>
            {
                Items = responseList,
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }
    }
}
