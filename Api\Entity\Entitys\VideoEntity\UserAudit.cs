using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.VideoEntity
{
    /// <summary>
    /// 用户审核记录表
    /// </summary>
    [Table("user_audits")]
    public class UserAudit : BaseEntity_GUID
    {
        /// <summary>
        /// 被审核用户ID
        /// </summary>
        [Column("user_id")]
        [Comment("被审核用户ID")]
        public string? UserId { get; set; }

        /// <summary>
        /// 审核人ID（员工ID）
        /// </summary>
        [Column("auditor_id")]
        [Comment("审核人ID（员工ID）")]
        [MaxLength(50)]
        public string? AuditorId { get; set; }

        /// <summary>
        /// 关联批次ID
        /// </summary>
        [Column("batch_id")]
        [Comment("关联批次ID")]
        public int? BatchId { get; set; }

        /// <summary>
        /// 推广链接ID
        /// </summary>
        [Column("promotion_link_id")]
        [Comment("推广链接ID")]
        public int? PromotionLinkId { get; set; }

        /// <summary>
        /// 审核状态:0待审核,1通过,2拒绝
        /// </summary>
        [Column("status")]
        [Comment("审核状态:0待审核,1通过,2拒绝")]
        public byte Status { get; set; } = 0;



        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        [Comment("创建时间")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        [Comment("更新时间")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
