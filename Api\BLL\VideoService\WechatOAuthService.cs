using BLL.Common;
using Common;
using Common.Exceptions;
using Common.JWT;
using Common.WX;
using DAL.VideoDAL;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Microsoft.Extensions.Logging;
using WxAccessTokenDto = Common.WX.WechatAccessTokenResponseDto;

namespace BLL.VideoService
{
    /// <summary>
    /// 微信OAuth2.0服务
    /// </summary>
    public class WechatOAuthService(
        UserDAL userDAL,
        WechatAccessTokenService wechatAccessTokenService,
        ILogger<WechatOAuthService> logger) : BasePermissionService(userDAL, null!)
    {
        private readonly UserDAL _userDAL = userDAL;
        private readonly WechatAccessTokenService _wechatAccessTokenService = wechatAccessTokenService;
        private readonly ILogger<WechatOAuthService> _logger = logger;

        /// <summary>
        /// 生成微信OAuth授权URL
        /// </summary>
        /// <param name="requestDto">授权请求DTO</param>
        /// <returns>授权URL</returns>
        public string GenerateOAuthUrl(WechatOAuthRequestDto requestDto)
        {
            _logger.LogInformation("开始生成微信OAuth授权URL，RedirectUri: {RedirectUri}, Scope: {Scope}, State: {State}",
                requestDto.RedirectUri, requestDto.Scope, requestDto.State);

            var appId = WxSetting.AppId;
            if (string.IsNullOrEmpty(appId))
            {
                _logger.LogError("微信AppID未配置");
                throw new BusinessException("微信AppID未配置");
            }

            var authUrl = WechatOAuthHelper.GenerateOAuthUrl(appId, requestDto.RedirectUri, requestDto.Scope, requestDto.State);
            _logger.LogInformation("成功生成微信OAuth授权URL: {AuthUrl}", authUrl);

            return authUrl;
        }

        /// <summary>
        /// 处理微信OAuth回调，完成用户登录
        /// </summary>
        /// <param name="callbackDto">回调DTO</param>
        /// <returns>登录响应</returns>
        public async Task<WechatOAuthLoginResponseDto> HandleOAuthCallbackAsync(WechatOAuthCallbackDto callbackDto)
        {
            _logger.LogInformation("开始处理微信OAuth回调，Code: {Code}, State: {State}", callbackDto.Code, callbackDto.State);

            var appId = WxSetting.AppId;
            var appSecret = WxSetting.AppSecret;

            if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(appSecret))
            {
                _logger.LogError("微信配置未完整设置，AppId: {AppId}, AppSecret: {AppSecretLength}",
                    appId, string.IsNullOrEmpty(appSecret) ? "未设置" : $"{appSecret.Length}位");
                throw new BusinessException("微信配置未完整设置");
            }

            // 1. 通过code换取access_token
            _logger.LogInformation("开始通过code换取access_token，AppId: {AppId}, Code: {Code}", appId, callbackDto.Code);
            var tokenResponse = await WechatOAuthHelper.GetOAuthAccessTokenAsync(appId, appSecret, callbackDto.Code);
            if (tokenResponse == null)
            {
                _logger.LogError("获取微信access_token失败，Code: {Code}", callbackDto.Code);
                throw new BusinessException("获取微信access_token失败");
            }
            _logger.LogInformation("成功获取微信access_token，OpenId: {OpenId}, UnionId: {UnionId}, ExpiresIn: {ExpiresIn}",
                tokenResponse.OpenId, tokenResponse.UnionId, tokenResponse.ExpiresIn);

            // 2. 获取用户信息
            _logger.LogInformation("开始获取微信用户信息，AccessToken: {AccessTokenLength}位, OpenId: {OpenId}",
                tokenResponse.AccessToken?.Length ?? 0, tokenResponse.OpenId);
            var wechatUserInfo = await WechatOAuthHelper.GetUserInfoAsync(tokenResponse.AccessToken, tokenResponse.OpenId);
            if (wechatUserInfo == null)
            {
                _logger.LogError("获取微信用户信息失败，OpenId: {OpenId}", tokenResponse.OpenId);
                throw new BusinessException("获取微信用户信息失败");
            }
            _logger.LogInformation("成功获取微信用户信息，昵称: {Nickname}, UnionId: {UnionId}, 头像: {Avatar}",
                wechatUserInfo.Nickname, wechatUserInfo.UnionId, wechatUserInfo.HeadImgUrl);

            // 3. 查找或创建用户
            _logger.LogInformation("开始查找或创建用户，OpenId: {OpenId}, UnionId: {UnionId}",
                wechatUserInfo.OpenId, wechatUserInfo.UnionId);
            var user = await FindOrCreateUserAsync(wechatUserInfo, tokenResponse);
            _logger.LogInformation("用户处理完成，用户ID: {UserId}, 是否新用户: {IsNewUser}",
                user.Id, user.CreateTime > DateTime.Now.AddMinutes(-1));

            // 4. 生成JWT Token
            _logger.LogInformation("开始生成JWT Token，用户ID: {UserId}", user.Id);
            var jwtToken = GenerateJwtToken(user);
            _logger.LogInformation("成功生成JWT Token，Token长度: {TokenLength}位", jwtToken?.Length ?? 0);

            // 5. 记录登录日志
            await LogLoginAsync(user, wechatUserInfo);

            var response = new WechatOAuthLoginResponseDto
            {
                AccessToken = jwtToken,
                UserInfo = new UserResponseDto
                {
                    Id = user.Id,
                    OpenId = user.OpenId,
                    UnionId = user.UnionId,
                    Nickname = user.Nickname,
                    Avatar = user.Avatar,
                    EmployeeId = user.EmployeeId,
                    LastLogin = user.LastLogin,
                    CreateTime = user.CreateTime
                },
                IsNewUser = user.CreateTime > DateTime.Now.AddMinutes(-1) // 1分钟内创建的视为新用户
            };

            _logger.LogInformation("微信OAuth登录完成，用户ID: {UserId}, 昵称: {Nickname}, 是否新用户: {IsNewUser}",
                user.Id, user.Nickname, response.IsNewUser);

            return response;
        }

        /// <summary>
        /// 刷新微信基础access_token
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> RefreshBasicAccessTokenAsync()
        {
            _logger.LogInformation("开始刷新微信基础access_token");

            var appId = WxSetting.AppId;
            var appSecret = WxSetting.AppSecret;

            if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(appSecret))
            {
                _logger.LogError("微信配置未完整设置，无法刷新基础access_token");
                throw new BusinessException("微信配置未完整设置");
            }

            // 获取新的基础access_token
            _logger.LogInformation("开始获取新的基础access_token，AppId: {AppId}", appId);
            var tokenResponse = await WechatOAuthHelper.GetBasicAccessTokenAsync(appId, appSecret);
            if (tokenResponse == null)
            {
                _logger.LogError("获取微信基础access_token失败");
                return false;
            }
            _logger.LogInformation("成功获取微信基础access_token，ExpiresIn: {ExpiresIn}秒", tokenResponse.ExpiresIn);

            // 保存到数据库
            var createDto = new WechatAccessTokenCreateDto
            {
                AppId = appId,
                AccessToken = tokenResponse.AccessToken,
                ExpiresAt = DateTime.Now.AddSeconds(tokenResponse.ExpiresIn),
                ExpiresIn = tokenResponse.ExpiresIn,
                TokenType = "basic_access_token",
                IsValid = 1
            };

            // 系统自动创建用户，无需当前用户信息

            await _wechatAccessTokenService.CreateAccessTokenAsync(createDto, null!);
            return true;
        }

        /// <summary>
        /// 查找或创建用户
        /// </summary>
        /// <param name="wechatUserInfo">微信用户信息</param>
        /// <param name="tokenResponse">微信token响应</param>
        /// <returns>用户实体</returns>
        private async Task<User> FindOrCreateUserAsync(WechatUserInfoDto wechatUserInfo, WxAccessTokenDto tokenResponse)
        {
            User? user = null;

            // 优先通过UnionID查找用户
            if (!string.IsNullOrEmpty(wechatUserInfo.UnionId))
            {
                _logger.LogInformation("通过UnionID查找用户: {UnionId}", wechatUserInfo.UnionId);
                user = await _userDAL.GetByUnionIdAsync(wechatUserInfo.UnionId);
                if (user != null)
                {
                    _logger.LogInformation("通过UnionID找到现有用户，用户ID: {UserId}, 昵称: {Nickname}", user.Id, user.Nickname);
                }
                else
                {
                    _logger.LogInformation("通过UnionID未找到用户: {UnionId}", wechatUserInfo.UnionId);
                }
            }

            // 如果通过UnionID没找到，再通过OpenID查找
            if (user == null)
            {
                _logger.LogInformation("通过OpenID查找用户: {OpenId}", wechatUserInfo.OpenId);
                user = await _userDAL.GetByOpenIdAsync(wechatUserInfo.OpenId);
                if (user != null)
                {
                    _logger.LogInformation("通过OpenID找到现有用户，用户ID: {UserId}, 昵称: {Nickname}", user.Id, user.Nickname);
                }
                else
                {
                    _logger.LogInformation("通过OpenID未找到用户: {OpenId}", wechatUserInfo.OpenId);
                }
            }

            if (user != null)
            {
                // 更新现有用户信息
                _logger.LogInformation("更新现有用户信息，用户ID: {UserId}, 原昵称: {OldNickname}, 新昵称: {NewNickname}",
                    user.Id, user.Nickname, wechatUserInfo.Nickname);

                user.OpenId = wechatUserInfo.OpenId;
                user.UnionId = wechatUserInfo.UnionId ?? tokenResponse.UnionId;
                user.Nickname = wechatUserInfo.Nickname;
                user.Avatar = wechatUserInfo.HeadImgUrl;
                user.LastLogin = DateTime.Now;
                user.UpdateTime = DateTime.Now;

                await _userDAL.UpdateAsync(user);
                _logger.LogInformation("成功更新用户信息，用户ID: {UserId}", user.Id);
            }
            else
            {
                // 创建新用户
                _logger.LogInformation("创建新用户，OpenId: {OpenId}, UnionId: {UnionId}, 昵称: {Nickname}",
                    wechatUserInfo.OpenId, wechatUserInfo.UnionId ?? tokenResponse.UnionId, wechatUserInfo.Nickname);

                user = new User
                {
                    OpenId = wechatUserInfo.OpenId,
                    UnionId = wechatUserInfo.UnionId ?? tokenResponse.UnionId,
                    Nickname = wechatUserInfo.Nickname,
                    Avatar = wechatUserInfo.HeadImgUrl,
                    LastLogin = DateTime.Now,
                    CreateTime = DateTime.Now,
                    CreatedBy = "WECHAT_OAUTH",
                    CreatorName = "微信授权登录"
                };

                await _userDAL.AddAsync(user);
                _logger.LogInformation("成功创建新用户，用户ID: {UserId}, 昵称: {Nickname}", user.Id, user.Nickname);
            }

            return user;
        }

        /// <summary>
        /// 生成JWT Token
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>JWT Token</returns>
        private static string GenerateJwtToken(User user)
        {
            var userInfo = new UserInfo
            {
                UserId = user.Id,
                UserName = user.Nickname ?? $"用户{user.Id}",
                IsAdmin = false,
                UserType = 4, // 普通用户类型
                Roles = []
            };

            return JWTHelper.CreateJwt(userInfo);
        }

        /// <summary>
        /// 记录登录日志
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="wechatUserInfo">微信用户信息</param>
        private async Task LogLoginAsync(User user, WechatUserInfoDto wechatUserInfo)
        {
            try
            {
                _logger.LogInformation("用户登录成功 - 用户ID: {UserId}, 昵称: {Nickname}, OpenId: {OpenId}, UnionId: {UnionId}, 员工ID: {EmployeeId}, 登录时间: {LoginTime}",
                    user.Id, user.Nickname, user.OpenId, user.UnionId, user.EmployeeId, user.LastLogin);

                // 记录详细的微信用户信息
                _logger.LogDebug("微信用户详细信息 - 性别: {Sex}, 省份: {Province}, 城市: {City}, 国家: {Country}, 头像: {Avatar}",
                    wechatUserInfo.Sex, wechatUserInfo.Province, wechatUserInfo.City, wechatUserInfo.Country, wechatUserInfo.HeadImgUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录登录日志时发生错误，用户ID: {UserId}", user.Id);
            }

            await Task.CompletedTask;
        }
    }
}
