using BLL.VideoService;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 微信红包发放控制器
    /// 用于视频营销系统中的红包发放管理
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class WechatPaymentController(WechatPaymentService wechatPaymentService) : BaseController
    {
        private readonly WechatPaymentService _wechatPaymentService = wechatPaymentService;

        /// <summary>
        /// 发放红包
        /// 用户完成视频观看和答题后，系统自动调用此接口发放红包
        /// </summary>
        /// <param name="createDto">红包发放DTO</param>
        /// <returns>支付记录ID</returns>
        [HttpPost("send-reward", Name = "WechatPayment_SendReward")]
        public async Task<Result<int>> SendReward([FromBody] WechatPaymentCreateDto createDto)
        {
            var paymentId = await _wechatPaymentService.CreatePaymentOrderAsync(createDto, GetCurrentUserInfo());
            return Success(paymentId, "红包发放成功");
        }

        /// <summary>
        /// 获取红包发放记录详情
        /// </summary>
        /// <param name="paymentId">支付记录ID</param>
        /// <returns>红包发放详情</returns>
        [HttpGet("{paymentId}", Name = "WechatPayment_GetRewardRecord")]
        public async Task<Result<WechatPaymentResponseDto?>> GetRewardRecord(int paymentId)
        => Success(await _wechatPaymentService.GetPaymentOrderAsync(paymentId));

        /// <summary>
        /// 根据商户订单号获取红包发放记录
        /// </summary>
        /// <param name="outTradeNo">商户订单号</param>
        /// <returns>红包发放详情</returns>
        [HttpGet("trade/{outTradeNo}", Name = "WechatPayment_GetRewardByTradeNo")]
        public async Task<Result<WechatPaymentResponseDto?>> GetRewardByTradeNo(string outTradeNo)
        => Success(await _wechatPaymentService.GetPaymentOrderByOrderNoAsync(outTradeNo));

        /// <summary>
        /// 分页查询红包发放记录列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>红包发放记录列表</returns>
        [HttpGet]
        public async Task<Result<PagedResult<WechatPaymentResponseDto>>> GetRewardPagedList([FromQuery] WechatPaymentQueryDto queryDto)
        => Success(await _wechatPaymentService.GetPaymentOrderPagedListAsync(queryDto));

        /// <summary>
        /// 获取用户红包记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="status">发放状态（可选）</param>
        /// <returns>用户红包记录列表</returns>
        [HttpGet("user/{userId}")]
        public async Task<Result<List<WechatPaymentResponseDto>>> GetUserRewards(string userId, [FromQuery] byte? status = null)
        => Success(await _wechatPaymentService.GetUserPaymentOrdersAsync(userId, status));

        /// <summary>
        /// 获取我的红包记录
        /// </summary>
        /// <param name="status">发放状态（可选）</param>
        /// <returns>我的红包记录列表</returns>
        [HttpGet("my-rewards")]
        public async Task<Result<List<WechatPaymentResponseDto>>> GetMyRewards([FromQuery] byte? status = null)
        => Success(await _wechatPaymentService.GetUserPaymentOrdersAsync(GetCurrentUserId(), status));

        /// <summary>
        /// 微信红包发放回调
        /// 微信服务器调用此接口通知红包发放结果
        /// </summary>
        /// <param name="callbackDto">回调数据</param>
        /// <returns>处理结果</returns>
        [HttpPost("callback")]
        [AllowAnonymous]
        public async Task<Result<string>> WechatRewardCallback([FromBody] WechatPaymentCallbackDto callbackDto)
        {
            var result = await _wechatPaymentService.HandlePaymentCallbackAsync(callbackDto);
            return Success(result ? "SUCCESS" : "FAIL", "红包发放回调处理成功");
        }

        /// <summary>
        /// 查询红包发放状态
        /// </summary>
        /// <param name="paymentId">支付记录ID</param>
        /// <returns>红包发放状态</returns>
        [HttpGet("{paymentId}/status")]
        public async Task<Result<WechatPaymentResponseDto?>> QueryRewardStatus(int paymentId)
        => Success(await _wechatPaymentService.GetPaymentOrderAsync(paymentId));

        /// <summary>
        /// 更新红包发放状态
        /// 管理员手动更新红包发放状态（如发放失败需要重新发放）
        /// </summary>
        /// <param name="paymentId">支付记录ID</param>
        /// <param name="status">新状态</param>
        /// <param name="transactionId">微信交易号（可选）</param>
        /// <returns>是否成功</returns>
        [HttpPost("{paymentId}/status")]
        public async Task<Result<bool>> UpdateRewardStatus(int paymentId, [FromQuery] byte status, [FromQuery] string? transactionId = null)
        {
            var payment = await _wechatPaymentService.GetPaymentOrderAsync(paymentId);
            if (payment == null)
                return Fail<bool>("红包记录不存在");

            var result = await _wechatPaymentService.UpdatePaymentStatusAsync(payment.OutTradeNo, status, transactionId, DateTime.Now, GetCurrentUserInfo());
            return Success(result, "红包状态更新成功");
        }

        /// <summary>
        /// 获取红包发放统计
        /// </summary>
        /// <param name="statisticsDto">统计查询DTO</param>
        /// <returns>红包发放统计</returns>
        [HttpPost("statistics")]
        public async Task<Result<WechatPaymentStatisticsDto>> GetRewardStatistics([FromBody] WechatPaymentQueryDto statisticsDto)
        => Success(await _wechatPaymentService.GetPaymentStatisticsAsync(
            statisticsDto.StartTime ?? DateTime.Today.AddDays(-30),
            statisticsDto.EndTime ?? DateTime.Today,
            statisticsDto.UserId));

        /// <summary>
        /// 导出红包发放记录
        /// </summary>
        /// <param name="exportDto">导出查询DTO</param>
        /// <returns>导出数据</returns>
        [HttpPost("export")]
        public async Task<Result<List<WechatPaymentResponseDto>>> ExportRewards([FromBody] WechatPaymentQueryDto exportDto)
        {
            var pagedResult = await _wechatPaymentService.GetPaymentOrderPagedListAsync(exportDto);
            var exportData = pagedResult.Items;
            return Success(exportData, "红包发放记录导出成功");
        }

        /// <summary>
        /// 重新发放失败的红包
        /// 对于发放失败的红包，管理员可以手动重新发放
        /// </summary>
        /// <param name="paymentId">支付记录ID</param>
        /// <returns>是否成功</returns>
        [HttpPost("{paymentId}/retry")]
        public async Task<Result<bool>> RetryReward(int paymentId)
        {
            var payment = await _wechatPaymentService.GetPaymentOrderAsync(paymentId);
            if (payment == null)
                return Fail<bool>("红包记录不存在");

            if (payment.Status != 2) // 2表示发放失败
                return Fail<bool>("只能重新发放失败的红包");

            // 这里需要调用微信API重新发放红包，暂时更新状态为待处理
            var result = await _wechatPaymentService.UpdatePaymentStatusAsync(payment.OutTradeNo, 0, null, null, GetCurrentUserInfo());
            return Success(result, "红包重新发放请求已提交");
        }

        /// <summary>
        /// 获取红包发放失败记录
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>失败记录列表</returns>
        [HttpGet("failed")]
        public async Task<Result<PagedResult<WechatPaymentResponseDto>>> GetFailedRewards([FromQuery] WechatPaymentQueryDto queryDto)
        {
            queryDto.Status = 2; // 只查询失败的记录
            return Success(await _wechatPaymentService.GetPaymentOrderPagedListAsync(queryDto));
        }

        /// <summary>
        /// 获取待发放红包记录
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>待发放记录列表</returns>
        [HttpGet("pending")]
        public async Task<Result<PagedResult<WechatPaymentResponseDto>>> GetPendingRewards([FromQuery] WechatPaymentQueryDto queryDto)
        {
            queryDto.Status = 0; // 只查询待发放的记录
            return Success(await _wechatPaymentService.GetPaymentOrderPagedListAsync(queryDto));
        }

        /// <summary>
        /// 批量重新发放失败的红包
        /// </summary>
        /// <param name="paymentIds">支付记录ID列表</param>
        /// <returns>处理结果</returns>
        [HttpPost("batch-retry")]
        public async Task<Result<int>> BatchRetryRewards([FromBody] List<int> paymentIds)
        {
            int successCount = 0;
            foreach (var paymentId in paymentIds)
            {
                var payment = await _wechatPaymentService.GetPaymentOrderAsync(paymentId);
                if (payment != null && payment.Status == 2)
                {
                    var result = await _wechatPaymentService.UpdatePaymentStatusAsync(payment.OutTradeNo, 0, null, null, GetCurrentUserInfo());
                    if (result) successCount++;
                }
            }
            return Success(successCount, $"成功提交 {successCount} 个红包重新发放请求");
        }
    }
}
