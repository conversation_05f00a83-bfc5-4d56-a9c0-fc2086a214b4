using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using BLL.VideoService;
using Entity.Dto.VideoDto;
using Entity.Dto;
using System.Text.Json;

namespace Scripts
{
    /// <summary>
    /// 修复已压缩但数据库未更新的视频记录
    /// </summary>
    public class FixCompressedVideos
    {
        public static async Task FixAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var videoService = scope.ServiceProvider.GetRequiredService<VideoService>();
            var videoDAL = scope.ServiceProvider.GetRequiredService<DAL.VideoDAL.VideoDAL>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<FixCompressedVideos>>();

            try
            {
                logger.LogInformation("开始修复已压缩的视频记录...");

                // 获取所有视频记录
                var queryable = new DAL.VideoDAL.VideoDAL.Queryable
                {
                    PageIndex = 1,
                    PageSize = 1000
                };
                var result = await videoDAL.GetPagedListAsync(queryable);
                var videos = result.Items ?? new List<Entity.Entitys.VideoEntity.Video>();

                logger.LogInformation($"找到 {videos.Count} 个视频记录");

                var wwwrootPath = Path.Combine(Environment.CurrentDirectory, "wwwroot");
                var compressedFolder = Path.Combine(wwwrootPath, "videos", "compressed");

                if (!Directory.Exists(compressedFolder))
                {
                    logger.LogWarning("压缩文件夹不存在");
                    return;
                }

                int fixedCount = 0;

                foreach (var video in videos)
                {
                    try
                    {
                        // 检查是否是原始文件URL
                        if (video.VideoUrl.Contains("/videos/original/"))
                        {
                            // 构造对应的压缩文件路径
                            var originalPath = video.VideoUrl.Replace("/", "\\");
                            if (originalPath.StartsWith("\\"))
                                originalPath = originalPath.Substring(1);

                            var fullOriginalPath = Path.Combine(wwwrootPath, originalPath);
                            var fileName = Path.GetFileNameWithoutExtension(fullOriginalPath);
                            var extension = Path.GetExtension(fullOriginalPath);

                            // 提取fileId (假设格式是 fileId_original.ext)
                            var fileId = fileName.Replace("_original", "");
                            var dateFolder = Path.GetFileName(Path.GetDirectoryName(fullOriginalPath));

                            var compressedFileName = $"{fileId}_compressed{extension}";
                            var compressedFilePath = Path.Combine(compressedFolder, dateFolder, compressedFileName);

                            if (File.Exists(compressedFilePath))
                            {
                                // 压缩文件存在，更新数据库
                                var compressedUrl = $"/videos/compressed/{dateFolder}/{compressedFileName}";

                                // 解析Questions字段
                                List<VideoQuestionDto>? questions = null;
                                if (!string.IsNullOrEmpty(video.Questions))
                                {
                                    try
                                    {
                                        questions = JsonSerializer.Deserialize<List<VideoQuestionDto>>(video.Questions);
                                    }
                                    catch (Exception ex)
                                    {
                                        logger.LogWarning(ex, $"解析视频问题失败: VideoId={video.Id}");
                                    }
                                }

                                // 创建更新DTO
                                var updateDto = new VideoUpdateDto
                                {
                                    Id = video.Id,
                                    Title = video.Title,
                                    Description = video.Description,
                                    VideoUrl = compressedUrl,
                                    CoverUrl = video.CoverUrl,
                                    Duration = video.Duration,
                                    RewardAmount = video.RewardAmount,
                                    Questions = questions
                                };

                                // 创建系统用户信息
                                var systemUserInfo = new CurrentUserInfoDto
                                {
                                    UserId = "SYSTEM",
                                    UserName = "系统修复脚本",
                                    UserType = 1
                                };

                                await videoService.UpdateVideoAsync(updateDto, systemUserInfo);
                                fixedCount++;

                                logger.LogInformation($"已修复视频 {video.Id}: {video.Title} -> {compressedUrl}");

                                // 删除原始文件（如果存在）
                                if (File.Exists(fullOriginalPath))
                                {
                                    File.Delete(fullOriginalPath);
                                    logger.LogInformation($"已删除原始文件: {fullOriginalPath}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, $"修复视频 {video.Id} 失败");
                    }
                }

                logger.LogInformation($"修复完成！共修复了 {fixedCount} 个视频记录");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "修复过程中发生错误");
            }
        }
    }
}
