# 微信服务号视频营销系统数据库设计

## 角色管理表

```sql
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

## 用户管理表

```sql
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(100) DEFAULT NULL COMMENT '微信OpenID',
  `unionid` varchar(100) DEFAULT NULL COMMENT '微信UnionID',
  `nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `role_id` int NOT NULL COMMENT '角色ID',
  `parent_id` int DEFAULT NULL COMMENT '上级ID',
  `super_parent_id` int DEFAULT NULL COMMENT '上上级ID(冗余)',
  `username` varchar(100) DEFAULT NULL COMMENT '账号(超管使用)',
  `password` varchar(255) DEFAULT NULL COMMENT '密码(超管使用)',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `status` tinyint DEFAULT '0' COMMENT '状态:0待审核,1正常,2禁用',
  `audit_status` tinyint DEFAULT '0' COMMENT '用户审核状态:0待审核,1审核通过,2审核拒绝',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_openid` (`openid`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```



## 视频内容表

```sql
CREATE TABLE `videos` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '视频ID',
  `title` varchar(255) NOT NULL COMMENT '视频标题',
  `description` text COMMENT '视频描述',
  `cover_url` varchar(255) DEFAULT NULL COMMENT '封面URL',
  `video_url` varchar(255) NOT NULL COMMENT '视频URL',
  `duration` int DEFAULT '0' COMMENT '视频时长(秒)',
  `creator_id` int NOT NULL COMMENT '创建人ID',
  `reward_amount` decimal(10,2) DEFAULT '0.00' COMMENT '红包金额',
  `questions` json DEFAULT NULL COMMENT '问题JSON格式：[{"question_text":"问题内容","order_num":0,"options":[{"option_text":"选项1","is_correct":1,"order_num":0},{"option_text":"选项2","is_correct":0,"order_num":1}]}]',
  `status` tinyint DEFAULT '1' COMMENT '状态:0下架,1上架',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频表';
```



## 批次表

```sql
CREATE TABLE `batches` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '批次ID',
  `name` varchar(100) NOT NULL COMMENT '批次名称',
  `description` varchar(255) DEFAULT NULL COMMENT '批次描述',
  `video_id` int NOT NULL COMMENT '关联视频ID',
  `video_title` varchar(255) NOT NULL COMMENT '视频标题(冗余)',
  `video_description` text COMMENT '视频描述(冗余)',
  `video_cover_url` varchar(255) DEFAULT NULL COMMENT '封面URL(冗余)',
  `video_url` varchar(255) NOT NULL COMMENT '视频URL(冗余)',
  `video_duration` int DEFAULT '0' COMMENT '视频时长(冗余)',
  `reward_amount` decimal(10,2) DEFAULT '0.00' COMMENT '红包金额(冗余)',
  `questions` json DEFAULT NULL COMMENT '问题JSON(冗余)',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `creator_id` int NOT NULL COMMENT '创建人ID',
  `status` tinyint DEFAULT '1' COMMENT '状态:0下线,1上线',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_video_id` (`video_id`),
  KEY `idx_creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批次表';
```


## 用户观看记录表

```sql
CREATE TABLE `view_records` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `batch_id` int NOT NULL COMMENT '批次ID',
  `video_id` int NOT NULL COMMENT '视频ID(冗余)',
  `promotion_link_id` int DEFAULT NULL COMMENT '推广链接ID',
  `employee_id` int DEFAULT NULL COMMENT '员工ID(冗余)',
  `admin_id` int DEFAULT NULL COMMENT '管理ID(冗余)',
  `view_duration` int DEFAULT '0' COMMENT '观看时长(秒)',
  `is_complete` tinyint DEFAULT '0' COMMENT '是否完播:0否,1是',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户设备信息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_employee_id` (`employee_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户观看记录表';
```

## 用户答题记录表

```sql
CREATE TABLE `answer_records` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `batch_id` int NOT NULL COMMENT '批次ID',
  `video_id` int NOT NULL COMMENT '视频ID(冗余)',
  `question_text` varchar(500) NOT NULL COMMENT '问题内容(冗余)',
  `option_text` varchar(255) NOT NULL COMMENT '选项内容(冗余)',
  `is_correct` tinyint DEFAULT '0' COMMENT '是否正确:0否,1是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_batch_id` (`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户答题记录表';
```

## 红包记录表

```sql
CREATE TABLE `rewards` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `batch_id` int NOT NULL COMMENT '批次ID',
  `video_id` int NOT NULL COMMENT '视频ID(冗余)',
  `employee_id` int DEFAULT NULL COMMENT '员工ID(冗余)',
  `admin_id` int DEFAULT NULL COMMENT '管理ID(冗余)',
  `amount` decimal(10,2) NOT NULL COMMENT '红包金额',
  `correct_rate` decimal(5,2) DEFAULT NULL COMMENT '正确率',
  `status` tinyint DEFAULT '0' COMMENT '状态:0未发放,1已发放,2发放失败',
  `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '微信支付交易ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_employee_id` (`employee_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='红包记录表';
```

## 用户审核记录表

```sql
CREATE TABLE `user_audits` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int NOT NULL COMMENT '被审核用户ID',
  `auditor_id` int DEFAULT NULL COMMENT '审核人ID',
  `batch_id` int DEFAULT NULL COMMENT '关联批次ID',
  `promotion_link_id` int DEFAULT NULL COMMENT '推广链接ID',
  `status` tinyint DEFAULT '0' COMMENT '审核状态:0待审核,1通过,2拒绝',
  `remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_auditor_id` (`auditor_id`),
  KEY `idx_batch_id` (`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户审核记录表';
```

## 系统配置表

```sql
CREATE TABLE `system_configs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

## 操作日志表

```sql
CREATE TABLE `operation_logs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int NOT NULL COMMENT '操作人ID',
  `module` varchar(50) NOT NULL COMMENT '模块名称',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `content` text COMMENT '操作内容',
  `ip` varchar(50) DEFAULT NULL COMMENT '操作IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module` (`module`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';
```

## 用户转移记录表

```sql
CREATE TABLE `user_transfers` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `from_employee_id` int DEFAULT NULL COMMENT '原员工ID',
  `to_employee_id` int DEFAULT NULL COMMENT '目标员工ID',
  `from_admin_id` int DEFAULT NULL COMMENT '原管理ID',
  `to_admin_id` int DEFAULT NULL COMMENT '目标管理ID',
  `operator_id` int NOT NULL COMMENT '操作人ID',
  `reason` varchar(255) DEFAULT NULL COMMENT '转移原因',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_from_employee_id` (`from_employee_id`),
  KEY `idx_to_employee_id` (`to_employee_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户转移记录表';
```

## 系统错误日志表

```sql
CREATE TABLE `error_logs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `level` varchar(20) NOT NULL COMMENT '错误级别',
  `message` text NOT NULL COMMENT '错误信息',
  `file` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `line` int DEFAULT NULL COMMENT '行号',
  `trace` text COMMENT '堆栈跟踪',
  `request_data` text COMMENT '请求数据',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_level` (`level`),
  KEY `idx_created_at` (`created_at`) 
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统错误日志表';
```

## 统计报表表

```sql
CREATE TABLE `statistics` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `date` date NOT NULL COMMENT '统计日期',
  `admin_id` int DEFAULT NULL COMMENT '管理ID',
  `employee_id` int DEFAULT NULL COMMENT '员工ID',
  `batch_id` int DEFAULT NULL COMMENT '批次ID',
  `video_id` int DEFAULT NULL COMMENT '视频ID',
  `new_user_count` int DEFAULT '0' COMMENT '新增用户数',
  `view_count` int DEFAULT '0' COMMENT '观看次数',
  `complete_view_count` int DEFAULT '0' COMMENT '完播次数',
  `complete_rate` decimal(5,2) DEFAULT '0.00' COMMENT '完播率',
  `question_correct_count` int DEFAULT '0' COMMENT '答题正确数',
  `question_total_count` int DEFAULT '0' COMMENT '答题总数',
  `correct_rate` decimal(5,2) DEFAULT '0.00' COMMENT '答题正确率',
  `reward_count` int DEFAULT '0' COMMENT '领取红包数',
  `reward_amount` decimal(10,2) DEFAULT '0.00' COMMENT '红包总金额',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_date_admin_employee_batch` (`date`,`admin_id`,`employee_id`,`batch_id`),
  KEY `idx_date` (`date`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_batch_id` (`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统计报表表';
```

## 微信AccessToken记录表

```sql
CREATE TABLE `wechat_access_tokens` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `access_token` varchar(500) NOT NULL COMMENT 'AccessToken',
  `expires_in` int NOT NULL COMMENT '有效时间(秒)',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信AccessToken记录表';
```

## 微信支付记录表

```sql
CREATE TABLE `wechat_payments` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `reward_id` int NOT NULL COMMENT '红包记录ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '用户OpenID',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '微信交易ID',
  `status` tinyint DEFAULT '0' COMMENT '状态:0未处理,1成功,2失败',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `error_msg` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_reward_id` (`reward_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_id` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信支付记录表';
```
