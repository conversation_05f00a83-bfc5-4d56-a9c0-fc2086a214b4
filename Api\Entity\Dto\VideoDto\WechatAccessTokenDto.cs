using System.ComponentModel.DataAnnotations;

namespace Entity.Dto.VideoDto
{
    /// <summary>
    /// 微信AccessToken创建DTO
    /// </summary>
    public class WechatAccessTokenCreateDto
    {
        /// <summary>
        /// 微信AppID
        /// </summary>
        [Required(ErrorMessage = "微信AppID不能为空")]
        [MaxLength(100, ErrorMessage = "微信AppID长度不能超过100个字符")]
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// AccessToken
        /// </summary>
        [Required(ErrorMessage = "AccessToken不能为空")]
        [MaxLength(512, ErrorMessage = "AccessToken长度不能超过512个字符")]
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// 过期时间
        /// </summary>
        [Required(ErrorMessage = "过期时间不能为空")]
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Token类型
        /// </summary>
        [MaxLength(50, ErrorMessage = "Token类型长度不能超过50个字符")]
        public string? TokenType { get; set; }

        /// <summary>
        /// 是否有效:0无效,1有效
        /// </summary>
        public byte IsValid { get; set; } = 1;

        /// <summary>
        /// 过期时间（秒）
        /// </summary>
        public int? ExpiresIn { get; set; }

        /// <summary>
        /// 刷新Token
        /// </summary>
        [MaxLength(512, ErrorMessage = "刷新Token长度不能超过512个字符")]
        public string? RefreshToken { get; set; }

        /// <summary>
        /// 权限范围
        /// </summary>
        [MaxLength(200, ErrorMessage = "权限范围长度不能超过200个字符")]
        public string? Scope { get; set; }
    }

    /// <summary>
    /// 微信AccessToken更新DTO
    /// </summary>
    public class WechatAccessTokenUpdateDto
    {
        /// <summary>
        /// AccessToken
        /// </summary>
        [MaxLength(512, ErrorMessage = "AccessToken长度不能超过512个字符")]
        public string? AccessToken { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Token类型
        /// </summary>
        [MaxLength(50, ErrorMessage = "Token类型长度不能超过50个字符")]
        public string? TokenType { get; set; }

        /// <summary>
        /// 是否有效:0无效,1有效
        /// </summary>
        public byte? IsValid { get; set; }

        /// <summary>
        /// Token ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 过期时间（秒）
        /// </summary>
        public int? ExpiresIn { get; set; }

        /// <summary>
        /// 刷新Token
        /// </summary>
        [MaxLength(512, ErrorMessage = "刷新Token长度不能超过512个字符")]
        public string? RefreshToken { get; set; }

        /// <summary>
        /// 权限范围
        /// </summary>
        [MaxLength(200, ErrorMessage = "权限范围长度不能超过200个字符")]
        public string? Scope { get; set; }
    }

    /// <summary>
    /// 微信AccessToken查询DTO
    /// </summary>
    public class WechatAccessTokenQueryDto : BaseQueryDto
    {
        /// <summary>
        /// 微信AppID
        /// </summary>
        public string? AppId { get; set; }

        /// <summary>
        /// Token类型
        /// </summary>
        public string? TokenType { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        public byte? IsValid { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 是否过期
        /// </summary>
        public bool? IsExpired { get; set; }

        /// <summary>
        /// 获取开始时间
        /// </summary>
        public DateTime? ObtainStartTime { get; set; }

        /// <summary>
        /// 获取结束时间
        /// </summary>
        public DateTime? ObtainEndTime { get; set; }

        /// <summary>
        /// 过期开始时间
        /// </summary>
        public DateTime? ExpiresStartTime { get; set; }

        /// <summary>
        /// 过期结束时间
        /// </summary>
        public DateTime? ExpiresEndTime { get; set; }
    }

    /// <summary>
    /// 微信AccessToken响应DTO
    /// </summary>
    public class WechatAccessTokenResponseDto
    {
        /// <summary>
        /// Token ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 微信AppID
        /// </summary>
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// AccessToken
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// Token类型
        /// </summary>
        public string? TokenType { get; set; }

        /// <summary>
        /// 是否有效:0无效,1有效
        /// </summary>
        public byte IsValid { get; set; }

        /// <summary>
        /// 是否过期
        /// </summary>
        public bool IsExpired => DateTime.Now >= ExpiresAt;

        /// <summary>
        /// 剩余有效时间（秒）
        /// </summary>
        public long RemainingSeconds => IsExpired ? 0 : (long)(ExpiresAt - DateTime.Now).TotalSeconds;

        /// <summary>
        /// 获取时间
        /// </summary>
        public DateTime ObtainTime { get; set; }

        /// <summary>
        /// 过期时间(秒)
        /// </summary>
        public int ExpiresIn { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpiresTime { get; set; }

        /// <summary>
        /// 刷新Token
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// 授权范围
        /// </summary>
        public string? Scope { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 微信Token统计DTO
    /// </summary>
    public class WechatTokenStatisticsDto
    {
        /// <summary>
        /// 总Token数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 有效Token数
        /// </summary>
        public int ValidCount { get; set; }

        /// <summary>
        /// 过期Token数
        /// </summary>
        public int ExpiredCount { get; set; }

        /// <summary>
        /// 即将过期Token数（1小时内）
        /// </summary>
        public int ExpiringSoonCount { get; set; }

        /// <summary>
        /// Token类型统计
        /// </summary>
        public Dictionary<string, int> TypeStatistics { get; set; } = [];

        /// <summary>
        /// 活跃Token数
        /// </summary>
        public int ActiveCount { get; set; }

        /// <summary>
        /// 即将过期Token数
        /// </summary>
        public int ExpiringCount { get; set; }

        /// <summary>
        /// 应用数量
        /// </summary>
        public int AppCount { get; set; }

        /// <summary>
        /// Token类型统计
        /// </summary>
        public Dictionary<string, int> TokenTypeStats { get; set; } = [];

        /// <summary>
        /// AppID统计
        /// </summary>
        public Dictionary<string, int> AppIdStatistics { get; set; } = [];
    }

    /// <summary>
    /// Token刷新请求DTO
    /// </summary>
    public class TokenRefreshRequestDto
    {
        /// <summary>
        /// Token类型列表
        /// </summary>
        [Required(ErrorMessage = "Token类型列表不能为空")]
        public List<string> TokenTypes { get; set; } = [];

        /// <summary>
        /// 应用ID
        /// </summary>
        public string? AppId { get; set; }

        /// <summary>
        /// Token类型
        /// </summary>
        public string? TokenType { get; set; }

        /// <summary>
        /// 新的访问Token
        /// </summary>
        public string? NewAccessToken { get; set; }

        /// <summary>
        /// 过期时间(秒)
        /// </summary>
        public int ExpiresIn { get; set; }

        /// <summary>
        /// 是否强制刷新
        /// </summary>
        public bool ForceRefresh { get; set; } = false;
    }

    /// <summary>
    /// 批量刷新结果DTO
    /// </summary>
    public class BatchRefreshResultDto
    {
        /// <summary>
        /// 成功刷新的Token类型
        /// </summary>
        public List<string> SuccessTokenTypes { get; set; } = [];

        /// <summary>
        /// 刷新失败的Token类型
        /// </summary>
        public List<string> FailedTokenTypes { get; set; } = [];

        /// <summary>
        /// 失败原因
        /// </summary>
        public Dictionary<string, string> FailureReasons { get; set; } = [];

        /// <summary>
        /// 总数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 成功数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 失败数
        /// </summary>
        public int FailedCount { get; set; }

        /// <summary>
        /// 失败数量
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// 刷新结果列表
        /// </summary>
        public List<TokenRefreshResultItemDto> Results { get; set; } = [];

        /// <summary>
        /// 刷新时间
        /// </summary>
        public DateTime RefreshTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Token刷新结果项DTO
    /// </summary>
    public class TokenRefreshResultItemDto
    {
        /// <summary>
        /// 应用ID
        /// </summary>
        public string? AppId { get; set; }

        /// <summary>
        /// Token类型
        /// </summary>
        public string? TokenType { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 新Token
        /// </summary>
        public string? NewToken { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string? Message { get; set; }
    }
}
