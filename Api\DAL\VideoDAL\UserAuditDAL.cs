using Common.Autofac;
using DAL.Databases;
using Entity.Entitys.VideoEntity;
using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.VideoDAL
{
    /// <summary>
    /// 用户审核数据访问层实现类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserAuditDAL(MyContext context) : BaseQueryDLL<UserAudit, UserAuditDAL.Queryable>(context)
    {

        /// <summary>
        /// 用户审核查询条件模型类
        /// </summary>
        public class Queryable : PageQueryEntity
        {
            /// <summary>
            /// 用户ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? UserId { get; set; }

            /// <summary>
            /// 审核人ID（员工ID）
            /// </summary>
            [Query(QueryOperator.等于)]
            public string? AuditorId { get; set; }

            /// <summary>
            /// 批次ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? BatchId { get; set; }

            /// <summary>
            /// 推广链接ID
            /// </summary>
            [Query(QueryOperator.等于)]
            public int? PromotionLinkId { get; set; }

            /// <summary>
            /// 审核状态:0待审核,1通过,2拒绝
            /// </summary>
            [Query(QueryOperator.等于)]
            public byte? Status { get; set; }

            /// <summary>
            /// 开始时间
            /// </summary>
            [Query(QueryOperator.日期范围, relatedProperty: nameof(EndTime))]
            public DateTime? StartTime { get; set; }

            /// <summary>
            /// 结束时间
            /// </summary>
            public DateTime? EndTime { get; set; }

            /// <summary>
            /// 排序字段
            /// </summary>
            [Query(QueryOperator.排序, orderDirection: OrderDirection.降序, orderPriority: 1)]
            public DateTime? CreatedAt { get; set; }

            /// <summary>
            /// 用户ID列表（用于权限过滤）
            /// </summary>
            [Query(QueryOperator.包含于)]
            public List<string>? UserIds { get; set; }
        }

        /// <summary>
        /// 获取用户的审核记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>审核记录列表</returns>
        public async Task<List<UserAudit>> GetByUserIdAsync(string userId)
        {
            return await _dbContext.Set<UserAudit>()
                .Where(ua => ua.UserId == userId)
                .OrderByDescending(ua => ua.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// 获取用户最新的审核记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>最新审核记录</returns>
        public async Task<UserAudit?> GetLatestUserAuditAsync(string userId)
        {
            return await _dbContext.Set<UserAudit>()
                .Where(ua => ua.UserId == userId)
                .OrderByDescending(ua => ua.CreatedAt)
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取审核人的审核记录
        /// </summary>
        /// <param name="auditorId">审核人ID（员工ID）</param>
        /// <returns>审核记录列表</returns>
        public async Task<List<UserAudit>> GetByAuditorIdAsync(string auditorId)
        {
            return await _dbContext.Set<UserAudit>()
                .Where(ua => ua.AuditorId == auditorId)
                .OrderByDescending(ua => ua.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// 获取审核统计信息
        /// </summary>
        /// <param name="auditorId">审核人ID（员工ID）</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns>审核统计</returns>
        public async Task<AuditStatistics> GetAuditStatisticsAsync(string? auditorId = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _dbContext.Set<UserAudit>().AsQueryable();

            if (!string.IsNullOrEmpty(auditorId))
            {
                query = query.Where(ua => ua.AuditorId == auditorId);
            }

            if (startDate.HasValue)
            {
                query = query.Where(ua => ua.CreatedAt >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(ua => ua.CreatedAt <= endDate.Value);
            }

            var totalCount = await query.CountAsync();
            var passCount = await query.CountAsync(ua => ua.Status == 1);
            var rejectCount = await query.CountAsync(ua => ua.Status == 2);

            return new AuditStatistics
            {
                TotalCount = totalCount,
                PassCount = passCount,
                RejectCount = rejectCount,
                PassRate = totalCount > 0 ? (decimal)passCount / totalCount * 100 : 0
            };
        }

        /// <summary>
        /// 根据ID获取用户审核记录
        /// </summary>
        /// <param name="id">审核记录ID</param>
        /// <returns>审核记录</returns>
        public async Task<UserAudit?> GetByIdAsync(string id)
        {
            return await _dbContext.Set<UserAudit>().FindAsync(id);
        }

        /// <summary>
        /// 获取分页用户审核记录列表
        /// </summary>
        /// <param name="queryable">查询条件</param>
        /// <returns>分页结果</returns>
        public async Task<PageEntity<UserAudit>> GetPagedListAsync(Queryable queryable)
        {
            return await GetPageDataAsync(queryable, q => q.OrderByDescending(x => x.CreatedAt));
        }



        /// <summary>
        /// 获取待审核用户列表
        /// </summary>
        /// <returns>待审核用户列表</returns>
        public async Task<List<UserAudit>> GetPendingAuditUsersAsync()
        {
            return await _dbContext.Set<UserAudit>()
                .Where(ua => ua.Status == 0) // 0表示待审核
                .OrderBy(ua => ua.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// 审核统计信息类
        /// </summary>
        public class AuditStatistics
        {
            public int TotalCount { get; set; }
            public int PassCount { get; set; }
            public int RejectCount { get; set; }
            public decimal PassRate { get; set; }
        }
    }
}
