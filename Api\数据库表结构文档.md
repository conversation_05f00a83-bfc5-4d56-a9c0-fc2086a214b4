# 数据库表结构文档

## 概述
本文档描述了视频分享系统的数据库表结构，包含系统管理、用户管理、视频管理、红包奖励等核心功能模块。

## 基础实体说明

### BaseEntity
所有实体的基础类，包含以下公共字段：
- `Remark` - 备注
- `CreatedBy` - 创建人ID (varchar(32))
- `CreatorName` - 创建人 (varchar(50))
- `CreateTime` - 创建时间
- `UpdatedBy` - 更新人ID (varchar(32))
- `UpdaterName` - 更新人 (varchar(50))
- `UpdateTime` - 更新时间

### BaseEntity_GUID
继承BaseEntity，使用GUID作为主键：
- `Id` - 主键ID (varchar(32), GUID字符串)

### BaseEntity_ID
继承BaseEntity，使用自增ID作为主键：
- `Id` - 主键ID (int, 自增)

---

## 系统管理模块

### 1. sys_user (系统用户表)
**用途**: 存储超级管理员、管理员和员工的基本信息和登录信息

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| UserId | varchar | 50 | 是 | - | 用户ID (主键) |
| UserName | varchar | 100 | 是 | - | 用户名 |
| Password | varchar | 100 | 是 | - | 密码 |
| RealName | varchar | 100 | 是 | - | 真实姓名 |
| Avatar | varchar | 200 | 否 | - | 头像URL |
| Email | varchar | 100 | 否 | - | 电子邮箱 |
| Mobile | varchar | 20 | 是 | - | 手机号码 |
| UserType | tinyint | - | 是 | - | 用户类型 (1:超级管理员 2:管理员 3:员工) |
| ParentUserId | varchar | 50 | 否 | - | 上级用户ID |
| Status | tinyint | - | 否 | 2 | 用户状态 (0:禁用 1:正常 2:待审核) |
| AuditRemark | varchar | 500 | 否 | - | 审核备注 |
| LastLoginTime | datetime | - | 否 | - | 最后登录时间 |
| LastLoginIp | varchar | 50 | 否 | - | 最后登录IP |

### 2. sys_log (系统日志表)
**用途**: 记录系统操作日志

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| Id | varchar | - | 是 | - | 日志ID (主键) |
| UserId | varchar | 100 | 否 | - | 用户ID |
| Username | varchar | 50 | 否 | - | 用户名 |
| Operation | varchar | 50 | 是 | - | 操作类型 |
| Method | varchar | 200 | 是 | - | 请求方法 |
| Params | text | - | 否 | - | 请求参数 |
| Time | bigint | - | 是 | - | 执行时长(毫秒) |
| Ip | varchar | 50 | 是 | - | IP地址 |
| Path | varchar | 500 | 是 | - | 请求路径 |
| LogType | varchar | 50 | 否 | System | 日志类型 |
| LogLevel | varchar | 50 | 是 | - | 日志级别 |
| Message | text | - | 是 | - | 日志内容 |
| Exception | text | - | 否 | - | 异常信息 |
| CreateTime | datetime | - | 否 | NOW() | 创建时间 |

---

## 用户管理模块

### 3. users (用户表)
**用途**: 存储微信用户的基本信息

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 用户ID (主键, 自增) |
| OpenId | varchar | 100 | 否 | - | 微信OpenID |
| UnionId | varchar | 100 | 否 | - | 微信UnionID |
| Nickname | varchar | 100 | 否 | - | 微信昵称 |
| Avatar | varchar | 255 | 否 | - | 头像URL |
| EmployeeId | varchar | 50 | 否 | - | 绑定的员工ID |
| LastLogin | datetime | - | 否 | - | 最后登录时间 |

---

## 视频管理模块

### 4. videos (视频内容表)
**用途**: 存储视频内容和相关信息

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 视频ID (主键, 自增) |
| Title | varchar | 255 | 是 | - | 视频标题 |
| Description | text | - | 否 | - | 视频描述 |
| CoverUrl | varchar | 255 | 否 | - | 封面URL |
| VideoUrl | varchar | 255 | 是 | - | 视频URL |
| Duration | int | - | 否 | 0 | 视频时长(秒) |

| RewardAmount | decimal | 10,2 | 否 | 0.00 | 红包金额 |
| Questions | text | - | 否 | - | 问题JSON格式 |
| ViewCount | int | - | 否 | 0 | 观看次数 |
| Status | tinyint | - | 否 | 1 | 状态 (0:下架 1:上架) |

### 5. batches (批次表)
**用途**: 管理视频发布批次

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 批次ID (主键, 自增) |
| Name | varchar | 100 | 是 | - | 批次名称 |
| Description | varchar | 255 | 否 | - | 批次描述 |
| VideoId | int | - | 是 | - | 关联视频ID |
| VideoTitle | varchar | 255 | 是 | - | 视频标题(冗余) |
| VideoDescription | text | - | 否 | - | 视频描述(冗余) |
| VideoCoverUrl | varchar | 255 | 否 | - | 封面URL(冗余) |
| VideoUrl | varchar | 255 | 是 | - | 视频URL(冗余) |
| VideoDuration | int | - | 否 | 0 | 视频时长(冗余) |
| CurrentParticipants | int | - | 否 | 0 | 当前参与人数 |
| RewardAmount | decimal | 10,2 | 否 | 0.00 | 红包金额(冗余) |
| RedPacketAmount | decimal | 10,2 | 否 | 0.00 | 红包金额 |
| Questions | text | - | 否 | - | 问题JSON(冗余) |
| StartTime | datetime | - | 是 | - | 开始时间 |
| EndTime | datetime | - | 是 | - | 结束时间 |

| Status | tinyint | - | 否 | 1 | 状态 (0:下线 1:上线) |

---

## 用户行为模块

### 6. view_records (用户观看记录表)
**用途**: 记录用户观看视频的详细信息

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 记录ID (主键, 自增) |
| UserId | int | - | 是 | - | 用户ID |
| BatchId | int | - | 是 | - | 批次ID |
| ViewDuration | int | - | 否 | 0 | 观看时长(秒) |
| PromotionLink | varchar | 500 | 否 | - | 推广链接 |
| StartTime | datetime | - | 否 | - | 开始时间 |
| EndTime | datetime | - | 否 | - | 结束时间 |
| WatchProgress | decimal | 5,2 | 否 | 0 | 观看进度(百分比) |
| IsCompleted | boolean | - | 否 | false | 是否完成观看 |

### 7. answer_records (用户答题记录表)
**用途**: 记录用户答题信息

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 记录ID (主键, 自增) |
| UserId | int | - | 是 | - | 用户ID |
| BatchId | int | - | 是 | - | 批次ID |
| AnswersJson | text | - | 是 | - | 答题信息JSON格式 |
| TotalQuestions | int | - | 否 | 0 | 总题目数 |
| CorrectAnswers | int | - | 否 | 0 | 正确题目数 |
| AnswerTime | datetime | - | 否 | NOW() | 答题时间 |

---

## 奖励系统模块

### 8. rewards (红包记录表)
**用途**: 管理红包发放记录

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 红包ID (主键, 自增) |
| UserId | int | - | 是 | - | 用户ID |
| BatchId | int | - | 是 | - | 批次ID |
| VideoId | int | - | 是 | - | 视频ID(冗余) |
| Type | varchar | 50 | 否 | - | 奖励类型 |
| Amount | decimal | 10,2 | 否 | 0.00 | 红包金额 |
| Status | tinyint | - | 否 | 0 | 发放状态 (0:待发放 1:发放成功 2:发放失败) |
| TransactionId | varchar | 100 | 否 | - | 微信支付交易号 |
| OutTradeNo | varchar | 100 | 否 | - | 微信支付订单号 |
| PayTime | datetime | - | 否 | - | 发放时间 |
| DistributeTime | datetime | - | 否 | - | 分发时间 |
| ClaimTime | datetime | - | 否 | - | 领取时间 |
| FailReason | varchar | 255 | 否 | - | 失败原因 |
| RetryCount | int | - | 否 | 0 | 重试次数 |

---

## 审核管理模块

### 9. user_audits (用户审核记录表)
**用途**: 记录员工对用户的审核信息

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 审核ID (主键, 自增) |
| user_id | int | - | 是 | - | 被审核用户ID |
| auditor_id | varchar | 50 | 否 | - | 审核人ID（员工ID） |
| batch_id | int | - | 否 | - | 关联批次ID |
| promotion_link_id | int | - | 否 | - | 推广链接ID |
| status | tinyint | - | 否 | 0 | 审核状态 (0:待审核 1:通过 2:拒绝) |
| created_at | datetime | - | 否 | NOW() | 创建时间 |
| updated_at | datetime | - | 否 | NOW() | 更新时间 |

### 10. user_transfers (用户转移记录表)
**用途**: 记录用户在员工间的转移记录

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 转移ID (主键, 自增) |
| UserId | int | - | 是 | - | 被转移用户ID |
| FromEmployeeId | varchar | 50 | 否 | - | 原员工ID |
| ToEmployeeId | varchar | 50 | 否 | - | 新员工ID |
| Reason | varchar | 255 | 否 | - | 转移原因 |
| OperatorId | varchar | 50 | 是 | - | 操作人ID |
| OperatorName | varchar | 50 | 否 | - | 操作人姓名 |
| TransferTime | datetime | - | 否 | NOW() | 转移时间 |

---

## 统计报表模块

### 11. statistics (统计报表表)
**用途**: 存储各类统计数据（旧版本，逐步迁移到用户每日统计表）

### 12. user_daily_statistics (用户每日统计表)
**用途**: 存储用户每日统计数据，一个用户一天一条记录，便于查询和聚合分析
**设计原则**: 以用户为中心的统计设计，支持按时间范围查询和权限控制

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 统计ID (主键, 自增) |
| StatDate | datetime | - | 是 | - | 统计日期 |
| BatchId | int | - | 否 | - | 批次ID |
| AdminId | int | - | 否 | - | 管理员ID |
| EmployeeId | int | - | 否 | - | 员工ID |
| ViewCount | int | - | 否 | 0 | 总观看次数 |
| CompleteViewCount | int | - | 否 | 0 | 完播次数 |
| CompleteRate | decimal | 5,2 | 否 | 0.00 | 完播率 |
| NewUserCount | int | - | 否 | 0 | 新增用户数 |
| CorrectAnswerCount | int | - | 否 | 0 | 答题正确次数 |
| TotalAnswerCount | int | - | 否 | 0 | 答题总次数 |
| CorrectRate | decimal | 5,2 | 否 | 0.00 | 答题正确率 |
| RewardCount | int | - | 否 | 0 | 红包发放次数 |
| UserId | int | - | 否 | - | 用户ID |
| VideoId | int | - | 否 | - | 视频ID |
| StatType | varchar | 50 | 否 | - | 统计类型 |
| StatValue | int | - | 否 | 0 | 统计值 |
| ShareCount | int | - | 否 | 0 | 分享次数 |
| LikeCount | int | - | 否 | 0 | 点赞次数 |
| CommentCount | int | - | 否 | 0 | 评论次数 |
| DownloadCount | int | - | 否 | 0 | 下载次数 |
| Duration | int | - | 否 | 0 | 时长(秒) |
| RewardAmount | decimal | 10,2 | 否 | 0.00 | 红包发放总金额 |

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 统计ID (主键, 自增) |
| UserId | int | - | 是 | - | 用户ID |
| StatDate | datetime | - | 是 | - | 统计日期 |
| EmployeeId | int | - | 否 | - | 负责员工ID (用于权限控制) |
| ViewCount | int | - | 否 | 0 | 观看次数 |
| CompleteViewCount | int | - | 否 | 0 | 完播次数 |
| TotalViewDuration | int | - | 否 | 0 | 总观看时长(秒) |
| AnswerCount | int | - | 否 | 0 | 答题次数 |
| CorrectAnswerCount | int | - | 否 | 0 | 正确答题次数 |
| RewardCount | int | - | 否 | 0 | 获得红包次数 |
| RewardAmount | bigint | - | 否 | 0 | 获得红包总金额(分) |
| ShareCount | int | - | 否 | 0 | 分享次数 |
| LikeCount | int | - | 否 | 0 | 点赞次数 |
| CommentCount | int | - | 否 | 0 | 评论次数 |
| DownloadCount | int | - | 否 | 0 | 下载次数 |

**索引设计**:
- 复合唯一索引: (UserId, StatDate) - 确保一个用户一天只有一条记录
- 普通索引: EmployeeId - 用于权限查询
- 普通索引: StatDate - 用于时间范围查询

**设计特点**:
- 一个用户一天一条统计记录，便于查询和聚合
- 通过EmployeeId实现基于用户归属的权限控制
- 支持多维度统计：观看、答题、红包、互动等
- 计算属性：完播率、答题正确率等可实时计算

---

## 系统配置模块

### 13. system_configs (系统配置表)
**用途**: 存储系统配置参数

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 配置ID (主键, 自增) |
| ConfigKey | varchar | 100 | 是 | - | 配置键 |
| ConfigValue | text | - | 否 | - | 配置值 |
| Description | varchar | 255 | 否 | - | 配置描述 |
| GroupName | varchar | 50 | 否 | - | 配置分组 |
| ConfigType | varchar | 50 | 否 | - | 配置类型 |
| IsEnabled | tinyint | - | 否 | 1 | 是否启用 (0:禁用 1:启用) |

---

## 微信集成模块

### 14. wechat_access_tokens (微信AccessToken记录表)
**用途**: 管理微信API访问令牌

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 令牌ID (主键, 自增) |
| AppId | varchar | 100 | 是 | - | 微信AppID |
| AccessToken | varchar | 512 | 是 | - | AccessToken |
| ExpiresAt | datetime | - | 是 | - | 过期时间 |
| ExpiresIn | int | - | 否 | - | 过期时间（秒） |
| ExpiresTime | datetime | - | 否 | - | 过期时间戳 |
| RefreshToken | varchar | 512 | 否 | - | 刷新Token |
| Scope | varchar | 200 | 否 | - | 授权范围 |
| TokenType | varchar | 50 | 否 | - | Token类型 |
| IsValid | tinyint | - | 否 | 1 | 是否有效 (0:无效 1:有效) |
| IsActive | tinyint | - | 否 | 1 | 是否激活 (0:未激活 1:已激活) |
| IsExpired | tinyint | - | 否 | 0 | 是否过期 (0:未过期 1:已过期) |
| ObtainTime | datetime | - | 否 | NOW() | 获取时间 |

### 15. wechat_payments (微信支付记录表)
**用途**: 记录微信支付交易信息

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | - | 是 | - | 支付ID (主键, 自增) |
| RewardId | int | - | 是 | - | 关联红包记录ID |
| UserId | int | - | 是 | - | 用户ID |
| OpenId | varchar | 100 | 是 | - | 微信OpenID |
| OutTradeNo | varchar | 100 | 是 | - | 商户订单号 |
| OrderNo | varchar | 100 | 是 | - | 订单号 |
| Currency | varchar | 10 | 否 | CNY | 货币类型 |
| Subject | varchar | 255 | 否 | - | 商品主题 |
| Body | varchar | 500 | 否 | - | 商品描述 |
| PaymentType | varchar | 50 | 否 | - | 支付类型 |
| NotifyUrl | varchar | 255 | 否 | - | 通知URL |
| ReturnUrl | varchar | 255 | 否 | - | 返回URL |
| TransactionId | varchar | 100 | 否 | - | 微信支付交易号 |
| Amount | int | - | 否 | 0 | 支付金额(分) |
| Status | tinyint | - | 否 | 0 | 支付状态 (0:待支付 1:支付成功 2:支付失败) |
| Description | varchar | 255 | 否 | - | 支付描述 |
| WechatResponse | text | - | 否 | - | 微信返回信息 |
| PayTime | datetime | - | 否 | - | 支付时间 |
| FailReason | varchar | 255 | 否 | - | 失败原因 |
| RetryCount | int | - | 否 | 0 | 重试次数 |
| RefundNo | varchar | 100 | 否 | - | 退款单号 |
| RefundAmount | int | - | 否 | - | 退款金额(分) |
| RefundReason | varchar | 255 | 否 | - | 退款原因 |
| RefundTime | datetime | - | 否 | - | 退款时间 |

---

## 枚举说明

### 批次状态 (BatchStatusEnum)
- `0` - 未激活
- `1` - 已激活
- `2` - 已出货

### 卡片状态 (CardStatusEnum)
- `0` - 未核销
- `1` - 已核销
- `2` - 锁定中

### 核销状态 (CardUseRecordStatusEnum)
- `0` - 转移卡片
- `1` - 使用卡片

### 卡片流转状态 (CardTransferRecordStatusEnum)
- `0` - 转卡
- `1` - 核销

---

## 数据关系说明

### 主要关联关系
1. **用户关系**: `users.EmployeeId` → `sys_user.UserId`
2. **视频批次**: `batches.VideoId` → `videos.id`
3. **观看记录**: `view_records.UserId` → `users.id`, `view_records.BatchId` → `batches.id`
4. **答题记录**: `answer_records.UserId` → `users.id`, `answer_records.BatchId` → `batches.id`
5. **红包记录**: `rewards.UserId` → `users.id`, `rewards.BatchId` → `batches.id`
6. **支付记录**: `wechat_payments.RewardId` → `rewards.id`
7. **用户审核**: `user_audits.user_id` → `users.id`
8. **用户转移**: `user_transfers.UserId` → `users.id`

### 业务流程关系
1. **视频发布流程**: videos → batches → view_records → answer_records → rewards
2. **用户管理流程**: users → user_audits → user_transfers
3. **支付流程**: rewards → wechat_payments
4. **统计流程**: 各业务表 → statistics

---

## 索引建议

### 主要索引
1. **用户表**: `idx_users_openid`, `idx_users_employee_id`
2. **观看记录**: `idx_view_records_user_batch`, `idx_view_records_batch_id`
3. **答题记录**: `idx_answer_records_user_batch`, `idx_answer_records_batch_id`
4. **红包记录**: `idx_rewards_user_id`, `idx_rewards_batch_id`, `idx_rewards_status`
5. **系统日志**: `idx_sys_log_user_time`, `idx_sys_log_operation`
6. **统计表**: `idx_statistics_date`, `idx_statistics_batch_id`

### 复合索引
1. `idx_view_records_user_batch_time` (UserId, BatchId, StartTime)
2. `idx_rewards_user_status_time` (UserId, Status, CreateTime)
3. `idx_answer_records_batch_time` (BatchId, AnswerTime)

---

## 注意事项

1. **数据一致性**: 批次表中包含视频信息的冗余字段，需要在视频更新时同步更新
2. **软删除**: 建议对重要业务数据实现软删除机制
3. **数据归档**: 对于历史数据建议定期归档，特别是日志表和统计表
4. **权限控制**: 员工只能查看和操作自己绑定的用户数据
5. **金额精度**: 所有金额字段使用decimal(10,2)确保精度
6. **时间字段**: 统一使用datetime类型，建议存储UTC时间

---

*文档生成时间: 2025-07-16*
*版本: v1.0*

