using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.VideoEntity
{
    /// <summary>
    /// 用户转移记录表 - 简化版，只记录员工绑定转移
    /// </summary>
    [Table("user_transfers")]
    public class UserTransfer : BaseEntity_ID
    {
        /// <summary>
        /// 被转移用户ID
        /// </summary>
        [Comment("被转移用户ID")]
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 原员工ID
        /// </summary>
        [MaxLength(50)]
        [Comment("原员工ID")]
        public string? FromEmployeeId { get; set; }

        /// <summary>
        /// 新员工ID
        /// </summary>
        [MaxLength(50)]
        [Comment("新员工ID")]
        public string? ToEmployeeId { get; set; }

        /// <summary>
        /// 转移原因
        /// </summary>
        [MaxLength(255)]
        [Comment("转移原因")]
        public string? Reason { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
        [Comment("操作人ID")]
        public string OperatorId { get; set; } = string.Empty;

        /// <summary>
        /// 操作人姓名
        /// </summary>
        [MaxLength(50)]
        [Comment("操作人姓名")]
        public string? OperatorName { get; set; }

        /// <summary>
        /// 转移时间
        /// </summary>
        [Comment("转移时间")]
        public DateTime TransferTime { get; set; } = DateTime.Now;
    }
}
