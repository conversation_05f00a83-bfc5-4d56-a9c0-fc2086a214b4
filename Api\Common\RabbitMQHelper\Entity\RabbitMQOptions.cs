namespace Common.RabbitMQHelper.Entity;

/// <summary>
/// RabbitMQ 配置选项
/// </summary>
public class RabbitMQOptions
{
    /// <summary>
    /// 主机名
    /// </summary>
    public string HostName { get; set; } = "localhost";

    /// <summary>
    /// 端口
    /// </summary>
    public int Port { get; set; } = 5672;

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; } = "guest";

    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; } = "guest";

    /// <summary>
    /// 虚拟主机
    /// </summary>
    public string VirtualHost { get; set; } = "/";

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// 连接池大小
    /// </summary>
    public int ConnectionPoolSize { get; set; } = 5;
}