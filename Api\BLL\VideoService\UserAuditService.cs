using BLL.SysService;
using Common.Autofac;
using Common.Exceptions;
using DAL.SysDAL;
using DAL.VideoDAL;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Entity.Entitys.SysEntity;
using Entity.Entitys.VideoEntity;
using Microsoft.Extensions.Logging;
using static DAL.SysDAL.SysUserDAL;

namespace BLL.VideoService
{
    /// <summary>
    /// 用户审核业务服务类
    /// </summary>
    [Dependency(DependencyType.Scoped)]
    public class UserAuditService(UserAuditDAL userAuditDAL, UserDAL userDAL, SysUserDAL sysUserDAL, SysLogService logService, SystemConfigService systemConfigService)
    {
        private readonly UserAuditDAL _userAuditDAL = userAuditDAL;
        private readonly UserDAL _userDAL = userDAL;
        private readonly SysUserDAL _sysUserDAL = sysUserDAL;
        private readonly SysLogService _logService = logService;
        private readonly SystemConfigService _systemConfigService = systemConfigService;

        /// <summary>
        /// 创建用户审核记录（用户注册后进入审核队列）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="batchId">批次ID</param>
        /// <param name="promotionLinkId">推广链接ID</param>
        /// <returns>审核记录ID</returns>
        public async Task<string> CreateUserAuditAsync(string userId, int? batchId = null, int? promotionLinkId = null)
        {
            // 验证用户是否存在
            _ = await _userDAL.GetByIdAsync(userId) ?? throw new BusinessException("指定的用户不存在");

            // 检查是否已有待审核记录
            var existingAudit = await _userAuditDAL.GetLatestUserAuditAsync(userId);
            if (existingAudit != null && existingAudit.Status == 0)
                throw new BusinessException("该用户已有待审核记录");

            // 创建用户审核实体
            var userAudit = new UserAudit
            {
                UserId = userId,
                BatchId = batchId,
                PromotionLinkId = promotionLinkId,
                Status = 0, // 待审核
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 添加审核记录
            await _userAuditDAL.AddAsync(userAudit);

            return userAudit.Id;
        }

        /// <summary>
        /// 创建用户审核记录（支持自动审核）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="employeeId">员工ID</param>
        /// <param name="batchId">批次ID</param>
        /// <param name="promotionLinkId">推广链接ID</param>
        /// <returns>审核记录ID</returns>
        public async Task<string> CreateUserAuditWithAutoApprovalAsync(string userId, string employeeId, int? batchId = null, int? promotionLinkId = null)
        {
            // 验证用户是否存在
            _ = await _userDAL.GetByIdAsync(userId) ?? throw new BusinessException("指定的用户不存在");

            // 验证员工是否存在
            _ = await _sysUserDAL.GetFirstAsync(new UserDALQuery { UserId = employeeId }) ?? throw new BusinessException("指定的员工不存在");

            // 检查是否已有待审核记录
            var existingAudit = await _userAuditDAL.GetLatestUserAuditAsync(userId);
            if (existingAudit != null && existingAudit.Status == 0)
                throw new BusinessException("该用户已有待审核记录");

            // 检查是否开启自动审核
            var isAutoAuditEnabled = await _systemConfigService.IsAutoAuditEnabledAsync();

            // 创建用户审核实体
            var userAudit = new UserAudit
            {
                UserId = userId,
                AuditorId = isAutoAuditEnabled ? "SYSTEM" : null, // 自动审核时设为系统
                BatchId = batchId,
                PromotionLinkId = promotionLinkId,
                Status = isAutoAuditEnabled ? (byte)1 : (byte)0, // 自动审核直接通过，否则待审核
                Remark = isAutoAuditEnabled ? "系统自动审核通过" : null,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 添加审核记录
            await _userAuditDAL.AddAsync(userAudit);

            // 记录业务日志
            var operation = isAutoAuditEnabled ? "自动审核通过" : "创建审核记录";
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户审核",
                Operation = operation,
                BusinessObject = "UserAudit",
                ObjectId = userAudit.Id.ToString(),
                DetailedInfo = $"用户 {userId} {operation}，员工ID：{employeeId}",
                AfterData = userAudit,
                Level = LogLevel.Information
            });

            return userAudit.Id;
        }

        /// <summary>
        /// 直接审核用户（不需要预先创建审核记录）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="auditorId">审核员ID（员工ID）</param>
        /// <param name="status">审核状态:1通过,2拒绝</param>
        /// <param name="remark">审核备注</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AuditUserDirectlyAsync(string userId, string auditorId, byte status, string? remark, CurrentUserInfoDto currentUserInfo)
        {
            // 验证用户是否存在
            _ = await _userDAL.GetByIdAsync(userId) ?? throw new BusinessException("指定的用户不存在");

            // 验证审核员是否存在
            _ = await _sysUserDAL.GetFirstAsync(new UserDALQuery { UserId = auditorId }) ?? throw new BusinessException("指定的审核员不存在");

            // 创建审核记录
            var userAudit = new UserAudit
            {
                UserId = userId,
                AuditorId = auditorId,
                Status = status,
                Remark = remark,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 保存审核记录
            await _userAuditDAL.AddAsync(userAudit);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户审核",
                Operation = status == 1 ? "审核通过" : "审核拒绝",
                BusinessObject = "UserAudit",
                ObjectId = userAudit.Id.ToString(),
                DetailedInfo = $"审核员 {auditorId} 审核用户 {userId}，结果：{(status == 1 ? "通过" : "拒绝")}",
                AfterData = userAudit,
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName
            });

            return true;
        }

        /// <summary>
        /// 获取审核记录详情
        /// </summary>
        /// <param name="auditId">审核记录ID</param>
        /// <returns>审核记录响应DTO</returns>
        public async Task<UserAuditResponseDto?> GetUserAuditAsync(string auditId)
        {
            var userAudit = await _userAuditDAL.GetByIdAsync(auditId);
            if (userAudit == null) return null;

            // 获取用户信息
            var user = string.IsNullOrEmpty(userAudit.UserId) ? null : await _userDAL.GetByIdAsync(userAudit.UserId);

            // 获取审核员信息
            SysUser? auditor = null;
            if (!string.IsNullOrEmpty(userAudit.AuditorId))
            {
                auditor = await _sysUserDAL.GetFirstAsync(new UserDALQuery { UserId = userAudit.AuditorId });
            }

            return new UserAuditResponseDto
            {
                Id = userAudit.Id,
                UserId = userAudit.UserId,
                UserNickname = user?.Nickname,
                AuditorId = userAudit.AuditorId,
                AuditorName = auditor?.RealName,
                AuditStatus = userAudit.Status,
                AuditStatusName = userAudit.Status switch
                {
                    0 => "待审核",
                    1 => "审核通过",
                    2 => "审核拒绝",
                    _ => "未知状态"
                },
                AuditRemark = userAudit.Remark,
                CreateTime = userAudit.CreatedAt,
                AuditTime = userAudit.UpdatedAt
            };
        }

        /// <summary>
        /// 分页查询审核记录列表
        /// </summary>
        /// <param name="queryDto">查询条件DTO</param>
        /// <returns>分页结果</returns>
        public async Task<PagedResult<UserAuditResponseDto>> GetUserAuditPagedListAsync(UserAuditQueryDto queryDto)
        {
            var queryable = new UserAuditDAL.Queryable
            {
                UserId = queryDto.UserId,
                AuditorId = queryDto.AuditorId,
                Status = queryDto.AuditStatus,
                StartTime = queryDto.StartTime,
                EndTime = queryDto.EndTime,
                PageIndex = queryDto.PageIndex,
                PageSize = queryDto.PageSize
            };

            var result = await _userAuditDAL.GetPagedListAsync(queryable);

            var responseList = new List<UserAuditResponseDto>();
            foreach (var audit in result.Items ?? [])
            {
                var user = string.IsNullOrEmpty(audit.UserId) ? null : await _userDAL.GetByIdAsync(audit.UserId);
                SysUser? auditor = null;
                if (!string.IsNullOrEmpty(audit.AuditorId))
                {
                    auditor = await _sysUserDAL.GetFirstAsync(new UserDALQuery { UserId = audit.AuditorId });
                }

                responseList.Add(new UserAuditResponseDto
                {
                    Id = audit.Id,
                    UserId = audit.UserId,
                    UserNickname = user?.Nickname,
                    AuditorId = audit.AuditorId,
                    AuditorName = auditor?.RealName,
                    AuditStatus = audit.Status,
                    AuditStatusName = audit.Status switch
                    {
                        0 => "待审核",
                        1 => "审核通过",
                        2 => "审核拒绝",
                        _ => "未知状态"
                    },
                    AuditRemark = audit.Remark,
                    CreateTime = audit.CreatedAt,
                    AuditTime = audit.UpdatedAt
                });
            }

            return new PagedResult<UserAuditResponseDto>
            {
                Items = responseList,
                TotalCount = result.TotalCount,
                PageIndex = result.PageIndex,
                PageSize = result.PageSize
            };
        }

        /// <summary>
        /// 获取待审核用户列表
        /// </summary>
        /// <param name="auditorId">审核员ID（可选，用于筛选特定审核员的待审核列表）</param>
        /// <returns>待审核用户列表</returns>
        public async Task<List<UserAuditResponseDto>> GetPendingAuditsAsync()
        {
            var audits = await _userAuditDAL.GetPendingAuditUsersAsync();

            var responseList = new List<UserAuditResponseDto>();
            foreach (var audit in audits)
            {
                var user = string.IsNullOrEmpty(audit.UserId) ? null : await _userDAL.GetByIdAsync(audit.UserId);

                responseList.Add(new UserAuditResponseDto
                {
                    Id = audit.Id,
                    UserId = audit.UserId,
                    UserNickname = user?.Nickname,
                    AuditStatus = audit.Status,
                    AuditStatusName = "待审核",
                    CreateTime = audit.CreatedAt
                });
            }

            return responseList;
        }

        /// <summary>
        /// 员工审核用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="employeeId">员工ID（从当前登录用户获取）</param>
        /// <param name="status">审核状态:1通过,2拒绝</param>
        /// <param name="remark">审核备注</param>
        /// <param name="currentUserInfo">当前用户信息</param>
        /// <returns>是否成功</returns>
        public async Task<bool> AuditUserByEmployeeAsync(string userId, string employeeId, byte status, string? remark, CurrentUserInfoDto currentUserInfo)
        {
            // 验证用户是否存在
            var user = await _userDAL.GetByIdAsync(userId) ?? throw new BusinessException("指定的用户不存在");

            // 验证员工是否存在
            _ = await _sysUserDAL.GetFirstAsync(new UserDALQuery { UserId = employeeId }) ?? throw new BusinessException("员工不存在");

            // 验证用户是否属于该员工
            if (user.EmployeeId != employeeId)
            {
                throw new BusinessException("您无权审核不属于您的用户");
            }

            // 创建审核记录
            var userAudit = new UserAudit
            {
                UserId = userId,
                AuditorId = employeeId, // 直接使用员工ID字符串
                Status = status,
                Remark = remark,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 保存审核记录
            await _userAuditDAL.AddAsync(userAudit);

            // 记录业务日志
            await _logService.LogBusinessOperationAsync(new BusinessLogDto
            {
                Module = "用户审核",
                Operation = status == 1 ? "审核通过" : "审核拒绝",
                BusinessObject = "UserAudit",
                ObjectId = userAudit.Id.ToString(),
                DetailedInfo = $"员工 {employeeId} 审核用户 {userId}，结果：{(status == 1 ? "通过" : "拒绝")}",
                AfterData = userAudit,
                UserId = currentUserInfo.UserId,
                Username = currentUserInfo.UserName
            });

            return true;
        }

        /// <summary>
        /// 获取审核统计信息
        /// </summary>
        /// <param name="auditorId">审核员ID（员工ID，可选）</param>
        /// <returns>审核统计</returns>
        public async Task<UserAuditStatisticsDto> GetAuditStatisticsAsync(string? auditorId = null)
        {
            var statistics = await _userAuditDAL.GetAuditStatisticsAsync(auditorId);

            return new UserAuditStatisticsDto
            {
                TotalCount = statistics.TotalCount,
                PendingCount = 0, // 需要额外计算
                ApprovedCount = statistics.PassCount,
                RejectedCount = statistics.RejectCount,
                ApprovalRate = statistics.PassRate,
                TodayCount = 0, // 需要额外计算
                TypeStatistics = [] // 需要额外计算
            };
        }

        /// <summary>
        /// 检查用户是否可以观看视频（审核状态检查）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否可以观看</returns>
        public async Task<bool> CanUserWatchVideoAsync(string userId)
        {
            var latestAudit = await _userAuditDAL.GetLatestUserAuditAsync(userId);

            // 没有审核记录或审核通过的用户可以观看
            return latestAudit == null || latestAudit.Status == 1;
        }

        /// <summary>
        /// 获取用户最新审核状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>审核状态：null=无记录，0=待审核，1=通过，2=拒绝</returns>
        public async Task<byte?> GetUserAuditStatusAsync(string userId)
        {
            var latestAudit = await _userAuditDAL.GetLatestUserAuditAsync(userId);
            return latestAudit?.Status;
        }
    }
}
